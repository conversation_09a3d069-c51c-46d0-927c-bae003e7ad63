---
description: Guía para verificar certificaciones de tatuadores en Firestore
---

# Guía para verificar certificaciones de tatuadores

Esta guía explica cómo verificar manualmente las certificaciones de los tatuadores desde la consola de Firebase Firestore.

## Proceso de verificación

1. Accede a la [consola de Firebase](https://console.firebase.google.com/) y selecciona tu proyecto.

2. En el menú lateral, haz clic en "Firestore Database".

3. Navega a la colección `profiles`.

4. Busca el documento correspondiente al usuario (tatuador) que deseas verificar. Puedes usar el ID de usuario (UID) para encontrarlo.

5. Dentro del documento del usuario, navega a la ruta: `profile.professional.requiredCertifications`.

6. Aquí encontrarás documentos para cada tipo de certificación (seremiRegistry, hepatitisB, biosafety) con los siguientes campos:

   - `status`: Estado actual ('pending', 'verified', 'rejected')
   - `documentNumber`: Número del documento o certificado
   - `issueDate`: Fecha de emisión
   - `expiryDate`: Fecha de vencimiento (si aplica)
   - `submittedAt`: Fecha en que el usuario envió la solicitud
   - `verifiedAt`: Fecha de verificación (si ya fue verificado)
   - `rejectionReason`: Motivo de rechazo (si fue rechazado)
   - `adminNotes`: Notas internas (opcional)

## Cómo actualizar el estado de una certificación

Para verificar una certificación:

1. Haz clic en el campo `status` y cambia su valor de 'pending' a 'verified'.
2. Añade un nuevo campo `verifiedAt` con el valor de la fecha actual (puedes usar el selector de fecha de Firestore).
3. Opcionalmente, añade notas en el campo `adminNotes`.

Para rechazar una certificación:

1. Haz clic en el campo `status` y cambia su valor de 'pending' a 'rejected'.
2. Añade un nuevo campo `rejectionReason` con el motivo del rechazo (por ejemplo, "Documento ilegible" o "Certificado vencido").
3. Opcionalmente, añade notas en el campo `adminNotes`.

## Ejemplo de estructura

```
profiles/
  └── [userId]/
      └── profile/
          └── professional/
              └── requiredCertifications/
                  ├── seremiRegistry/
                  │   ├── status: "pending"
                  │   ├── documentNumber: "12345"
                  │   ├── issueDate: "2025-01-15"
                  │   └── submittedAt: "2025-06-02T21:30:00.000Z"
                  │
                  └── hepatitisB/
                      ├── status: "verified"
                      ├── documentNumber: "VHB-789"
                      ├── issueDate: "2024-03-10"
                      ├── expiryDate: "2029-03-10"
                      ├── submittedAt: "2025-05-20T14:25:00.000Z"
                      └── verifiedAt: "2025-05-21T09:15:00.000Z"
```

## Consideraciones importantes

- Revisa las certificaciones diariamente para mantener un buen tiempo de respuesta.
- Verifica que los números de documento y fechas sean coherentes.
- Para certificaciones que requieren renovación (como hepatitisB), asegúrate de que la fecha de vencimiento sea correcta.
- Si rechazas una certificación, proporciona un motivo claro para que el usuario pueda corregir el problema.
