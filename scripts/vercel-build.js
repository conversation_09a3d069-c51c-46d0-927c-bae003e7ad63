const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Iniciando script de compilación personalizado para Vercel...');

// Aplicar parche para Firestore
try {
  console.log('🔧 Aplicando parche para evitar errores de inicialización múltiple de Firestore...');

  const patchPath = path.join(process.cwd(), 'scripts/vercel/patches/firestore-settings-fix.js');
  if (fs.existsSync(patchPath)) {
    require(patchPath);
    console.log('✅ Parche de Firestore aplicado correctamente');
  } else {
    console.log('⚠️ Archivo de parche no encontrado. Creando uno nuevo...');
    const patchDir = path.dirname(patchPath);
    if (!fs.existsSync(patchDir)) {
      fs.mkdirSync(patchDir, { recursive: true });
    }

    const patchContent = `/**
 * Parche para evitar que Firestore se configure múltiples veces durante el build de Vercel
 *
 * Este script modifica temporalmente el comportamiento de la configuración de Firestore
 * para evitar el error: "Firestore has already been initialized. You can only call settings() once"
 */

try {
  // Guardar la función settings original
  const originalFirestoreSettings = require('@google-cloud/firestore').Firestore.prototype.settings;

  // Flag para rastrear si ya se ha llamado a settings
  let settingsCalled = false;

  // Reemplazar la función settings para que solo se ejecute la primera vez
  require('@google-cloud/firestore').Firestore.prototype.settings = function(settings) {
    if (settingsCalled) {
      console.log('⚠️ Intento de llamar a Firestore.settings() múltiples veces - Ignorando llamada adicional');
      return this;
    }

    settingsCalled = true;
    console.log('✅ Configurando Firestore (primera llamada):', settings);
    return originalFirestoreSettings.call(this, settings);
  };

  console.log('🔧 Parche de Firestore settings aplicado correctamente');
} catch (err) {
  console.error('❌ Error aplicando parche de Firestore:', err);
}`;

    fs.writeFileSync(patchPath, patchContent);
    require(patchPath);
    console.log('✅ Parche de Firestore creado y aplicado');
  }
} catch (error) {
  console.error('❌ Error al aplicar parche de Firestore:', error);
}

// Aplicar el parche dedicado para WebAssembly
try {
  console.log('🔧 Aplicando parche específico para WebAssembly...');
  const wasmPatchPath = path.join(process.cwd(), 'scripts/vercel/patches/wasm-hash-fix.js');
  if (fs.existsSync(wasmPatchPath)) {
    require(wasmPatchPath);
    console.log('✅ Parche de WebAssembly cargado');
  } else {
    console.log('⚠️ Archivo de parche de WebAssembly no encontrado');
  }
} catch (error) {
  console.error('❌ Error al aplicar parche de WebAssembly:', error);
}

// Configurar variables de entorno para deshabilitar WebAssembly
process.env.NEXT_SKIP_WASM = '1';
process.env.NEXT_DISABLE_WASM = '1';
process.env.NEXT_PUBLIC_SKIP_WASM = '1';
process.env.NEXT_WEBPACK_DISABLE_WASM = '1';
process.env.NEXT_DISABLE_WEBPACK_WASM = '1';
process.env.NEXT_DISABLE_OPTIMIZATION = '1';
process.env.NEXT_DISABLE_MINIFICATION = '1';
process.env.NODE_OPTIONS = '--max-old-space-size=4096 --no-warnings';

// Instalar dependencias críticas usando el script dedicado
console.log('📦 Instalando dependencias críticas...');
try {
  const installDepsPath = path.join(process.cwd(), 'scripts/vercel/install-deps.js');
  if (fs.existsSync(installDepsPath)) {
    require(installDepsPath);
    console.log('✅ Dependencias críticas instaladas');
  } else {
    console.log('⚠️ Script de instalación de dependencias no encontrado, instalando manualmente...');
    execSync('npm install --no-save --legacy-peer-deps webpack@5 null-loader file-loader events stream-browserify', { stdio: 'inherit' });
    console.log('✅ Dependencias críticas instaladas manualmente');
  }
} catch (error) {
  console.error('❌ Error al instalar dependencias críticas:', error);
}

// Backup de next.config.js y crear configuración temporal
console.log('📝 Creando configuración temporal para la compilación...');
const originalConfigPath = path.join(process.cwd(), 'next.config.js');
const backupConfigPath = path.join(process.cwd(), 'next.config.backup.js');
const vercelConfigPath = path.join(process.cwd(), 'next.config.vercel.js');

try {
  // Hacer una copia de seguridad del archivo original
  if (fs.existsSync(originalConfigPath)) {
    fs.copyFileSync(originalConfigPath, backupConfigPath);
    console.log('✅ Copia de seguridad de next.config.js creada');
  }

  // Usar nuestra configuración especial para Vercel
  if (fs.existsSync(vercelConfigPath)) {
    fs.copyFileSync(vercelConfigPath, originalConfigPath);
    console.log('✅ Usando configuración especial para Vercel');
  } else {
    console.log('✅ Usando configuración existente (ya optimizada)');
  }
} catch (error) {
  console.error('❌ Error al crear copia de seguridad:', error);
}

// Ejecutar la compilación de Next.js con una versión simplificada
console.log('🏗️ Ejecutando compilación de Next.js...');
try {
  // Usar un enfoque de compilación más básico
  execSync(
    'NODE_ENV=production next build --no-lint',
    {
      stdio: 'inherit',
      env: {
        ...process.env,
        NEXT_SKIP_WASM: '1',
        NEXT_DISABLE_WASM: '1',
        NEXT_DISABLE_WEBPACK_WASM: '1',
        NEXT_DISABLE_OPTIMIZATION: '1',
        NEXT_DISABLE_MINIFICATION: '1',
        NEXT_DISABLE_SWC: '1'
      }
    }
  );
  console.log('✅ Compilación completada con éxito');
} catch (error) {
  console.error('❌ Error en la compilación:', error);

  // Intentar con una configuración aún más básica
  console.log('🔄 Intentando compilación con configuración mínima...');
  try {
    // Usar la configuración simplificada que hemos creado
    const simpleConfigPath = path.join(process.cwd(), 'next.config.simple.js');
    if (fs.existsSync(simpleConfigPath)) {
      fs.copyFileSync(simpleConfigPath, originalConfigPath);
      console.log('✅ Usando configuración simplificada existente');
    } else {
      console.log('⚠️ No se encontró la configuración simplificada, creando una básica');
      const minConfig = `
      /** @type {import('next').NextConfig} */
      const nextConfig = {
        reactStrictMode: true,
        swcMinify: false,
        typescript: { ignoreBuildErrors: true },
        eslint: { ignoreDuringBuilds: true },
        experimental: { esmExternals: true },
        webpack: (config) => {
          // Deshabilitar WebAssembly
          config.experiments = { asyncWebAssembly: false, syncWebAssembly: false };

          // Ignorar archivos .wasm
          config.module.rules.push({ test: /\\.wasm$/, type: "javascript/auto", use: [] });

          // Deshabilitar optimizaciones
          config.optimization = { minimize: false };

          return config;
        },
        output: 'standalone'
      }
      module.exports = nextConfig;
      `;
      fs.writeFileSync(originalConfigPath, minConfig);
    }

    console.log('✅ Configuración mínima creada');

    // Intentar compilar con la configuración mínima
    execSync(
      'NODE_ENV=production NEXT_DISABLE_WEBPACK_WASM=1 NEXT_DISABLE_OPTIMIZATION=1 next build --no-lint',
      { stdio: 'inherit' }
    );
    console.log('✅ Compilación con configuración mínima completada');

  } catch (minError) {
    console.error('❌ Error en la compilación con configuración mínima:', minError);

    // Restaurar configuración original antes de salir
    try {
      if (fs.existsSync(backupConfigPath)) {
        fs.copyFileSync(backupConfigPath, originalConfigPath);
        console.log('🔄 Configuración original restaurada');
      }
    } catch (restoreError) {
      console.error('❌ Error al restaurar configuración original:', restoreError);
    }

    process.exit(1);
  }
}

// Restaurar configuración original si existe un backup
try {
  if (fs.existsSync(backupConfigPath)) {
    fs.copyFileSync(backupConfigPath, originalConfigPath);
    fs.unlinkSync(backupConfigPath);
    console.log('🔄 Configuración original restaurada');
  }
} catch (error) {
  console.error('❌ Error al restaurar configuración original:', error);
}

// Crear archivos de manifiesto faltantes para el grupo de rutas (app)
console.log('🔧 Creando archivos de manifiesto faltantes para el grupo de rutas (app)...');

const appRoutesDir = path.join(process.cwd(), '.next/server/app/(app)');
const standaloneAppRoutesDir = path.join(process.cwd(), '.next/standalone/.next/server/app/(app)');

// Crear directorios si no existen
try {
  if (!fs.existsSync(appRoutesDir)) {
    fs.mkdirSync(appRoutesDir, { recursive: true });
    console.log(`✅ Directorio creado: ${appRoutesDir}`);
  }

  if (!fs.existsSync(standaloneAppRoutesDir)) {
    fs.mkdirSync(standaloneAppRoutesDir, { recursive: true });
    console.log(`✅ Directorio creado: ${standaloneAppRoutesDir}`);
  }
} catch (dirError) {
  console.error('❌ Error creando directorios:', dirError);
}

// Crear archivos de manifiesto vacíos
const filesToCreate = [
  { path: path.join(appRoutesDir, 'page.js'), content: 'export default function Page() { return null }' },
  { path: path.join(appRoutesDir, 'page_client-reference-manifest.js'), content: 'export const clientReferenceManifest = {}' },
  { path: path.join(appRoutesDir, 'layout.js'), content: 'export default function Layout({ children }) { return children }' },
  { path: path.join(appRoutesDir, 'layout_client-reference-manifest.js'), content: 'export const clientReferenceManifest = {}' }
];

// Crear archivos en el directorio .next/server
filesToCreate.forEach(file => {
  try {
    fs.writeFileSync(file.path, file.content);
    console.log(`✅ Archivo creado: ${file.path}`);

    // Copiar al directorio standalone si existe
    const standaloneFilePath = file.path.replace('.next/server', '.next/standalone/.next/server');
    if (fs.existsSync(path.dirname(standaloneFilePath))) {
      fs.writeFileSync(standaloneFilePath, file.content);
      console.log(`✅ Archivo copiado a standalone: ${standaloneFilePath}`);
    }
  } catch (fileError) {
    console.error(`❌ Error creando archivo ${file.path}:`, fileError);
  }
});

// Ejecutar script de corrección adicional
console.log('🔧 Ejecutando script de corrección adicional...');
try {
  require('./fix-standalone');
} catch (fixError) {
  console.error('❌ Error ejecutando script de corrección:', fixError);
}

console.log('✨ Proceso de compilación completado');