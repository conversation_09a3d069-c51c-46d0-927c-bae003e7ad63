{"name": "tatu", "version": "0.1.0", "private": true, "type": "commonjs", "scripts": {"dev": "next dev -p 3000", "build": "next build && tsc -p tsconfig.worker.json", "build:vercel": "node scripts/vercel-build.js", "post-build": "node scripts/fix-standalone.js", "start": "next start", "clean": "rm -rf .next && rm -rf .local-chromium && rm -rf .wwebjs_auth && rm -rf dist && rm -rf whatsapp-data", "dev:server": "node server.js", "dev:tunnel": "node dev-server.js", "tunnel": "tsx scripts/tunnel.ts", "dev:with-tunnel": "sh scripts/tunnel.sh", "export": "node scripts/static-export.js", "deploy:static": "node scripts/static-export.js && cd out && vercel --prebuilt"}, "dependencies": {"@clerk/nextjs": "^6.9.6", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^9.0.0", "@dnd-kit/utilities": "^3.2.2", "@firebase/app": "^0.10.16", "@firebase/auth": "^1.8.1", "@firebase/firestore": "^4.7.5", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@mercadopago/sdk-react": "^0.0.19", "@novu/node": "^2.6.6", "@novu/notification-center": "^2.0.0", "@novu/react": "^3.0.0", "@pedroslopez/moduleraid": "^5.0.2", "@phosphor-icons/react": "^2.1.7", "@puppeteer/browsers": "^2.6.1", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@react-pdf/renderer": "^4.1.6", "@tailwindcss/forms": "^0.5.10", "@tiptap/extension-bullet-list": "^2.10.3", "@tiptap/extension-heading": "^2.10.3", "@tiptap/extension-horizontal-rule": "^2.10.3", "@tiptap/extension-list-item": "^2.10.3", "@tiptap/extension-ordered-list": "^2.10.3", "@tiptap/extension-text-align": "^2.10.3", "@tiptap/pm": "^2.10.3", "@tiptap/react": "^2.10.3", "@tiptap/starter-kit": "^2.10.3", "@types/aos": "^3.0.7", "@types/express": "^5.0.0", "@types/leaflet": "^1.9.14", "@types/node": "^20.17.11", "@types/nodemailer": "^6.4.17", "@types/react": "^18.2.61", "@types/react-dom": "^18.2.19", "@types/react-image-crop": "^9.0.2", "@types/ws": "^8.5.13", "aos": "^2.3.4", "apify-client": "^2.12.5", "autoprefixer": "^10.4.17", "axios": "^1.8.4", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.1", "cloudinary": "^2.5.1", "clsx": "^2.1.1", "cross-env": "^7.0.3", "crypto-browserify": "^3.12.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.4.7", "express": "^4.21.2", "extract-zip": "2.0.1", "ffmpeg-static": "^5.2.0", "firebase": "^11.1.0", "firebase-admin": "^12.0.0", "fluent-ffmpeg": "^2.1.3", "form-data": "^4.0.2", "framer-motion": "^12.5.0", "get-stream": "5.2.0", "http-proxy-middleware": "^3.0.3", "https-browserify": "^1.0.0", "leaflet": "^1.9.4", "localtunnel": "^2.0.2", "lucide-react": "^0.462.0", "mercadopago": "^2.0.15", "next": "14.1.0", "next-plausible": "^3.12.4", "next-themes": "^0.4.4", "node-fetch": "^3.3.2", "node-webpmux": "^3.2.0", "nodemailer": "^6.9.16", "null-loader": "^4.0.1", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "pdfjs-dist": "^4.9.155", "postcss": "^8.4.35", "process": "^0.11.10", "puppeteer": "^19.7.0", "qrcode": "^1.5.4", "raw-loader": "^4.0.2", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-colorful": "^5.6.1", "react-day-picker": "^9.5.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-icons": "^5.4.0", "react-image-crop": "^11.0.7", "recharts": "^2.13.3", "sonner": "^1.7.2", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "tailwind-merge": "^2.2.0", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.1", "typescript": "^5.3.3", "typewriter-effect": "^2.21.0", "webpack": "^5.90.3", "whatsapp-web.js": "^1.26.0", "ws": "^8.18.0"}, "devDependencies": {"@shadcn/ui": "^0.0.4", "@types/uuid": "^10.0.0", "eslint": "^8.57.0", "eslint-config-next": "14.1.0", "file-loader": "^6.2.0", "firebase-frameworks": "^0.11.6", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.19.2", "uuid": "^9.0.1", "webpack-cli": "^5.1.4"}}