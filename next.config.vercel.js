/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    domains: [
      'firebasestorage.googleapis.com',
      'lh3.googleusercontent.com',
      'res.cloudinary.com',
      'platform-lookaside.fbsbx.com',
      'scontent.xx.fbcdn.net',
      'instagram.fscl11-1.fna.fbcdn.net',
      'instagram.fgye1-2.fna.fbcdn.net',
      'instagram.fscl19-1.fna.fbcdn.net',
      'instagram.fgye10-1.fna.fbcdn.net',
      'instagram.fgye1-1.fna.fbcdn.net',
      'instagram.fscl11-2.fna.fbcdn.net',
      'instagram.fgye10-2.fna.fbcdn.net',
      'instagram.fscl19-2.fna.fbcdn.net',
      'cdn.discordapp.com',
      'i.imgur.com',
      'i.ibb.co',
      'scontent-iad3-1.cdninstagram.com',
      'scontent-iad3-2.cdninstagram.com',
      'scontent-mia3-1.cdninstagram.com',
      'scontent-mia3-2.cdninstagram.com',
      'scontent.cdninstagram.com',
      'instagram.com',
      'www.instagram.com',
      'graph.instagram.com',
      'scontent-atl3-1.cdninstagram.com',
      'scontent-atl3-2.cdninstagram.com',
    ],
  },
  webpack: (config, { isServer }) => {
    // Resolver para manejar el problema de undici en instagramdp
    config.resolve.alias = {
      ...config.resolve.alias,
      // Evitar que se use undici directamente
      'undici': false
    };

    // Agregar un fallback para los módulos problemáticos
    config.resolve.fallback = {
      ...config.resolve.fallback,
      'undici': false,
      'fs': false,
      'net': false,
      'tls': false,
      'crypto': require.resolve('crypto-browserify'),
      'stream': require.resolve('stream-browserify'),
      'http': require.resolve('stream-http'),
      'https': require.resolve('https-browserify'),
      'zlib': require.resolve('browserify-zlib'),
      'path': require.resolve('path-browserify'),
      'os': require.resolve('os-browserify/browser'),
      'buffer': require.resolve('buffer/'),
      'process': require.resolve('process/browser'),
    };

    return config;
  },
  experimental: {
    disableOptimizedLoading: true,
  },
};

module.exports = nextConfig;
