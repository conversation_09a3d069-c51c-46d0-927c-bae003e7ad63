# Branding and Content
- Rename 'Pro' plan to 'Artista del Tatuaje', '<PERSON><PERSON><PERSON>' plan to 'Tatuador Independiente', with pricing at $49.990 monthly or annual with 20% discount ($479.904).
- Change all instances of 'Tatu Studio' to 'Tatu.Ink' throughout the site, including welcome messages.
- Focus on tattoo artists rather than tattoo studios in all messaging, starting with the Hero section.
- Add 'Estudio' plan with dynamic pricing based on number of accounts (minimum 2), with discounts of 2% for 2-4 accounts, 5% for 5-9 accounts, and 10% for 10+ accounts.
- Remove statistical claims (like '1,000+ Artistas del tatuaje') from the landing page.
- Highlight that users can connect messaging channels using 'Unipile', including Instagram, Messenger, WhatsApp, and Telegram.

# Design Preferences
- Use primarily black and white color scheme with purple and blue only as accent colors.
- Prefer modern design styles for website components, particularly for FAQ, Hero, PlanPromoModal, and settings/billing sections.
- Use video backgrounds in Hero section with semi-transparent black overlay, visible on mobile devices.
- Use fixed dimensions for UI elements with dynamic content to prevent layout shifts.
- Prefer HTML/CSS-based UI representations over traditional images for the landing page.
- Enhance visual design for popups/modals while maintaining functionality.
- Ensure mobile-responsive table designs that don't get compressed on small screens.
- Fix spacing in app layout so main content extends fully without extra margins.
- Use black and gray colors for selection/hover states in the Sidebar instead of blue.

# UI Components and Interactions
- Make modal components dismissible by swiping down, tapping outside, or clicking the trigger button again.
- Open image and video previews in modals above all interface elements rather than in new tabs.
- Use ModalPortal to make popups appear above all elements (including Sidebar and Topbar), with 'fixed inset-0' for overlays.
- Make chat interfaces show 'Mensaje entrante!' indicator when a new message arrives but conversation isn't open.
- Disable automatic scroll-to-bottom behavior in chat interfaces to allow scrolling through message history.
- In TodaySchedule component, show payment status of appointments instead of chat buttons, clearly specifying which payments (reservation vs session) are paid/unpaid.
- Display actual session descriptions provided by tattoo artists instead of generic 'tattoo' text.
- Make session items clickable to open payment status modals above all elements, allowing users to mark reservations or sessions as paid.
- For payment status buttons, when marking as paid, the 'Mark as paid' button should disappear and be replaced by 'Mark as pending' button, and all status changes should reflect in real-time without page refresh.
- Tutorial banner should appear once per session and respect user preferences for future appearances.
- In client lists, prevent accidental navigation to connected social media platforms when users click on them, and ensure action buttons (calendar, dollar) have clear functionality or should be removed if non-functional.

# Technical Implementation
- Add CSS directly in page files using HTML style tags rather than external CSS files.
- Use page-specific CSS files with separate files for desktop and mobile views.
- Add images and videos as exceptions in middleware to prevent processing as tattoo artist pages.
- Project theme only reads image paths directly from /public directory without additional path segments.
- Use actual logo images with transparent PNGs with white lines rather than SVG placeholders.
- Embed external forms directly rather than using popup integrations (https://31de5h42.forms.app/plan-para-estudios-de-tatuajes).
- Cannot use Playwright to view project locally, can only edit code and deploy using git push.

# Messaging and API Integration
- Standardize on Unipile API and remove other messaging platform APIs (Meta, Sinch, Superchat).
- Change message sending from Sinch API to Unipile API with multipart/form-data format for text, account_id and attachments.
- Migrate AI assistant and automations from Sinch to Unipile API, maintaining conversation context and automation conditions.
- Retrieve user profile pictures from Unipile API, convert from binary blob to image, store in Cloudinary for chat profile pictures.
- For group chats, obtain profile pictures using chat_id instead of attendant_id.
- When processing Unipile webhooks, don't fetch profile pictures when sender is the tattoo artist.
- RTDB security rules allow full read/write access to conversations, profile pictures, and related paths.
- For Unipile integration, when accounts arrive with ERROR status, implement reconnection flow using /api/v1/hosted/accounts/link with type 'reconnect', handle RECONNECTED webhook status, and add reconnect capability to UnipileIntegration component.
- When deleting Unipile accounts in UnipileIntegration component, all conversations associated with that account_id should be actually deleted from the database, not just the account connection.

# Billing and Subscriptions
- For subscription reactivation: restore normal billing cycle without requiring immediate payment, just reactivate the existing payment flow.
- Billing section should show pricing tabs only for free users, but for paid users show detailed subscription table with current plan (Artista del Tatuaje Mensual/Anual or new 'Estudio' plan), next payment date, suspend/reactivate buttons, and access expiration messaging.
- For Reveniu subscription reactivation, use endpoint /v1/subscriptions/reactivate/{id} not /api/v1/subscriptions/{id}/enablerenew/ which returns 404.
- Reveniu reactivate subscription endpoint is /v1/subscriptions/reactivate/{subscription_id} and returns JSON with 'result': true on success.
- User prefers redirecting to /payment page instead of popup modals for payment flow, as there was already a working process for this.
- Payment URL should include query parameters to preserve user selections: /payment?billingCycle=monthly&planType=regular&currency=CLP for monthly plans, with billingCycle changing to 'yearly' for annual plans.

# Firebase y Estructura de Datos
- Los datos del perfil de usuario deben guardarse en la colección `users` y no en `profiles`. Esto incluye información profesional, certificaciones y ubicación del estudio.
- La estructura correcta para guardar información profesional es `users/{userId}/profile.professional` (no `profile.profile.professional`).
- La estructura correcta para guardar información del estudio es `users/{userId}/profile.contact.studio` (no `profile.profile.contact.studio`).
- Cuando se detecten problemas de datos no visibles en componentes, verificar:
  1. La colección correcta donde se guardan los datos (`users` vs `profiles`)
  2. Las rutas exactas dentro del documento (usando console.log para inspeccionar la estructura)
  3. La consistencia entre las funciones que guardan y las que leen los datos
- El componente `ProfileContext.tsx` debe usar la misma colección tanto para leer como para escribir datos (usar `users` en ambos casos).
- Para depurar problemas de estructura de datos, añadir logs detallados en cada nivel de la estructura para identificar exactamente dónde están los datos.