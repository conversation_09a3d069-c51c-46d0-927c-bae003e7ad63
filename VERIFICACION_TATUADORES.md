# Guía de Verificación de Tatuadores en Tatu App

Esta guía explica detalladamente cómo verificar las certificaciones profesionales de los tatuadores en la plataforma Tatu, directamente desde la consola de Firebase Firestore.

## Acceso a Firebase

1. Accede a la [consola de Firebase](https://console.firebase.google.com/)
2. Selecciona el proyecto "Tatu"
3. En el menú lateral izquierdo, haz clic en "Firestore Database"

## Estructura de la Base de Datos

La información de las certificaciones de los tatuadores se encuentra en la siguiente ruta:

```
profiles/[ID_DEL_USUARIO]/profile/professional/requiredCertifications
```

Donde:
- `profiles` es la colección principal
- `[ID_DEL_USUARIO]` es el identificador único del tatuador
- `profile` es un objeto que contiene toda la información del perfil
- `professional` contiene la información profesional
- `requiredCertifications` contiene las certificaciones requeridas

## Tipos de Certificaciones

Existen tres tipos principales de certificaciones que debes verificar:

1. **Registro SEREMI de Salud** (`seremiRegistry`): Inscripción en el Libro de Registro de Tatuadores
2. **Vacuna Hepatitis B** (`hepatitisB`): Esquema completo de 3 dosis, requiere refuerzo cada 5 años
3. **Certificado de Bioseguridad** (`biosafety`): Manejo de residuos peligrosos y medidas de bioseguridad

## Estados de Verificación

Cada certificación puede tener uno de estos tres estados:

- **Esperando Verificación** (`status: "pending"`): El tatuador ha enviado la información pero aún no ha sido revisada
- **Verificado** (`status: "verified"`): La certificación ha sido revisada y aprobada
- **Rechazado** (`status: "rejected"`): La certificación ha sido revisada y rechazada

## Proceso de Verificación Paso a Paso

### 1. Encontrar Tatuadores Pendientes de Verificación

Para encontrar tatuadores con certificaciones pendientes, puedes:

- Navegar manualmente por la colección `profiles`
- Utilizar la función de búsqueda de Firestore para buscar documentos con `status: "pending"`

### 2. Revisar la Información de la Certificación

Para cada certificación pendiente, revisa los siguientes campos:

- `documentNumber`: Número del documento o certificado
- `issueDate`: Fecha de emisión
- `expiryDate`: Fecha de vencimiento (si aplica)
- `submittedAt`: Fecha en que el usuario envió la solicitud

### 3. Aprobar una Certificación

Si la información es correcta y deseas aprobar la certificación:

1. Haz clic en el campo `status` y cambia su valor de `"pending"` a `"verified"`
2. Añade un nuevo campo `verifiedAt` con el valor de la fecha actual (puedes usar el selector de fecha de Firestore)
3. Opcionalmente, añade notas en el campo `adminNotes` (por ejemplo, "Certificado verificado correctamente")

### 4. Rechazar una Certificación

Si la información es incorrecta o incompleta:

1. Haz clic en el campo `status` y cambia su valor de `"pending"` a `"rejected"`
2. Añade un campo `rejectionReason` con el motivo específico del rechazo (por ejemplo, "Documento ilegible" o "Certificado vencido")
3. Opcionalmente, añade notas internas en el campo `adminNotes`

## Ejemplos Visuales

### Ejemplo 1: Certificación Pendiente

```json
{
  "status": "pending",
  "documentNumber": "12345",
  "issueDate": "2025-01-15",
  "submittedAt": "2025-06-02T21:30:00.000Z"
}
```

### Ejemplo 2: Certificación Verificada

```json
{
  "status": "verified",
  "documentNumber": "VHB-789",
  "issueDate": "2024-03-10",
  "expiryDate": "2029-03-10",
  "submittedAt": "2025-05-20T14:25:00.000Z",
  "verifiedAt": "2025-05-21T09:15:00.000Z",
  "adminNotes": "Certificado completo y válido"
}
```

### Ejemplo 3: Certificación Rechazada

```json
{
  "status": "rejected",
  "documentNumber": "BIO-456",
  "issueDate": "2023-11-05",
  "submittedAt": "2025-06-01T16:45:00.000Z",
  "rejectionReason": "Certificado vencido, se requiere renovación",
  "adminNotes": "Contactar al usuario para informarle"
}
```

## Consideraciones Importantes

1. **Frecuencia de verificación**: Se recomienda revisar las certificaciones pendientes al menos una vez al día para mantener una buena experiencia de usuario.

2. **Validación de fechas**: 
   - Para la vacuna de Hepatitis B, verifica que la fecha de vencimiento sea 5 años después de la fecha de emisión
   - Para el certificado de Bioseguridad, verifica que la fecha de vencimiento sea 2 años después de la fecha de emisión

3. **Documentación**: Si es posible, guarda capturas de pantalla o registros de las verificaciones realizadas para referencia futura.

4. **Comunicación**: Aunque el sistema notifica automáticamente a los usuarios cuando su certificación cambia de estado, es recomendable tener un canal de comunicación adicional para resolver dudas.

## Solución de Problemas Comunes

- **No encuentro la colección `profiles`**: Asegúrate de estar en la base de datos correcta y tener los permisos adecuados.
- **No puedo editar los campos**: Verifica que tienes permisos de escritura en la base de datos.
- **El campo no se actualiza**: Intenta refrescar la página o salir y volver a entrar a la consola de Firebase.

## Contacto para Soporte

Si encuentras algún problema durante el proceso de verificación, contacta al administrador del sistema:

- Correo: [<EMAIL>]
- Teléfono: [+56 9 XXXX XXXX]

---

Documento actualizado: 2 de junio de 2025
