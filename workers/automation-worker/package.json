{"name": "automation-worker", "version": "0.1.0", "private": true, "scripts": {"deploy": "node -e \"require('dotenv').config({path: '../../.env.local'})\" && wrangler deploy", "dev": "wrangler dev --test-scheduled --env development", "start": "wrangler dev --test-scheduled --env development"}, "dependencies": {"@cloudflare/workers-types": "^4.20240208.0", "date-fns": "^3.3.1", "date-fns-tz": "^3.2.0", "dotenv": "^16.4.4", "firebase-admin": "^12.0.0", "jose": "^5.9.6"}, "devDependencies": {"typescript": "^5.3.3", "wrangler": "^4.19.1"}}