// Archivo temporal para despliegue en Cloudflare Workers
// Este archivo utiliza el formato ES Modules que es compatible con Cloudflare Workers

// Importamos el worker original
import * as originalWorker from './worker.ts';

// Exportamos las funciones en el formato que espera Cloudflare Workers
export default {
  // Función fetch para manejar solicitudes HTTP
  async fetch(request, env, ctx) {
    try {
      // Redirigimos a la implementación original
      return await originalWorker.default.fetch(request, env, ctx);
    } catch (error) {
      console.error('Error en fetch:', error);
      return new Response('Error interno del servidor', { status: 500 });
    }
  },

  // Función scheduled para manejar eventos programados
  async scheduled(event, env, ctx) {
    try {
      // Redirigimos a la implementación original
      return await originalWorker.default.scheduled(event, env, ctx);
    } catch (error) {
      console.error('Error en scheduled:', error);
      return new Response('Error en el procesamiento programado', { status: 500 });
    }
  }
};
