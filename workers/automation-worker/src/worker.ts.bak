import { initializeApp, cert, getApps, ServiceAccount } from 'firebase-admin/app';
import { getDatabase, Database, DataSnapshot, Reference } from 'firebase-admin/database';
import { getFirestore } from 'firebase-admin/firestore'
import { addDays, addHours, addMinutes, addMonths, addWeeks, isBefore, isAfter, formatISO, isSameMinute, format } from 'date-fns'
import { toZonedTime } from 'date-fns-tz'
import { automationService } from '@/lib/services/AutomationService'
import { SignJWT, importPKCS8 } from 'jose';
import { createPrivateKey } from 'crypto';
import { es } from 'date-fns/locale'

// Tipos de Cloudflare Worker
declare global {
  interface ExecutionContext {
    waitUntil(promise: Promise<any>): void
  }
}

interface ScheduledEvent {
  cron: string
  scheduledTime: number
}

interface Env {
  FIREBASE_CONFIG: string
  FIREBASE_DATABASE_URL: string
  FIREBASE_SERVICE_ACCOUNT: string
  ENVIRONMENT: string
  NEXT_PUBLIC_UNIPILE_API_KEY?: string
  NEXT_PUBLIC_UNIPILE_API_URL?: string
}

interface Appointment {
  id: string
  date: string
  clientName: string
  clientId: string
  artistId: string
  depositPaid: boolean
  lastProcessed?: string
  [key: string]: any
}

interface MetaConnection {
  pageId: string
  pageAccessToken: string
  userId: string
}

interface FirebaseConfig {
  projectId: string;
  databaseURL: string;
  serviceAccount: any;
  apiKey: string;
}

interface AuthResponse {
  idToken: string;
  email: string;
  refreshToken: string;
  expiresIn: string;
  localId: string;
}

interface GoogleTokenResponse {
  access_token: string;
  expires_in: number;
  token_type: string;
}

interface FirestoreDocument {
  name: string;
  fields: {
    [key: string]: any;
  };
  createTime: string;
  updateTime: string;
}

interface FirestoreResponse {
  documents: FirestoreDocument[];
}

interface FirestoreQueryResponse {
  document: {
    name: string;
    fields: {
      date: { timestampValue: string };
      clientId: { stringValue: string };
      clientName: { stringValue: string };
      artistId: { stringValue: string };
      depositPaid: { booleanValue: boolean };
      lastProcessed?: { timestampValue: string };
    };
  };
}

interface FirestoreClientQueryResponse {
  document?: {
    name: string;
    fields: {
      name?: { stringValue: string };
      conversationId?: { stringValue: string };
    };
  };
}

interface FirestoreClientResponse {
  fields: {
    name?: { stringValue: string };
    conversationId?: { stringValue: string };
  };
}

interface MetaQueryResponse {
  document?: {
    fields: {
      pageId: { stringValue: string };
      pageAccessToken: { stringValue: string };
    };
  };
}

interface MetaMessageResponse {
  message_id: string;
  recipient_id: string;
}

interface TimingConfig {
  type: 'before' | 'after' | 'during' | 'specific'
  value: number
  unit: 'minutes' | 'hours' | 'days' | 'weeks' | 'months'
  specificDate?: string
  specificTime?: string
}

interface CustomCondition {
  type: 'sessions_completed' | 'time_since_last_appointment' | 'total_spent' | 'pending_sessions' | 'custom'
  value: number
  operator: '>' | '<' | '=' | '>=' | '<='
}

interface AutomationRTDB {
  id: string
  userId: string
  name: string
  description: string
  phaseId: string
  trigger: string
  timing: TimingConfig
  messageTemplate: string
  isActive: boolean
  variables: string[]
  customConditions?: CustomCondition[]
  createdAt: number
  updatedAt: number
  channel: string
  lastProcessed?: number
}

// Inicializar Firebase Admin
let app: any
let db: any
let firestore: any
let rtdb: any

// Agregar constante para zona horaria de Chile
const CHILE_TIMEZONE = 'America/Santiago';

async function generateFirebaseToken(serviceAccount: any): Promise<string> {
  try {
    const now = Math.floor(Date.now() / 1000);

    // Convertir la clave privada a formato PEM
    const privateKeyPEM = serviceAccount.private_key
      .replace(/\\n/g, '\n')
      .trim();

    // Usar importPKCS8 en lugar de createPrivateKey
    const privateKey = await importPKCS8(privateKeyPEM, 'RS256');

    const token = await new SignJWT({
      iss: serviceAccount.client_email,
      sub: serviceAccount.client_email,
      aud: 'https://identitytoolkit.googleapis.com/google.identity.identitytoolkit.v1.IdentityToolkit',
      iat: now,
      exp: now + 3600,
      uid: 'automation-worker'
    })
    .setProtectedHeader({ alg: 'RS256' })
    .sign(privateKey);

    return token;
  } catch (error) {
    console.error('Error generating Firebase token:', error);
    throw error;
  }
}

async function generateGoogleAccessToken(serviceAccount: any): Promise<string> {
  try {
    const now = Math.floor(Date.now() / 1000);

    // Convertir la clave privada a formato PEM
    const privateKeyPEM = serviceAccount.private_key
      .replace(/\\n/g, '\n')
      .trim();

    // Crear el JWT usando jose
    const privateKey = await importPKCS8(privateKeyPEM, 'RS256');

    const jwt = await new SignJWT({
      scope: [
        'https://www.googleapis.com/auth/firebase.database',
        'https://www.googleapis.com/auth/userinfo.email',
        'https://www.googleapis.com/auth/cloud-platform'
      ].join(' '),
      aud: 'https://oauth2.googleapis.com/token',
      iss: serviceAccount.client_email,
      sub: serviceAccount.client_email
    })
    .setProtectedHeader({ alg: 'RS256', typ: 'JWT' })
    .setExpirationTime('1h')
    .setIssuedAt(now)
    .sign(privateKey);

    console.log('JWT generated successfully');

    // Intercambiar JWT por token de acceso
    const response = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({
        grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',
        assertion: jwt
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Error getting access token: ${JSON.stringify(errorData)}`);
    }

    const data = await response.json() as GoogleTokenResponse;
    console.log('Access token obtained successfully');
    return data.access_token;
  } catch (error) {
    console.error('Error generating Google access token:', error);
    throw error;
  }
}

async function getCustomToken(serviceAccount: any, apiKey: string): Promise<string> {
  try {
    // Primero generamos un JWT token
    const now = Math.floor(Date.now() / 1000);

    // Convertir la clave privada a formato PEM
    const privateKeyPEM = serviceAccount.private_key
      .replace(/\\n/g, '\n')
      .trim();

    // Crear el JWT usando jose directamente con la clave privada en formato PEM
    const privateKey = await importPKCS8(privateKeyPEM, 'RS256');

    const jwtToken = await new SignJWT({
      iss: serviceAccount.client_email,
      sub: serviceAccount.client_email,
      aud: 'https://identitytoolkit.googleapis.com/google.identity.identitytoolkit.v1.IdentityToolkit',
      iat: now,
      exp: now + 3600,
      uid: 'service-account',
      claims: {
        provider_id: 'service_account',
        admin: true
      }
    })
    .setProtectedHeader({ alg: 'RS256' })
    .sign(privateKey);

    console.log('JWT token generated with service account claims');

    // Ahora intercambiamos el JWT por un token de Firebase
    const response = await fetch(`https://identitytoolkit.googleapis.com/v1/accounts:signInWithCustomToken?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        token: jwtToken,
        returnSecureToken: true
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Failed to get custom token: ${JSON.stringify(errorData)}`);
    }

    const data = await response.json() as { idToken: string };
    console.log('Firebase token obtained successfully');
    return data.idToken;
  } catch (error) {
    console.error('Error getting custom token:', error);
    throw error;
  }
}

async function initializeFirebase(env: Env): Promise<{ databaseURL: string }> {
  try {
    console.log('Iniciando inicialización de Firebase...');

    // Parse and validate service account
    const serviceAccount = JSON.parse(env.FIREBASE_SERVICE_ACCOUNT);
    console.log('Service Account Project ID:', serviceAccount.project_id);

    // Parse and validate Firebase config
    const firebaseConfig = JSON.parse(env.FIREBASE_CONFIG);
    console.log('Firebase Config Project ID:', firebaseConfig.projectId);

    // Validate database URL
    if (!env.FIREBASE_DATABASE_URL) {
      throw new Error('FIREBASE_DATABASE_URL no está definido');
    }
    console.log('Database URL:', env.FIREBASE_DATABASE_URL);

    // Check if app is already initialized
    const apps = getApps();
    if (apps.length === 0) {
      console.log('Inicializando nueva app de Firebase...');
      const app = initializeApp({
        credential: cert(serviceAccount as any),
        databaseURL: env.FIREBASE_DATABASE_URL
      });
      console.log('App inicializada:', app.name);

      // Initialize database
      const database = getDatabase();
      console.log('Database inicializada');
    } else {
      console.log('Firebase ya está inicializado:', apps[0].name);
    }

    return {
      databaseURL: env.FIREBASE_DATABASE_URL
    };
  } catch (error) {
    console.error('Error detallado al inicializar Firebase:', {
      message: error instanceof Error ? error.message : 'Error desconocido',
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error;
  }
}

async function getActiveAutomations(env: Env): Promise<AutomationRTDB[]> {
  try {
    console.log('Iniciando obtención de automatizaciones activas...');

    await initializeFirebase(env);
    console.log('Firebase inicializado correctamente');

    const database = getDatabase();
    console.log('Referencia a la base de datos obtenida');

    // Obtener una referencia específica a las automatizaciones
    const automationsRef = database.ref('automations');
    console.log('Referencia a automatizaciones creada:', automationsRef.toString());

    try {
      console.log('Intentando obtener snapshot de automatizaciones...');
      const snapshot = await Promise.race([
        automationsRef.once('value'),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout getting automations')), 5000)
        )
      ]) as DataSnapshot;

      console.log('Snapshot obtenido:', {
        exists: snapshot.exists(),
        key: snapshot.key,
        numChildren: snapshot.numChildren?.() || 0
      });

      if (!snapshot.exists()) {
        console.log('No se encontraron automatizaciones');
        return [];
      }

      console.log('Procesando datos del snapshot...');
      const automations: AutomationRTDB[] = [];

      snapshot.forEach((userSnapshot: DataSnapshot) => {
        console.log(`Procesando automatizaciones para usuario: ${userSnapshot.key}`);

        userSnapshot.forEach((automationSnapshot: DataSnapshot) => {
          const automation = automationSnapshot.val();
          console.log(`Encontrada automatización: ${automationSnapshot.key}`, {
            isActive: automation?.isActive,
            timing: automation?.timing
          });

          if (automation?.isActive) {
            automations.push({
              ...automation,
              id: automationSnapshot.key,
              userId: userSnapshot.key
            });
          }
        });
      });

      console.log(`Se encontraron ${automations.length} automatizaciones activas`);
      return automations;
    } catch (snapshotError) {
      console.error('Error obteniendo snapshot:', {
        error: snapshotError,
        message: snapshotError instanceof Error ? snapshotError.message : 'Error desconocido',
        stack: snapshotError instanceof Error ? snapshotError.stack : undefined
      });
      throw snapshotError;
    }
  } catch (error) {
    console.error('Error detallado al obtener automatizaciones:', {
      message: error instanceof Error ? error.message : 'Error desconocido',
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error;
  }
}

function processAutomationsData(data: any): AutomationRTDB[] {
  console.log('Processing automations data...');

  if (!data) {
    console.log('No data found');
    return [];
  }

  const automations: AutomationRTDB[] = [];

  Object.entries(data).forEach(([userId, userAutomations]: [string, any]) => {
    console.log(`Processing automations for user ${userId}`);

    if (typeof userAutomations === 'object' && userAutomations !== null) {
      Object.entries(userAutomations).forEach(([automationId, automation]: [string, any]) => {
        if (automation?.isActive) {
          automations.push({
            id: automationId,
            userId,
            ...automation,
            createdAt: automation.createdAt || Date.now(),
            updatedAt: automation.updatedAt || Date.now(),
            variables: automation.variables || [],
            description: automation.description || ''
          });
        }
      });
    }
  });

  console.log(`Found ${automations.length} active automations`);
  return automations;
}

async function logAppointmentProcessed(
  automation: AutomationRTDB,
  appointment: Appointment,
  result: any
) {
  const logRef = db.ref(`automation_logs/${automation.userId}`)
  const newLogRef = logRef.push()
  await newLogRef.set({
    automationId: automation.id,
    appointmentId: appointment.id,
    status: 'success',
    result,
    timestamp: Date.now()
  })
}

async function logError(automation: AutomationRTDB, error: any) {
  const logRef = db.ref(`automation_logs/${automation.userId}`)
  const newLogRef = logRef.push()
  await newLogRef.set({
    automationId: automation.id,
    status: 'error',
    error: error.message || String(error),
    timestamp: Date.now()
  })
}

async function processAppointments(
  appointments: Appointment[],
  automation: AutomationRTDB,
  env: Env
): Promise<void> {
  const serviceAccount = JSON.parse(env.FIREBASE_SERVICE_ACCOUNT);
  const accessToken = await generateGoogleAccessToken(serviceAccount);

  for (const appointment of appointments) {
    try {
      console.log(`Processing appointment ${appointment.id} for automation ${automation.id}`);
      console.log('Appointment details:', {
        date: appointment.date,
        clientId: appointment.clientId,
        clientName: appointment.clientName,
        depositPaid: appointment.depositPaid
      });

      // La verificación del depósito ya se hizo en el filtro anterior, pero lo mantenemos por seguridad
      if (!appointment.depositPaid) {
        console.log(`Skipping appointment ${appointment.id} because deposit is not paid`);
        continue;
      }

      // Verificar condiciones personalizadas si existen
      if (automation.customConditions && automation.customConditions.length > 0) {
        const conditionsMet = await evaluateCustomConditions(appointment, automation.customConditions, accessToken, env);
        if (!conditionsMet) {
          console.log(`Skipping appointment ${appointment.id} because custom conditions are not met`);
          continue;
        }
      }

      // Get client details from Firestore using REST API with a structured query
      const clientQuery = {
        structuredQuery: {
          from: [{ collectionId: 'clients' }],
          where: {
            fieldFilter: {
              field: { fieldPath: 'conversationId' },
              op: 'EQUAL',
              value: { stringValue: appointment.clientId }
            }
          },
          limit: 1
        }
      };

      const clientResponse = await fetch(
        `https://firestore.googleapis.com/v1/projects/${serviceAccount.project_id}/databases/(default)/documents:runQuery`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(clientQuery)
        }
      );

      if (!clientResponse.ok) {
        throw new Error(`Failed to fetch client: ${clientResponse.statusText}`);
      }

      const clientResults = await clientResponse.json() as FirestoreClientQueryResponse[];
      console.log('Client query results:', JSON.stringify(clientResults, null, 2));

      if (!clientResults[0]?.document?.fields) {
        console.log(`No client found with conversationId ${appointment.clientId}`);
        continue;
      }

      const clientData = clientResults[0].document;
      const client = {
        id: clientData.name.split('/').pop(),
        name: clientData.fields.name?.stringValue || appointment.clientName,
        conversationId: clientData.fields.conversationId?.stringValue || appointment.clientId
      };

      console.log('Found client:', client);


    // Obtener conexión Meta para enviar mensajes
    const metaQuery = {
      structuredQuery: {
        from: [{ collectionId: 'meta' }],
        where: {
          fieldFilter: {
            field: { fieldPath: 'userId' },
            op: 'EQUAL',
            value: { stringValue: automation.userId }
          }
        },
        limit: 1
      }
    };

    const metaResponse = await fetch(
      `https://firestore.googleapis.com/v1/projects/${serviceAccount.project_id}/databases/(default)/documents:runQuery`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(metaQuery)
      }
    );

    if (!metaResponse.ok) {
      throw new Error(`Error al obtener conexión Meta: ${metaResponse.statusText}`);
    }

    const metaResults = await metaResponse.json() as Array<Record<string, any>>;
    if (!metaResults[0]?.document?.fields) {
      throw new Error('No se encontró conexión Meta para el usuario');
    }

    const metaConnection = {
      pageId: metaResults[0].document.fields.pageId.stringValue as string,
      pageAccessToken: metaResults[0].document.fields.pageAccessToken.stringValue as string
    };

    // ...
  } catch (error) {
    console.error('Error procesando anuncio específico:', error);
    throw error;
  }
}

async function sendMessage(
  automation: AutomationRTDB,
  appointment: Appointment,
  metaConnection: MetaConnection,
  env: Env
): Promise<any> {
  try {
    console.log(`Sending message for automation ${automation.id} to appointment ${appointment.id}`);
    const serviceAccount = JSON.parse(env.FIREBASE_SERVICE_ACCOUNT);
    const accessToken = await generateGoogleAccessToken(serviceAccount);

    // Get client details from Firestore
    const clientQuery = {
      structuredQuery: {
        from: [{ collectionId: 'clients' }],
        where: {
          fieldFilter: {
            field: { fieldPath: 'conversationId' },
            op: 'EQUAL',
            value: { stringValue: appointment.clientId }
          }
        },
        limit: 1
      }
    };

    const clientResponse = await fetch(
      `https://firestore.googleapis.com/v1/projects/${serviceAccount.project_id}/databases/(default)/documents:runQuery`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(clientQuery)
      }
    );

    if (!clientResponse.ok) {
      throw new Error(`Failed to fetch client: ${clientResponse.statusText}`);
    }

    const clientResults = await clientResponse.json() as Array<Record<string, any>>;
    if (!clientResults[0]?.document?.fields) {
      console.log(`No client found with conversationId ${appointment.clientId}`);
      throw new Error(`No client found with conversationId ${appointment.clientId}`);
    }

    const clientData = clientResults[0].document;
    const client = {
      id: clientData.name.split('/').pop() as string,
      name: clientData.fields.name?.stringValue || appointment.clientName,
      conversationId: clientData.fields.conversationId.stringValue as string
    };

    // Process message template
    const message = automation.messageTemplate.replace(/{{([^}]+)}}/g, (match, variable) => {
      const mappings: { [key: string]: string } = {
        client_name: client.name || 'Cliente',
        appointment_time: format(toZonedTime(new Date(appointment.date), CHILE_TIMEZONE), 'HH:mm', { locale: es }),
        appointment_date: format(toZonedTime(new Date(appointment.date), CHILE_TIMEZONE), "EEEE, d 'de' MMMM", { locale: es })
      };
      return mappings[variable.trim()] || match;
    });

    // Obtener la clave API de Unipile desde las variables de entorno
    const apiKey = env.NEXT_PUBLIC_UNIPILE_API_KEY;
    const apiUrl = env.NEXT_PUBLIC_UNIPILE_API_URL || 'https://api1.unipile.com:13111';

    if (!apiKey) {
      console.error('Error: NEXT_PUBLIC_UNIPILE_API_KEY no está configurada');
      throw new Error('Configuración de API de Unipile incompleta. Falta NEXT_PUBLIC_UNIPILE_API_KEY.');
    }

    // Obtener datos de la conversación
    const conversationResponse = await fetch(
      `${env.FIREBASE_DATABASE_URL}/conversations/${automation.userId}/${client.conversationId}.json?access_token=${accessToken}`,
      {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      }
    );

    if (!conversationResponse.ok) {
      throw new Error(`No se pudo obtener la conversación: ${conversationResponse.statusText}`);
    }

    const conversationData = await conversationResponse.json() as Record<string, any>;

    if (!conversationData) {
      throw new Error(`Conversación no encontrada: ${client.conversationId}`);
    }

    // Obtener accountId y chatId de la conversación
    const accountId = conversationData.accountId as string;
    const unipileChatId = (conversationData.unipileChatId as string) || client.conversationId;

    // Verificaciones críticas
    if (!accountId) {
      console.error(`No se encontró un ID de cuenta de Unipile para la conversación ${client.conversationId}`);
      throw new Error(`No se encontró un ID de cuenta de Unipile para la conversación ${client.conversationId}`);
    }

    // Crear FormData para la solicitud a Unipile
    const formData = new FormData();
    formData.append('text', message);
    formData.append('account_id', accountId);
    formData.append('chat_id', unipileChatId);

    // Construir la URL completa con el ID de chat correcto
    const apiEndpoint = `${apiUrl}/api/v1/chats/${unipileChatId}/messages`;

    // Enviar mensaje usando Unipile API
    const unipileResponse = await fetch(
      apiEndpoint,
      {
        method: 'POST',
        headers: {
          'accept': 'application/json',
          'X-API-KEY': apiKey
        },
        body: formData
      }
    );

    // Manejar errores de la API de Unipile
    if (!unipileResponse.ok) {
      let errorText = '';
      try {
        errorText = await unipileResponse.text();
      } catch (e) {
        errorText = 'No se pudo leer el texto de error';
      }

      console.error(`Error from Unipile API: ${unipileResponse.status}`, errorText);
      throw new Error(`Error al enviar mensaje a través de Unipile: ${unipileResponse.status} - ${errorText}`);
    }

    // Procesar la respuesta
    const responseData = await unipileResponse.json() as Record<string, any>;
    console.log('Mensaje enviado exitosamente a través de Unipile:', responseData);

    // Usar el ID del mensaje de Unipile
    const messageResult = {
      message_id: responseData.id as string
    };

    // Marcar la cita como procesada en Realtime Database
    await fetch(
      `${env.FIREBASE_DATABASE_URL}/appointments/${appointment.id}.json?access_token=${accessToken}`,
      {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          lastProcessed: appointment.lastProcessed
            ? `${appointment.lastProcessed},${automation.id}`
            : automation.id
        })
      }
    );

    // Save message to RTDB
    const timestamp = Date.now();
    await fetch(
      `${env.FIREBASE_DATABASE_URL}/messages/${client.conversationId}.json?access_token=${accessToken}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          text: message,
          timestamp,
          direction: 'outbound',
          automationId: automation.id
        })
      }
    );

    return messageResult;
  } catch (error) {
    console.error('Error sending message:', error);
    throw error;
  }
}

async function processSpecificAnnouncement(automation: AutomationRTDB, env: Env): Promise<void> {
  try {
    console.log('Procesando anuncio específico para todos los clientes');
    const serviceAccount = JSON.parse(env.FIREBASE_SERVICE_ACCOUNT);
    const accessToken = await generateGoogleAccessToken(serviceAccount);

    // Obtener todos los clientes del usuario usando una consulta estructurada
    const clientsQuery = {
      structuredQuery: {
        from: [{ collectionId: 'clients' }],
        where: {
          fieldFilter: {
            field: { fieldPath: 'userId' },
            op: 'EQUAL',
            value: { stringValue: automation.userId }
          }
        },
        limit: 100
      }
    };
    
    const clientsResponse = await fetch(
      `https://firestore.googleapis.com/v1/projects/${serviceAccount.project_id}/databases/(default)/documents:runQuery`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(clientsQuery)
      }
    );

    if (!clientsResponse.ok) {
      throw new Error(`Failed to fetch clients: ${clientsResponse.statusText}`);
    }

    const clientsData = await clientsResponse.json() as Array<Record<string, any>>;
    console.log(`Se encontraron ${clientsData.length} resultados de consulta`);

    // Filtrar clientes válidos (que tengan conversationId)
    const userClients = clientsData
      .filter(result => result.document && result.document.fields && result.document.fields.conversationId)
      .map(result => result.document);
      
    console.log(`Se procesarán ${userClients.length} clientes para el anuncio`);

    // Obtener conexión Meta
    const metaQuery = {
      structuredQuery: {
        from: [{ collectionId: 'metaConnections' }],
        where: {
          fieldFilter: {
            field: { fieldPath: 'userId' },
            op: 'EQUAL',
            value: { stringValue: automation.userId }
          }
        },
        limit: 1
      }
    };

    const metaResponse = await fetch(
      `https://firestore.googleapis.com/v1/projects/${serviceAccount.project_id}/databases/(default)/documents:runQuery`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(metaQuery)
      }
    );

    if (!metaResponse.ok) {
      throw new Error(`Failed to fetch Meta connection: ${metaResponse.statusText}`);
    }

    const metaResults = await metaResponse.json() as Array<Record<string, any>>;
    if (!metaResults[0]?.document?.fields) {
      console.log(`No Meta connection found for user ${automation.userId}`);
      return;
    }

    const metaData = metaResults[0].document.fields;
    const metaConnection = {
      pageId: metaData.pageId.stringValue as string,
      pageAccessToken: metaData.pageAccessToken.stringValue as string,
      userId: automation.userId
    };

    // Enviar mensaje a cada cliente
    for (const client of userClients) {
      try {
        const clientData = {
          id: client.name.split('/').pop(),
          name: client.fields.name?.stringValue || 'Cliente',
          conversationId: client.fields.conversationId?.stringValue
        };

        // Crear un objeto de cita ficticio para la función de envío de mensajes
        const dummyAppointment = {
          id: 'announcement',
          clientId: clientData.conversationId,
          clientName: clientData.name,
          date: new Date().toISOString(),
          artistId: automation.userId,
          depositPaid: true
        };

        await sendMessage(automation, dummyAppointment, metaConnection, env);
        console.log(`Mensaje enviado a cliente ${clientData.name}`);
      } catch (clientError) {
        console.error(`Error enviando mensaje a cliente:`, clientError);
        // Continuar con el siguiente cliente
      }
    }

    // Actualizar timestamp de último procesamiento
    await fetch(
      `${env.FIREBASE_DATABASE_URL}/automations/${automation.userId}/${automation.id}.json?access_token=${accessToken}`,
      {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          lastProcessed: Date.now()
        })
      }
    );

  } catch (error) {
    console.error(`Error procesando anuncio específico:`, error);
    throw error;
  }
}

async function processAutomation(automation: AutomationRTDB, env: Env): Promise<void> {
  try {
    console.log(`Processing automation ${automation.id} for user ${automation.userId}`);
    console.log('Automation timing:', {
      type: automation.timing.type,
      value: automation.timing.value,
      unit: automation.timing.unit,
      specificDate: automation.timing.specificDate,
      specificTime: automation.timing.specificTime
    });

    const currentTime = new Date();
    // Usar toZonedTime para convertir correctamente a hora de Chile
    const currentTimeChile = toZonedTime(currentTime, CHILE_TIMEZONE);
    console.log('Current time UTC:', currentTime.toISOString());
    console.log('Current time in Chile:', formatISO(currentTimeChile));

    // Obtener el token de acceso al inicio para usarlo en toda la función
    const serviceAccount = JSON.parse(env.FIREBASE_SERVICE_ACCOUNT);
    const accessToken = await generateGoogleAccessToken(serviceAccount);
    
    // Para el tipo 'specific', calcular la fecha objetivo
    if (automation.timing.type === 'specific' && automation.timing.specificDate && automation.timing.specificTime) {
      console.log('Processing specific timing type:', {
        specificDate: automation.timing.specificDate,
        specificTime: automation.timing.specificTime,
        phaseId: automation.phaseId
      });

      const [year, month, day] = automation.timing.specificDate.split('-').map(Number);
      const [hours, minutes] = automation.timing.specificTime.split(':').map(Number);
      const targetDate = new Date(year, month - 1, day, hours, minutes);

      // Verificar si es el momento exacto para enviar la automatización específica
      const isSameYear = currentTime.getFullYear() === targetDate.getFullYear();
      const isSameMonth = currentTime.getMonth() === targetDate.getMonth();
      const isSameDay = currentTime.getDate() === targetDate.getDate();
      const isSameHour = currentTime.getHours() === targetDate.getHours();
      const isSameMinute = currentTime.getMinutes() === targetDate.getMinutes();
      const isExactTime = isSameYear && isSameMonth && isSameDay && isSameHour && isSameMinute;

      console.log('Target date for specific timing:', {
        targetDate: targetDate.toISOString(),
        currentTime: currentTime.toISOString(),
        isMatch: isExactTime
      });
      
      // Si es una automatización de tipo 'specific' en la fase 'announcements' y es el momento exacto
      if (automation.phaseId === 'announcements' && isExactTime) {
        console.log('Es el momento exacto para enviar la automatización de anuncios');
        await processSpecificAnnouncement(automation, env);
        
        // Actualizar timestamp de último procesamiento
        await fetch(
          `${env.FIREBASE_DATABASE_URL}/automations/${automation.userId}/${automation.id}.json?access_token=${accessToken}`,
          {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              lastProcessed: Date.now()
            })
          }
        );
        
        return; // Terminar aquí ya que ya procesamos esta automatización
      }
    }

    // Get appointments from Realtime Database
    console.log('Fetching appointments from Realtime Database...');

    const appointmentsResponse = await fetch(
      `${env.FIREBASE_DATABASE_URL}/appointments.json?orderBy="artistId"&equalTo="${automation.userId}"&access_token=${accessToken}`,
      {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      }
    );

    if (!appointmentsResponse.ok) {
      const errorText = await appointmentsResponse.text();
      console.error('RTDB error response:', errorText);
      throw new Error(`Failed to fetch appointments: ${appointmentsResponse.statusText}. Details: ${errorText}`);
    }

    const appointmentsData = await appointmentsResponse.json();
    console.log('Raw appointments response:', JSON.stringify(appointmentsData, null, 2));

    if (!appointmentsData) {
      console.log('No appointments found');
      return;
    }

    // Transform RTDB data to Appointments and filter by timing
    const appointments: Appointment[] = Object.entries(appointmentsData)
      .map(([id, data]: [string, any]) => ({
        id,
        date: data.date,
        clientId: data.clientId,
        clientName: data.clientName,
        artistId: data.artistId,
        depositPaid: data.depositPaid,
        lastProcessed: data.lastProcessed
      }))
      .filter(appointment => {
        const appointmentDateUTC = new Date(appointment.date);
        const currentTimeUTC = new Date();

        // Calcular la fecha objetivo según el timing de la automatización
        let targetDate = new Date(appointmentDateUTC.getTime());

        console.log('Timing calculation:', {
          appointmentDate: appointmentDateUTC.toISOString(),
          currentTime: currentTimeUTC.toISOString(),
          initialTargetDate: targetDate.toISOString(),
          automationType: automation.timing.type,
          automationValue: automation.timing.value,
          automationUnit: automation.timing.unit
        });

        // Para el tipo 'specific', usar la fecha y hora específica
        if (automation.timing.type === 'specific' && automation.timing.specificDate && automation.timing.specificTime) {
          const [year, month, day] = automation.timing.specificDate.split('-').map(Number);
          const [hours, minutes] = automation.timing.specificTime.split(':').map(Number);
          targetDate = new Date(year, month - 1, day, hours, minutes);
          console.log('Specific date calculation:', {
            targetDate: targetDate.toISOString(),
            specificDate: automation.timing.specificDate,
            specificTime: automation.timing.specificTime
          });
        }
        // Para el tipo 'during', usamos directamente la fecha de la cita
        else if (automation.timing.type === 'during') {
          console.log('Processing "during" type - using exact appointment time');
          targetDate = appointmentDateUTC;
        }
        else if (automation.timing.type === 'before') {
        switch (automation.timing.unit) {
          case 'minutes':
              targetDate.setMinutes(targetDate.getMinutes() - automation.timing.value);
            break;
          case 'hours':
              targetDate.setHours(targetDate.getHours() - automation.timing.value);
            break;
          case 'days':
              targetDate.setDate(targetDate.getDate() - automation.timing.value);
            break;
          case 'weeks':
              targetDate.setDate(targetDate.getDate() - (automation.timing.value * 7));
            break;
          case 'months':
              targetDate.setMonth(targetDate.getMonth() - automation.timing.value);
            break;
          }
        }
        else if (automation.timing.type === 'after') {
          switch (automation.timing.unit) {
            case 'minutes':
              targetDate.setMinutes(targetDate.getMinutes() + automation.timing.value);
              break;
            case 'hours':
              targetDate.setHours(targetDate.getHours() + automation.timing.value);
              break;
            case 'days':
              targetDate.setDate(targetDate.getDate() + automation.timing.value);
              break;
            case 'weeks':
              targetDate.setDate(targetDate.getDate() + (automation.timing.value * 7));
              break;
            case 'months':
              targetDate.setMonth(targetDate.getMonth() + automation.timing.value);
              break;
          }
        }

        console.log('After timing adjustment:', {
          targetDate: targetDate.toISOString(),
          diffInMinutes: (appointmentDateUTC.getTime() - targetDate.getTime()) / (1000 * 60),
          minutesUntilTarget: (targetDate.getTime() - currentTimeUTC.getTime()) / (1000 * 60)
        });

        // Comparar hora y minutos exactos
        const isSameYear = currentTimeUTC.getFullYear() === targetDate.getFullYear();
        const isSameMonth = currentTimeUTC.getMonth() === targetDate.getMonth();
        const isSameDay = currentTimeUTC.getDate() === targetDate.getDate();
        const isSameHour = currentTimeUTC.getHours() === targetDate.getHours();
        const isSameMinute = currentTimeUTC.getMinutes() === targetDate.getMinutes(); // Debe ser exactamente el mismo minuto

        // Verificar si el depósito está pagado
        const isDepositPaid = appointment.depositPaid === true;

        // Solo procesar si coincide con la fecha y hora objetivo exacta y el depósito está pagado
        const shouldProcess = isSameYear && isSameMonth && isSameDay && isSameHour && isSameMinute && isDepositPaid;

        console.log('Time comparison:', {
          isWithinWindow: shouldProcess,
          timing: {
            isSameYear,
            isSameMonth,
            isSameDay,
            isSameHour,
            isSameMinute
          },
          isDepositPaid
        });

        return shouldProcess;
      });

    console.log(`Processing ${appointments.length} appointments for automation ${automation.id}`);

    if (appointments.length > 0) {
      await processAppointments(appointments, automation, env);
    }

    // Update last processed timestamp in Realtime Database using REST API
    await fetch(
      `${env.FIREBASE_DATABASE_URL}/automations/${automation.userId}/${automation.id}.json?access_token=${accessToken}`,
      {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          lastProcessed: currentTime.getTime()
        })
      }
    );
  } catch (error) {
    console.error(`Error processing automation ${automation.id}:`, error);
    throw error;
  }
}

async function evaluateCustomConditions(
  appointment: Appointment,
  conditions: CustomCondition[],
  accessToken: string,
  env: Env
): Promise<boolean> {
  console.log(`Evaluating ${conditions.length} custom conditions for appointment ${appointment.id}`);

  for (const condition of conditions) {
    console.log(`Evaluating condition of type ${condition.type} with value ${condition.value} and operator ${condition.operator}`);

    if (condition.type === 'total_spent') {
      const totalSpent = await calculateTotalSpent(appointment.clientId, accessToken, env);
      console.log(`Total spent by client ${appointment.clientId}: ${totalSpent}`);

      const conditionValue = condition.value;
      let conditionMet = false;

      switch (condition.operator) {
        case '>':
          conditionMet = totalSpent > conditionValue;
          break;
        case '<':
          conditionMet = totalSpent < conditionValue;
          break;
        case '=':
          conditionMet = totalSpent === conditionValue;
          break;
        case '>=':
          conditionMet = totalSpent >= conditionValue;
          break;
        case '<=':
          conditionMet = totalSpent <= conditionValue;
          break;
      }

      console.log(`Condition evaluation result: ${conditionMet} (${totalSpent} ${condition.operator} ${conditionValue})`);

      if (!conditionMet) {
        return false;
      }
    }
    else if (condition.type === 'pending_sessions') {
      // Calcular sesiones pendientes para el cliente
      const pendingSessions = await calculatePendingSessions(appointment.clientId, accessToken, env);
      console.log(`Pending sessions for client ${appointment.clientId}: ${pendingSessions}`);

      const conditionValue = condition.value;
      let conditionMet = false;

      switch (condition.operator) {
        case '>':
          conditionMet = pendingSessions > conditionValue;
          break;
        case '<':
          conditionMet = pendingSessions < conditionValue;
          break;
        case '=':
          conditionMet = pendingSessions === conditionValue;
          break;
        case '>=':
          conditionMet = pendingSessions >= conditionValue;
          break;
        case '<=':
          conditionMet = pendingSessions <= conditionValue;
          break;
      }

      console.log(`Condition evaluation result: ${conditionMet} (${pendingSessions} ${condition.operator} ${conditionValue})`);

      if (!conditionMet) {
        return false;
      }
    }
    // Aquí se pueden agregar más tipos de condiciones en el futuro
  }

  return true;
}

async function calculateTotalSpent(
  clientId: string,
  accessToken: string,
  env: Env
): Promise<number> {
  console.log(`Calculating total spent for client ${clientId}`);

  try {
    // Obtener todas las citas desde la base de datos (sin filtrar por clientId)
    const appointmentsResponse = await fetch(
      `${env.FIREBASE_DATABASE_URL}/appointments.json?access_token=${accessToken}`,
      {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      }
    );

    if (!appointmentsResponse.ok) {
      const errorText = await appointmentsResponse.text();
      console.error('RTDB error response:', errorText);
      throw new Error(`Failed to fetch appointments: ${appointmentsResponse.statusText}`);
    }

    const appointmentsData = await appointmentsResponse.json();
    if (!appointmentsData) {
      return 0;
    }

    // Filtrar las citas del cliente manualmente
    const clientAppointments = Object.values(appointmentsData).filter((appointment: any) =>
      appointment.clientId === clientId
    );

    console.log(`Found ${clientAppointments.length} appointments for client ${clientId}`);

    // Calcular el gasto total sumando sessionPrice si sessionPaid es true y bookingPrice si depositPaid es true
    let totalSpent = 0;

    clientAppointments.forEach((appointment: any) => {
      console.log('Processing appointment for total spent calculation:', {
        id: appointment.id,
        sessionPrice: appointment.sessionPrice,
        sessionPaid: appointment.sessionPaid,
        bookingPrice: appointment.bookingPrice,
        depositPaid: appointment.depositPaid
      });

      if (appointment.sessionPaid && appointment.sessionPrice) {
        totalSpent += Number(appointment.sessionPrice);
      }

      if (appointment.depositPaid && appointment.bookingPrice) {
        totalSpent += Number(appointment.bookingPrice);
      }
    });

    console.log(`Total spent calculated for client ${clientId}: ${totalSpent}`);
    return totalSpent;

  } catch (error) {
    console.error(`Error calculating total spent for client ${clientId}:`, error);
    return 0;
  }
}

async function calculatePendingSessions(
  clientId: string,
  accessToken: string,
  env: Env
): Promise<number> {
  console.log(`Calculating pending sessions for client ${clientId}`);

  try {
    // Buscar todas las citas del cliente
    const appointmentsResponse = await fetch(
      `${env.FIREBASE_DATABASE_URL}/appointments.json?orderBy="clientId"&equalTo="${clientId}"&access_token=${accessToken}`,
      {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      }
    );

    if (!appointmentsResponse.ok) {
      throw new Error(`Failed to fetch appointments: ${appointmentsResponse.statusText}`);
    }

    const appointmentsData = await appointmentsResponse.json();

    if (!appointmentsData) {
      console.log(`No appointments found for client ${clientId}`);
      return 0;
    }

    let totalPendingSessions = 0;

    // Calcular sesiones pendientes totales
    for (const id in appointmentsData) {
      // Usar tipado explícito para evitar errores
      const appointment = appointmentsData[id] as Record<string, any>;

      // Solo considerar citas con estado pendiente o confirmado
      if (appointment.status !== 'cancelled' && appointment.status !== 'completed') {
        // Si la cita tiene información de sesiones, calculamos las pendientes
        if (appointment.totalSessions && appointment.completedSessions !== undefined) {
          const pendingForAppointment = Math.max(0, appointment.totalSessions - appointment.completedSessions);
          totalPendingSessions += pendingForAppointment;

          console.log(`Appointment ${id}: ${pendingForAppointment} pending sessions (${appointment.completedSessions}/${appointment.totalSessions})`);
        } else if (appointment.totalSessions) {
          // Si tiene totalSessions pero no completedSessions, todas están pendientes
          totalPendingSessions += appointment.totalSessions;

          console.log(`Appointment ${id}: ${appointment.totalSessions} pending sessions (0/${appointment.totalSessions})`);
        } else {
          // Si no tiene información de sesiones, consideramos que es 1 sesión pendiente
          totalPendingSessions += 1;

          console.log(`Appointment ${id}: 1 pending session (default)`);
        }
      }
    }

    console.log(`Total pending sessions calculated for client ${clientId}: ${totalPendingSessions}`);
    return totalPendingSessions;

  } catch (error) {
    console.error(`Error calculating pending sessions for client ${clientId}:`, error);
    // En caso de error, asumimos 0 sesiones pendientes
    return 0;
  }
}

export default {
  async fetch(request: Request, env: Env, ctx: ExecutionContext) {
    const url = new URL(request.url);

    // Endpoint para verificar variables de entorno
    if (request.method === 'GET' && url.pathname === '/test-env') {
      try {
        const firebaseConfig = JSON.parse(env.FIREBASE_CONFIG);
        const serviceAccount = JSON.parse(env.FIREBASE_SERVICE_ACCOUNT);

        return new Response(JSON.stringify({
          success: true,
          config: {
            hasFirebaseConfig: !!env.FIREBASE_CONFIG,
            hasServiceAccount: !!env.FIREBASE_SERVICE_ACCOUNT,
            hasDatabaseUrl: !!env.FIREBASE_DATABASE_URL,
            databaseUrl: env.FIREBASE_DATABASE_URL,
            projectId: firebaseConfig.projectId,
            serviceAccountEmail: serviceAccount.client_email
          }
        }, null, 2), {
          status: 200,
          headers: {
            'Content-Type': 'application/json'
          }
        });
      } catch (error) {
        return new Response(JSON.stringify({
          success: false,
          error: error instanceof Error ? error.message : 'Error desconocido'
        }, null, 2), {
          status: 500,
          headers: {
            'Content-Type': 'application/json'
          }
        });
      }
    }

    // Endpoint de prueba de conexión
    if (request.method === 'GET' && url.pathname === '/test-connection') {
      try {
        console.log('Testing Firebase connection...');

        // Parse service account
        const serviceAccount = JSON.parse(env.FIREBASE_SERVICE_ACCOUNT);
        console.log('Service Account loaded');

        // Get Google access token
        const accessToken = await generateGoogleAccessToken(serviceAccount);
        console.log('Access token obtained');

        // Test connection using REST API
        const response = await fetch(`${env.FIREBASE_DATABASE_URL}/.json?access_token=${accessToken}`, {
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error(`Database connection failed: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Connection test successful');

        return new Response(JSON.stringify({
          success: true,
          connected: true,
          timestamp: Date.now()
        }), {
          status: 200,
          headers: {
            'Content-Type': 'application/json'
          }
        });
      } catch (error) {
        console.error('Connection test error:', error);
        return new Response(JSON.stringify({
          success: false,
          error: error instanceof Error ? error.message : 'Error desconocido',
          timestamp: Date.now()
        }), {
          status: 500,
          headers: {
            'Content-Type': 'application/json'
          }
        });
      }
    }

    // Endpoint para probar la lectura de automatizaciones
    if (request.method === 'GET' && url.pathname === '/test-automations') {
      try {
        console.log('Testing automations retrieval...');

        // Parse service account
        const serviceAccount = JSON.parse(env.FIREBASE_SERVICE_ACCOUNT);
        console.log('Service Account loaded');

        // Get Google access token
        const accessToken = await generateGoogleAccessToken(serviceAccount);
        console.log('Access token obtained');

        // Get automations using REST API
        const automationsResponse = await fetch(`${env.FIREBASE_DATABASE_URL}/automations.json?access_token=${accessToken}`, {
          headers: { 'Content-Type': 'application/json' }
        });

        if (!automationsResponse.ok) {
          throw new Error(`Failed to fetch automations: ${automationsResponse.statusText}`);
        }

        const automationsData = await automationsResponse.json();
        console.log('Automations data fetched successfully');

        // Process automations data
        const automations = processAutomationsData(automationsData);

        return new Response(JSON.stringify({
          success: true,
          diagnostics: {
            rootAccess: true,
            automationsAccess: true,
            hasData: automationsData !== null
          },
          automations,
          count: automations.length,
          timestamp: Date.now()
        }, null, 2), {
          status: 200,
          headers: {
            'Content-Type': 'application/json'
          }
        });
      } catch (error) {
        console.error('Automations test error:', error);
        return new Response(JSON.stringify({
          success: false,
          error: error instanceof Error ? error.message : 'Error desconocido',
          timestamp: Date.now()
        }), {
          status: 500,
          headers: {
            'Content-Type': 'application/json'
          }
        });
      }
    }

    // Endpoint para probar la evaluación de condiciones personalizadas
    if (request.method === 'GET' && url.pathname === '/test-conditions') {
      try {
        console.log('Testing custom conditions evaluation...');

        // Parse service account
        const serviceAccount = JSON.parse(env.FIREBASE_SERVICE_ACCOUNT);
        console.log('Service Account loaded');

        // Get Google access token
        const accessToken = await generateGoogleAccessToken(serviceAccount);
        console.log('Access token obtained');

        // Obtener parámetros de la URL
        const params = new URLSearchParams(url.search);
        const clientId = params.get('clientId');
        const conditionType = params.get('type') || 'total_spent';
        const conditionValue = parseInt(params.get('value') || '30000');
        const conditionOperator = params.get('operator') || '>';

        if (!clientId) {
          return new Response(JSON.stringify({
            success: false,
            error: 'Se requiere un clientId'
          }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          });
        }

        // Crear condición de prueba
        const condition: CustomCondition = {
          type: conditionType as any,
          value: conditionValue,
          operator: conditionOperator as any
        };

        console.log('Testing condition:', condition);

        // Calcular el gasto total
        const totalSpent = await calculateTotalSpent(clientId, accessToken, env);

        // Evaluar la condición
        let conditionMet = false;
        switch (condition.operator) {
          case '>':
            conditionMet = totalSpent > condition.value;
            break;
          case '<':
            conditionMet = totalSpent < condition.value;
            break;
          case '=':
            conditionMet = totalSpent === condition.value;
            break;
          case '>=':
            conditionMet = totalSpent >= condition.value;
            break;
          case '<=':
            conditionMet = totalSpent <= condition.value;
            break;
        }

        return new Response(JSON.stringify({
          success: true,
          clientId,
          condition,
          totalSpent,
          conditionMet,
          evaluation: `${totalSpent} ${condition.operator} ${condition.value} = ${conditionMet}`,
          timestamp: Date.now()
        }, null, 2), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });
      } catch (error) {
        console.error('Conditions test error:', error);
        return new Response(JSON.stringify({
          success: false,
          error: error instanceof Error ? error.message : 'Error desconocido',
          timestamp: Date.now()
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }

    // Ruta original para pruebas programadas
    if (request.method === 'POST' && url.pathname === '/__scheduled') {
      const body = await request.json();

      if (typeof body === 'object' && body !== null &&
          'cron' in body && typeof body.cron === 'string' &&
          'scheduledTime' in body && typeof body.scheduledTime === 'number') {
        const scheduledEvent: ScheduledEvent = {
          cron: body.cron,
          scheduledTime: body.scheduledTime
        };
        await this.scheduled(scheduledEvent, env, ctx);
        return new Response('OK', { status: 200 });
      }

      return new Response('Invalid request body', { status: 400 });
    }

    return new Response('Not found', { status: 404 });
  },

  if (!clientId) {
    return new Response(JSON.stringify({
      success: false,
      error: 'Se requiere un clientId'
    }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' }
    });
  }

  // Crear condición de prueba
  const condition: CustomCondition = {
    type: conditionType as any,
    value: conditionValue,
    operator: conditionOperator as any
  };

  console.log('Testing condition:', condition);

  // Calcular el gasto total
  const totalSpent = await calculateTotalSpent(clientId, accessToken, env);

  // Evaluar la condición
  let conditionMet = false;
  switch (condition.operator) {
    case '>':
      conditionMet = totalSpent > condition.value;
      break;
    case '<':
      conditionMet = totalSpent < condition.value;
      break;
    case '=':
      conditionMet = totalSpent === condition.value;
      break;
    case '>=':
      conditionMet = totalSpent >= condition.value;
      break;
    case '<=':
      conditionMet = totalSpent <= condition.value;
      break;
  }

  return new Response(JSON.stringify({
    success: true,
    clientId,
    condition,
    totalSpent,
    conditionMet,
    evaluation: `${totalSpent} ${condition.operator} ${condition.value} = ${conditionMet}`,
    timestamp: Date.now()
  }, null, 2), {
    status: 200,
    headers: { 'Content-Type': 'application/json' }
  });
} catch (error) {
  console.error('Conditions test error:', error);
  return new Response(JSON.stringify({
    success: false,
    error: error instanceof Error ? error.message : 'Error desconocido',
    timestamp: Date.now()
  }), {
    status: 500,
    headers: { 'Content-Type': 'application/json' }
  });
}
}

// Ruta original para pruebas programadas
if (request.method === 'POST' && url.pathname === '/__scheduled') {
const body = await request.json();

if (typeof body === 'object' && body !== null &&
    'cron' in body && typeof body.cron === 'string' &&
    'scheduledTime' in body && typeof body.scheduledTime === 'number') {
  const scheduledEvent: ScheduledEvent = {
    cron: body.cron,
    scheduledTime: body.scheduledTime
  };
  await this.scheduled(scheduledEvent, env, ctx);
  return new Response('OK', { status: 200 });
}

return new Response('Invalid request body', { status: 400 });
}

return new Response('Not found', { status: 404 });
},

async scheduled(event: ScheduledEvent, env: Env, ctx: ExecutionContext) {
console.log('Starting automation processing:', new Date().toISOString());
const currentTime = Date.now();

try {
  console.log('Initializing automation processing...');

  // Parse service account and get access token
  const serviceAccount = JSON.parse(env.FIREBASE_SERVICE_ACCOUNT);
  console.log('Service Account loaded');

  const accessToken = await generateGoogleAccessToken(serviceAccount);
  console.log('Access token obtained');

  // Get automations using REST API
  const automationsResponse = await fetch(`${env.FIREBASE_DATABASE_URL}/automations.json?access_token=${accessToken}`, {
    headers: { 'Content-Type': 'application/json' }
  });

  if (!automationsResponse.ok) {
    throw new Error(`Failed to fetch automations: ${automationsResponse.statusText}`);
  }

  const automationsData = await automationsResponse.json();
  console.log('Automations data fetched successfully');

  // Process automations data
  const automations = processAutomationsData(automationsData);
  console.log(`Found ${automations.length} active automations`);

  // Process each automation
  for (const automation of automations) {
    try {
      await processAutomation(automation, env);
      console.log(`Successfully processed automation for appointment ${automation.id}`);
      for (const automation of automations) {
        try {
          await processAutomation(automation, env);
          console.log(`Successfully processed automation for appointment ${automation.id}`);
        } catch (error) {
          console.error(`Error processing automation ${automation.id}:`, error);
          // Log error
          await fetch(
            `${env.FIREBASE_DATABASE_URL}/automation_logs/${automation.userId}.json?access_token=${accessToken}`,
            {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                automationId: automation.id,
                status: 'error',
                error: error instanceof Error ? error.message : 'Unknown error',
                timestamp: currentTime
              })
            }
          );
        }
      }

      return new Response('OK', { status: 200 });
    } catch (error) {
      console.error('Critical error in scheduled function:', error);
      return new Response('Error', { status: 500 });
    }
  }
}