name = "automation-worker"
main = "src/worker.ts"
compatibility_date = "2024-09-23"
compatibility_flags = ["nodejs_compat"]

[triggers]
crons = ["* * * * *"]

[vars]
ENVIRONMENT = "development"

[env.development]
name = "automation-worker-dev"
vars = { ENVIRONMENT = "development" }

# Asegúrate de que estas variables estén configuradas con wrangler
# wrangler secret put FIREBASE_CONFIG
# wrangler secret put FIREBASE_SERVICE_ACCOUNT
# wrangler secret put FIREBASE_DATABASE_URL
# wrangler secret put UNIPILE_API_KEY
# wrangler secret put UNIPILE_API_URL