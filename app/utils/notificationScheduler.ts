import { 
  createSessionTodayNotification, 
  createAIClientMatchNotification 
} from '@/app/services/notifications';
import { sendNotificationEmail } from '@/app/services/email';
import { startOfDay, isToday, parseISO } from 'date-fns';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { Notification, NotificationType } from '@/app/types/notifications';

// Verifica sesiones para hoy y crea notificaciones
export const checkTodaySessions = async (userId: string, userEmail: string, artistName: string) => {
  try {
    // Obtener el inicio del día actual
    const today = startOfDay(new Date());
    
    // Referencia al documento de sesiones del usuario
    const appointmentsRef = doc(db, 'appointments', userId);
    const appointmentsSnap = await getDoc(appointmentsRef);
    
    if (!appointmentsSnap.exists()) {
      console.log('No hay sesiones para este usuario');
      return;
    }
    
    const appointments = appointmentsSnap.data().appointments || [];
    
    // Filtrar sesiones que son para hoy
    const todayAppointments = appointments.filter((appointment: any) => {
      if (!appointment.date) return false;
      
      let appointmentDate;
      try {
        appointmentDate = parseISO(appointment.date);
      } catch (error) {
        console.error('Error al parsear fecha de sesión:', error);
        return false;
      }
      
      return isToday(appointmentDate);
    });
    
    // Crear notificaciones para cada sesión de hoy
    for (const appointment of todayAppointments) {
      // Obtener datos del cliente
      let clientName = 'Cliente';
      if (appointment.clientId) {
        const clientRef = doc(db, 'clients', userId, 'clientList', appointment.clientId);
        const clientSnap = await getDoc(clientRef);
        if (clientSnap.exists()) {
          clientName = clientSnap.data().name || 'Cliente';
        }
      }
      
      // Formatear hora
      const sessionTime = appointment.time || 'hora programada';
      
      // Crear notificación en la base de datos
      const notificationId = await createSessionTodayNotification(
        userId,
        clientName,
        sessionTime,
        appointment.id
      );
      
      // Enviar correo electrónico
      if (userEmail) {
        // Crear objeto de notificación para el correo
        const notification: Notification = {
          id: notificationId,
          type: NotificationType.SESSION_TODAY,
          title: 'Sesión programada para hoy',
          message: `Tienes una sesión con ${clientName} a las ${sessionTime}`,
          read: false,
          createdAt: Date.now(),
          metadata: {
            sessionId: appointment.id,
            clientName,
            sessionTime
          }
        };
        
        // Enviar correo
        await sendNotificationEmail(userEmail, artistName, notification);
      }
    }
    
    return todayAppointments.length;
  } catch (error) {
    console.error('Error verificando sesiones de hoy:', error);
    return 0;
  }
};

// Función para enviar notificación de cliente potencial detectado por IA
export const sendAIClientMatchNotification = async (
  userId: string,
  userEmail: string,
  artistName: string,
  clientId: string,
  clientName: string,
  matchReason: string
) => {
  try {
    // Verificar las preferencias de notificación del usuario
    const userRef = doc(db, 'users', userId);
    const userDoc = await getDoc(userRef);
    let shouldSendEmail = true;
    
    if (userDoc.exists()) {
      const userData = userDoc.data();
      // Verificar si el usuario tiene preferencias de notificación configuradas
      if (userData.notificationPreferences && 
          userData.notificationPreferences.push && 
          userData.notificationPreferences.push.potentialClients === false) {
        // El usuario ha desactivado las notificaciones de clientes potenciales
        shouldSendEmail = false;
        console.log(`Usuario ${userId} ha desactivado las notificaciones de clientes potenciales`);
      }
    }
    
    // Crear notificación en la base de datos (siempre se crea en la base de datos)
    const notificationId = await createAIClientMatchNotification(
      userId,
      clientName,
      clientId,
      matchReason
    );
    
    // Enviar correo electrónico solo si el usuario tiene habilitadas las notificaciones
    if (userEmail && shouldSendEmail) {
      console.log(`Enviando correo de cliente potencial a ${userEmail}`);
      // Crear objeto de notificación para el correo
      const notification: Notification = {
        id: notificationId,
        type: NotificationType.AI_CLIENT_MATCH,
        title: 'Cliente potencial detectado',
        message: `La IA ha identificado a ${clientName} como un cliente potencial debido a ${matchReason}`,
        read: false,
        createdAt: Date.now(),
        metadata: {
          clientId,
          clientName,
          matchReason
        }
      };
      
      // Enviar correo
      await sendNotificationEmail(userEmail, artistName, notification);
    } else if (!shouldSendEmail) {
      console.log(`No se envió correo a ${userEmail} porque tiene desactivadas las notificaciones de clientes potenciales`);
    }
    
    return notificationId;
  } catch (error) {
    console.error('Error enviando notificación de cliente potencial:', error);
    return null;
  }
}; 