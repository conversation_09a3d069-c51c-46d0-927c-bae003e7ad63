/**
 * OBSOLETO: Este módulo ha sido reemplazado por la implementación directa con la API de Unipile
 * en app/api/webhooks/unipile/instagram-profile.ts
 * 
 * Se mantiene por compatibilidad con código existente, pero se recomienda usar la nueva implementación
 * que obtiene la foto de perfil directamente desde Unipile usando el attendee_id.
 *
 * @deprecated Usar fetchAndSaveInstagramProfilePicture de instagram-profile.ts en su lugar
 */
import axios from 'axios';

// Interfaz para mantener compatibilidad con instagramdp
export interface InstagramProfileData {
  profile_pic_url: string;
  profile_pic_url_hd?: string;
  username: string;
}

// Interfaz para la respuesta de Apify
interface ApifyProfileData {
  profilePicUrl: string;
  username: string;
}

/**
 * OBSOLETO: Obtiene los datos del perfil de Instagram usando la API de Apify con un enfoque simplificado
 * @deprecated Esta función ha sido reemplazada por la implementación directa con Unipile
 * @param username Nombre de usuario de Instagram
 * @returns Objeto con las URLs de la imagen de perfil o null si no se encuentra
 */
export async function getInstagramProfileWithApify(username: string): Promise<InstagramProfileData | null> {
  const debugId = Date.now().toString().slice(-6);
  console.log(`[${debugId}] 🤖 Iniciando búsqueda con Apify para: ${username}`);
  
  try {
    // Limpiar el nombre de usuario
    const cleanUsername = username.replace('@', '').trim();
    console.log(`[${debugId}] 👤 Username limpio: ${cleanUsername}`);
    
    // Verificar token de API
    const apiToken = process.env.APIFY_API_TOKEN;
    if (!apiToken) {
      console.error(`[${debugId}] ❌ Token de API de Apify no configurado - APIFY_API_TOKEN no está definido en las variables de entorno`);
      return null;
    }
    
    console.log(`[${debugId}] ✓ Token de Apify encontrado (primeros 4 caracteres): ${apiToken.substring(0, 4)}...`);
    
    // Preparar datos para la API - Usar un enfoque más simple
    const input = { usernames: [cleanUsername] };
    console.log(`[${debugId}] 📦 Ejecutando actor con input:`, JSON.stringify(input));
    
    // Usar un enfoque directo con un timeout más corto para evitar problemas con Vercel
    try {
      // Log antes de la llamada a la API
      console.log(`[${debugId}] 🔄 Iniciando llamada a API de Apify (método directo)`);
      
      // Usar axios con un timeout explícito más corto
      const response = await axios.post(
        `https://api.apify.com/v2/acts/dSCLg0C3YEZ83HzYX/run-sync-get-dataset-items?token=${apiToken}`,
        input,
        {
          headers: { 
            'Content-Type': 'application/json',
            'User-Agent': 'Tatu.Ink/1.0'
          },
          timeout: 8000 // 8 segundos de timeout (para estar dentro del límite de Vercel)
        }
      );
      
      console.log(`[${debugId}] ✓ Respuesta recibida de Apify con status: ${response.status}`);
      
      // Extraer directamente los datos de la respuesta
      const items = response.data;
      
      // Verificar si la respuesta es válida
      if (response.status !== 200) {
        console.error(`[${debugId}] ❌ Error al ejecutar Actor: ${response.status}`);
        console.error(`[${debugId}] ❌ Detalles del error:`, response.statusText);
        return null;
      }
      
      console.log(`[${debugId}] 🔄 Datos recibidos de Apify...`);
      
      console.log(`[${debugId}] ✅ Datos recibidos de tipo: ${typeof items}, es array: ${Array.isArray(items)}, longitud: ${Array.isArray(items) ? items.length : 'N/A'}`);
      console.log(`[${debugId}] ✅ Muestra de datos:`, JSON.stringify(items).substring(0, 100) + '...');
      
      // Verificar si hay resultados
      if (!Array.isArray(items) || items.length === 0) {
        console.log(`[${debugId}] ⚠️ No se encontraron resultados para ${cleanUsername}`);
        return null;
      }
      
      // Extraer la información del perfil
      const profileData = items[0] as ApifyProfileData;
      console.log(`[${debugId}] 🔍 Datos del primer resultado:`, JSON.stringify(profileData).substring(0, 200));
      
      if (!profileData || !profileData.profilePicUrl) {
        console.log(`[${debugId}] ⚠️ No se encontró URL de imagen de perfil para ${cleanUsername}`);
        return null;
      }
      
      // Construir el objeto de respuesta
      const result: InstagramProfileData = {
        profile_pic_url: profileData.profilePicUrl,
        profile_pic_url_hd: profileData.profilePicUrl,
        username: profileData.username || cleanUsername
      };
      
      console.log(`[${debugId}] ✅ Perfil encontrado para ${cleanUsername}:`, JSON.stringify({
        username: result.username,
        profile_pic_url: result.profile_pic_url.substring(0, 50) + '...'
      }));
      
      return result;
    } catch (fetchError) {
      console.error(`[${debugId}] ❌ Error en la llamada a Apify:`, fetchError);
      console.error(`[${debugId}] ❌ Stack trace:`, (fetchError as Error).stack);
      
      // Intentar con un método alternativo usando un endpoint diferente
      try {
        console.log(`[${debugId}] 🔄 Intentando con método alternativo: llamada a API de Apify con actor run...`);
        
        // Primero iniciamos la ejecución del actor (método asíncrono)
        const startRunResponse = await axios.post(
          `https://api.apify.com/v2/acts/dSCLg0C3YEZ83HzYX/runs?token=${apiToken}`,
          input,
          {
            headers: { 
              'Content-Type': 'application/json',
              'User-Agent': 'Tatu.Ink/1.0'
            },
            timeout: 8000 // 8 segundos de timeout
          }
        );
        
        const runId = startRunResponse.data.id;
        console.log(`[${debugId}] ✓ Ejecución iniciada con ID: ${runId}`);
        
        // Intentar obtener resultados inmediatamente (puede que ya estén disponibles)
        try {
          console.log(`[${debugId}] 🔄 Intentando obtener resultados inmediatos...`);
          const datasetResponse = await axios.get(
            `https://api.apify.com/v2/actor-runs/${runId}/dataset/items?token=${apiToken}`,
            {
              timeout: 5000 // 5 segundos de timeout
            }
          );
          
          const items = datasetResponse.data;
          
          if (Array.isArray(items) && items.length > 0) {
            console.log(`[${debugId}] ✅ Datos alternativos recibidos correctamente`);
            
            const profileData = items[0] as ApifyProfileData;
            
            if (profileData && profileData.profilePicUrl) {
              const result: InstagramProfileData = {
                profile_pic_url: profileData.profilePicUrl,
                profile_pic_url_hd: profileData.profilePicUrl,
                username: profileData.username || cleanUsername
              };
              
              console.log(`[${debugId}] ✅ Perfil alternativo encontrado para ${cleanUsername}`);
              return result;
            }
          }
          
          console.log(`[${debugId}] ⚠️ No se encontraron resultados inmediatos, el proceso continuará en segundo plano`);
        } catch (datasetError) {
          console.log(`[${debugId}] ⚠️ No se pudieron obtener resultados inmediatos: ${(datasetError as Error).message}`);
        }
        
        // Registrar el ID de ejecución para consulta posterior
        console.log(`[${debugId}] 📝 Registrando ID de ejecución ${runId} para consulta posterior`);
        // Aquí podríamos guardar el runId en Firebase para consultarlo posteriormente
        
        // Como no podemos esperar más tiempo en el contexto de Vercel, retornamos null
        return null;
      } catch (alternativeError) {
        console.error(`[${debugId}] ❌ Error en el método alternativo:`, alternativeError);
        return null;
      }
    }
  } catch (error) {
    console.error(`[${debugId}] ❌ Error general al obtener perfil de Instagram:`, error);
    console.error(`[${debugId}] ❌ Stack trace:`, (error as Error).stack);
    return null;
  }
}

/**
 * OBSOLETO: Simula la interfaz de instagramdp para mantener compatibilidad
 * @deprecated Este objeto ha sido reemplazado por la implementación directa con Unipile
 */
export const apifyInstagramCompat = {
  getProfilePicture: async (username: string): Promise<InstagramProfileData> => {
    console.log(`🔍 [APIFY-COMPAT] Iniciando búsqueda de perfil para: ${username}`);
    try {
      const profileData = await getInstagramProfileWithApify(username);
      
      if (!profileData) {
        console.error(`❌ [APIFY-COMPAT] No se pudo obtener la foto de perfil para ${username} - getInstagramProfileWithApify devolvió null`);
        throw new Error(`No se pudo obtener la foto de perfil para ${username}`);
      }
      
      console.log(`✅ [APIFY-COMPAT] Perfil obtenido exitosamente para: ${username}`);
      return profileData;
    } catch (error) {
      console.error(`❌ [APIFY-COMPAT] Error al obtener perfil:`, error);
      throw error;
    }
  }
};

export default apifyInstagramCompat;
