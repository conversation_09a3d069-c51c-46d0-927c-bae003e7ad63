/**
 * Implementación exacta de instagram-pfp-downloader para obtener fotos de perfil de Instagram
 * https://github.com/edizbaha/instagram-pfp-downloader
 */
import axios from 'axios';

// Interfaz para mantener compatibilidad con instagramdp
interface ProfilePictureResult {
  profile_pic_url_hd?: string;
  profile_pic_url?: string;
  username?: string;
}

/**
 * Obtiene la URL de la imagen de perfil de un usuario de Instagram
 * @param username Nombre de usuario de Instagram
 * @returns Objeto con las URLs de la imagen de perfil o null si no se encuentra
 */
export async function getInstagramProfilePicture(username: string): Promise<ProfilePictureResult | null> {
  const debugId = Date.now().toString().slice(-6);
  console.log(`[${debugId}] 🔍 Iniciando búsqueda de foto de perfil para: ${username}`);
  
  try {
    // Limpiar el nombre de usuario
    const cleanUsername = username.replace('@', '').trim();
    console.log(`[${debugId}] 👤 Username limpio: ${cleanUsername}`);
    
    // Instagram API endpoint y user agent exactamente como en instagram-pfp-downloader
    const apiUrl = `https://i.instagram.com/api/v1/users/web_profile_info/?username=${cleanUsername}`;
    const userAgent = "Instagram 337.0.0.0.77 Android (28/9; 420dpi; 1080x1920; samsung; SM-G611F; on7xreflte; samsungexynos7870; en_US; 493419337)";
    
    console.log(`[${debugId}] 🌐 Realizando solicitud a la API oficial de Instagram`);
    
    // Hacer la solicitud a la API de Instagram con el User-Agent exacto
    const response = await axios.get(apiUrl, {
      headers: {
        'User-Agent': userAgent,
        'Accept': '*/*',
        'Accept-Language': 'en-US,en;q=0.5',
        'X-IG-App-ID': '936619743392459',
        'X-ASBD-ID': '198387',
        'X-IG-WWW-Claim': '0',
        'Origin': 'https://www.instagram.com',
        'Connection': 'keep-alive',
        'Referer': 'https://www.instagram.com/',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-site'
      }
    });
    
    console.log(`[${debugId}] ✅ Respuesta recibida de la API de Instagram`);
    
    // Extraer la URL de la imagen de perfil HD exactamente como en instagram-pfp-downloader
    if (response.data && response.data.data && response.data.data.user) {
      const profile_pic_url_hd = response.data.data.user.profile_pic_url_hd;
      const profile_pic_url = response.data.data.user.profile_pic_url;
      console.log(`[${debugId}] 🖼️ URL de imagen encontrada: ${profile_pic_url_hd || profile_pic_url}`);
      return { profile_pic_url_hd, profile_pic_url };
    }
    
    console.log(`[${debugId}] ⚠️ No se encontró la estructura de datos esperada en la respuesta`);
    console.log(`[${debugId}] 📦 Estructura de respuesta:`, JSON.stringify({
      hasData: !!response.data,
      hasDataData: !!(response.data && response.data.data),
      hasUser: !!(response.data && response.data.data && response.data.data.user)
    }));
    
    // Si no se encontró la URL, intentar extraerla directamente de la respuesta
    if (response.data) {
      try {
        const responseStr = JSON.stringify(response.data);
        const profilePicRegex = /"profile_pic_url_hd":"([^"]+)"/;
        const match = responseStr.match(profilePicRegex);
        
        if (match && match[1]) {
          const profileUrl = match[1].replace(/\\u0026/g, '&').replace(/\\\//g, '/');
          console.log(`[${debugId}] 🖼️ URL de imagen encontrada mediante regex: ${profileUrl}`);
          return { profile_pic_url_hd: profileUrl, profile_pic_url: profileUrl };
        }
        
        // Intentar con URL no HD
        const profilePicRegexNonHD = /"profile_pic_url":"([^"]+)"/;
        const matchNonHD = responseStr.match(profilePicRegexNonHD);
        
        if (matchNonHD && matchNonHD[1]) {
          const profileUrl = matchNonHD[1].replace(/\\u0026/g, '&').replace(/\\\//g, '/');
          console.log(`[${debugId}] 🖼️ URL de imagen no-HD encontrada mediante regex: ${profileUrl}`);
          return { profile_pic_url: profileUrl };
        }
      } catch (regexError) {
        console.error(`[${debugId}] ❌ Error al extraer URL mediante regex: ${regexError}`);
      }
    }
    
    return null;
  } catch (error) {
    console.error(`[${debugId}] ❌ Error al obtener la foto de perfil: ${error}`);
    
    // Intentar método alternativo: scraping directo de la página de Instagram
    try {
      const cleanUsername = username.replace('@', '').trim();
      console.log(`[${debugId}] 🌐 Intentando método alternativo: scraping de página`);
      
      const response = await axios.get(`https://www.instagram.com/${cleanUsername}/`, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
          'Cache-Control': 'max-age=0'
        }
      });
      
      // Buscar la URL de la imagen de perfil en el HTML
      const html = response.data;
      const profilePicRegex = /"profile_pic_url_hd":"([^"]+)"/;
      const match = html.match(profilePicRegex);
      
      if (match && match[1]) {
        // Decodificar la URL (Instagram usa caracteres escapados)
        const profileUrl = match[1].replace(/\\u0026/g, '&').replace(/\\\//g, '/');
        console.log(`[${debugId}] 🖼️ URL de imagen encontrada mediante scraping: ${profileUrl}`);
        return { profile_pic_url_hd: profileUrl, profile_pic_url: profileUrl };
      }
      
      // Intentar con URL no HD
      const profilePicRegexNonHD = /"profile_pic_url":"([^"]+)"/;
      const matchNonHD = html.match(profilePicRegexNonHD);
      
      if (matchNonHD && matchNonHD[1]) {
        const profileUrl = matchNonHD[1].replace(/\\u0026/g, '&').replace(/\\\//g, '/');
        console.log(`[${debugId}] 🖼️ URL de imagen no-HD encontrada mediante scraping: ${profileUrl}`);
        return { profile_pic_url: profileUrl };
      }
      
      console.log(`[${debugId}] ⚠️ No se encontró URL de imagen mediante scraping`);
      return null;
    } catch (scrapingError) {
      console.error(`[${debugId}] ❌ Error en método de scraping: ${scrapingError}`);
      return null;
    }
  }
}

/**
 * Tipo para la respuesta de la API de Instagram
 */
export interface InstagramProfileData {
  profile_pic_url: string;
  profile_pic_url_hd?: string;
  username: string;
}

/**
 * Simula la interfaz de instagramdp para mantener compatibilidad
 */
export const instagramdpCompat = {
  getProfilePicture: async (username: string): Promise<InstagramProfileData> => {
    const profilePicData = await getInstagramProfilePicture(username);
    
    if (!profilePicData) {
      throw new Error(`No se pudo obtener la foto de perfil para ${username}`);
    }
    
    return {
      profile_pic_url: profilePicData.profile_pic_url || profilePicData.profile_pic_url_hd || '',
      profile_pic_url_hd: profilePicData.profile_pic_url_hd || profilePicData.profile_pic_url || '',
      username: username
    };
  }
};

export default instagramdpCompat;
