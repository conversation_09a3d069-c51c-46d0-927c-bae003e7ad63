import { Timestamp } from 'firebase/firestore';

export type MessageDirection = 'incoming' | 'outgoing';
export type MessageStatus = 'sent' | 'delivered' | 'read' | 'failed';
export type Platform = 'facebook' | 'instagram' | 'whatsapp';

export interface Attachment {
  type?: string;
  url: string;
}

export interface ConversationMessage {
  id: string;
  content: string;
  timestamp: number;
  sender: 'user' | 'business';
  read: boolean;
  messageId?: string;
  attachments?: (string | Attachment)[];
  platform?: Platform; // Plataforma del mensaje
  contactId?: string; // ID del contacto en Superchat (para mensajes entrantes)
}

export interface Conversation {
  id: string;
  pageId: string;
  platform: Platform;
  lastMessage?: {
    content: string;
    timestamp: number;
    sender: 'user' | 'business';
  };
  lastMessageAt?: number;
  unread: boolean;
  unreadCount: number;
  clientId?: string;
  participant?: ConversationParticipant; // Información del participante
  participants?: ConversationParticipant[]; // Lista de participantes (para grupos)
  participantId?: string; // ID del participante
  metaSenderId?: string; // ID del remitente en Meta (usado para enviar mensajes)
  superchatContactId?: string; // ID del contacto en Superchat (usado para enviar mensajes)
  participantName?: string; // Nombre del participante
  isSuperchat?: boolean; // Indicador explícito de si es una conversación de Superchat
  provider?: string; // Proveedor de la conversación (superchat, sinch, etc)
  attendeeId?: string; // ID del participante en Unipile
  profilePictureUrl?: string; // URL de la imagen de perfil del participante
  participantPicture?: string; // URL alternativa de la imagen de perfil (usado por el webhook)
  profilePictureUpdatedAt?: number; // Timestamp de la última actualización de la imagen de perfil
  profilePicUrl?: string; // URL antigua de la imagen de perfil (formato anterior)
  profilePicUpdatedAt?: string; // Timestamp antiguo de actualización (formato anterior)
  lastImageUpdate?: number; // Timestamp adicional para forzar actualización de imagen
  profilePictureSuccess?: boolean; // Indicador de si la obtención de la imagen fue exitosa
  profilePictureError?: string; // Error al obtener la imagen de perfil
  profilePictureAttempts?: number; // Número de intentos de obtener la imagen
  lastProfileAttempt?: number; // Timestamp del último intento de obtener la imagen
  superchatChannelId?: string; // ID del canal de Superchat para esta conversación
}

export interface ConversationParticipant {
  id: string;
  name: string;
  profilePic?: string;
  profilePictureUrl?: string; // Alias para profilePic, usado en algunos componentes
}
