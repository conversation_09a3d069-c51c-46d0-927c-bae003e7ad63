export enum NotificationType {
  AI_CLIENT_MATCH = 'AI_CLIENT_MATCH',
  SESSION_TODAY = 'SESSION_TODAY',
}

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  read: boolean;
  createdAt: number; // Timestamp en ms
  metadata?: {
    clientId?: string;
    sessionId?: string;
    clientName?: string;
    sessionTime?: string;
    matchReason?: string;
    conversationId?: string;
    // Otros metadatos específicos según el tipo de notificación
  };
}

// Estructura para la tabla en Realtime Database
export interface NotificationsData {
  [userId: string]: {
    [notificationId: string]: Notification;
  };
}

// Interfaz para el contexto de notificaciones
export interface NotificationsContextType {
  notifications: Notification[];
  unreadCount: number;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
} 