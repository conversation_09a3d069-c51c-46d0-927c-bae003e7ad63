'use client'

import { useState } from 'react'
import { useTransactions } from './TransactionsContext'
import { formatPrice } from '@/app/utils/formatPrice'
import { X } from '@phosphor-icons/react'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip as ChartTooltip,
  Legend
} from 'chart.js'
import { Bar } from 'react-chartjs-2'
import { useSettings } from '@/app/components/settings/SettingsContext'
import ModalPortal from '../customize/ModalPortal'

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  ChartTooltip,
  Legend
)

type TimeRange = 'day' | 'week' | 'month' | 'year' | 'all'

export default function FinancialSummary() {
  const [timeRange, setTimeRange] = useState<TimeRange>('month')
  const [showTransfersModal, setShowTransfersModal] = useState(false)
  const { transactions } = useTransactions()
  const { settings } = useSettings()
  const taxRate = settings.personal.taxRate || 19 // Usar la tasa de IVA configurada por el usuario o 19% por defecto

  const calculateSummary = () => {
    const now = new Date()
    
    console.log('Todas las transacciones:', transactions.length);
    
    // Filtrar transacciones por rango de tiempo
    const filteredTransactions = transactions.filter(t => {
      // Asegurarse de que la fecha es válida
      if (!t.date) {
        console.warn('Transacción sin fecha:', t);
        return false;
      }
      
      let transactionDate;
      try {
        transactionDate = new Date(t.date);
        // Verificar si la fecha es válida
        if (isNaN(transactionDate.getTime())) {
          console.warn('Fecha inválida para transacción:', t);
          return false;
        }
      } catch (error) {
        console.error('Error al parsear fecha:', t.date, error);
        return false;
      }
      
      switch (timeRange) {
        case 'day':
          return (
            transactionDate.getFullYear() === now.getFullYear() &&
            transactionDate.getMonth() === now.getMonth() &&
            transactionDate.getDate() === now.getDate()
          )
        case 'week':
          const weekAgo = new Date(now);
          weekAgo.setDate(now.getDate() - 7);
          return transactionDate >= weekAgo;
        case 'month':
          const isCurrentMonth = (
            transactionDate.getMonth() === now.getMonth() &&
            transactionDate.getFullYear() === now.getFullYear()
          );
          return isCurrentMonth;
        case 'year':
          return transactionDate.getFullYear() === now.getFullYear();
        case 'all':
          return true;
        default:
          return true;
      }
    });
    
    console.log(`Transacciones filtradas (${timeRange}):`, filteredTransactions.length);

    // Calcular totales
    const summary = filteredTransactions.reduce(
      (acc, transaction) => {
        // Validar que el monto sea un número
        const amount = typeof transaction.amount === 'number' ? transaction.amount : 
                      (parseFloat(transaction.amount as any) || 0);
        
        if (transaction.type === 'income') {
          if (transaction.status === 'completed') {
            acc.totalIncome += amount;
            acc.incomesCount++;
            console.log(`Ingreso sumado: ${amount} - ${transaction.description}`);
          } else {
            acc.pendingIncome += amount;
          }
        } else if (transaction.type === 'expense') {
          if (transaction.status === 'completed') {
            acc.totalExpenses += amount;
            acc.expensesCount++;
            console.log(`Gasto sumado: ${amount} - ${transaction.description}`);
          } else {
            acc.pendingExpenses += amount;
          }
        }
        return acc;
      },
      {
        totalIncome: 0,
        totalExpenses: 0,
        pendingIncome: 0,
        pendingExpenses: 0,
        incomesCount: 0,
        expensesCount: 0
      }
    );

    console.log('Resumen calculado:', {
      ingresos: `${summary.totalIncome} (${summary.incomesCount} transacciones)`,
      gastos: `${summary.totalExpenses} (${summary.expensesCount} transacciones)`
    });

    // Obtener transferencias únicas por período
    const uniqueTransfers = new Set(
      filteredTransactions
        .filter(t => t.type === 'income' && t.status === 'completed' && t.paymentMethod === 'transfer')
        .map(t => t.clientId || t.description || 'custom-payment')
    ).size;

    // Obtener transferencias únicas por semestre
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(now.getMonth() - 6);
    
    const semesterTransfers = new Set(
      transactions
        .filter(t => {
          try {
            const date = new Date(t.date);
            return t.type === 'income' && 
                   t.status === 'completed' && 
                   t.paymentMethod === 'transfer' &&
                   date >= sixMonthsAgo;
          } catch (error) {
            return false;
          }
        })
        .map(t => t.clientId || t.description || 'custom-payment')
    ).size;

    // Determinar si se exceden los límites
    const limits = {
      day: uniqueTransfers >= 50 && timeRange === 'day',
      week: uniqueTransfers >= 50 && timeRange === 'week',
      month: uniqueTransfers >= 50 && timeRange === 'month',
      semester: semesterTransfers >= 100
    };

    // Determinar recomendación tributaria
    const shouldPayTaxes = semesterTransfers >= 100 || 
                          (timeRange === 'month' && uniqueTransfers >= 50) ||
                          summary.totalIncome >= 1000000; // Umbral de 1M mensual

    // Preparar datos para el gráfico comparativo
    const chartData = {
      labels: ['Mes Actual', 'Semestre'],
      datasets: [
        {
          label: 'Transferencias Únicas',
          data: [uniqueTransfers, semesterTransfers],
          backgroundColor: ['rgba(59, 130, 246, 0.5)', 'rgba(147, 51, 234, 0.5)'],
          borderColor: ['rgb(59, 130, 246)', 'rgb(147, 51, 234)'],
          borderWidth: 1
        }
      ]
    };

    const chartOptions = {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Número de Transferencias'
          }
        }
      },
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          callbacks: {
            label: function(context: any) {
              return `${context.raw} transferencias únicas`;
            }
          }
        }
      }
    };

    // Calcular el IVA usando la tasa configurada por el usuario
    const iva = summary.totalIncome * (taxRate / 100);

    return {
      ...summary,
      netIncome: summary.totalIncome - summary.totalExpenses,
      pendingNet: summary.pendingIncome - summary.pendingExpenses,
      iva,
      taxRate, // Agregar la tasa de IVA al resultado
      uniqueTransfers,
      semesterTransfers,
      limits,
      shouldPayTaxes,
      chartData,
      chartOptions
    };
  }

  const summary = calculateSummary()

  const timeRangeLabels: Record<TimeRange, string> = {
    day: 'Hoy',
    week: 'Esta Semana',
    month: 'Este Mes',
    year: 'Este Año',
    all: 'Todo'
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
          Resumen Financiero
        </h2>
        <select
          value={timeRange}
          onChange={(e) => setTimeRange(e.target.value as TimeRange)}
          className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
        >
          {Object.entries(timeRangeLabels).map(([value, label]) => (
            <option key={value} value={value}>
              {label}
            </option>
          ))}
        </select>
      </div>

      {/* Recomendación Tributaria */}
      <div className={`p-4 mb-6 rounded-lg ${summary.shouldPayTaxes ? 'bg-yellow-50 border border-yellow-200' : 'bg-green-50 border border-green-200'}`}>
        <div className="flex flex-col">
          <div>
            <h3 className={`text-lg font-medium mb-2 ${summary.shouldPayTaxes ? 'text-yellow-800' : 'text-green-800'}`}>
              Recomendación Tributaria
            </h3>
            <p className={`text-sm ${summary.shouldPayTaxes ? 'text-yellow-700' : 'text-green-700'}`}>
              {summary.shouldPayTaxes ? (
                <>
                  🚨 Según tus ingresos y número de transferencias, deberías:
                  <ul className="list-disc ml-5 mt-2">
                    <li>Iniciar actividades en el SII</li>
                    <li>Emitir boletas por tus servicios</li>
                    <li>Declarar IVA mensualmente</li>
                    <li>Considerar asesoría contable</li>
                  </ul>
                </>
              ) : (
                '✅ Por el momento, no es necesario que inicies actividades en el SII. Mantén un registro de tus ingresos.'
              )}
            </p>
          </div>
          <button
            onClick={() => setShowTransfersModal(true)}
            className="mt-3 self-start px-3 py-1.5 text-sm font-medium text-blue-700 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
          >
            Ver Transferencias
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="p-4 bg-emerald-50 border border-emerald-200 rounded-lg">
          <h3 className="text-sm font-medium text-emerald-800 mb-2">
            Ingresos Totales
          </h3>
          <p className="text-2xl font-bold text-emerald-700">
            {formatPrice(summary.totalIncome)}
          </p>
        </div>

        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <h3 className="text-sm font-medium text-red-800 mb-2">
            Gastos Totales
          </h3>
          <p className="text-2xl font-bold text-red-700">
            {formatPrice(summary.totalExpenses)}
          </p>
        </div>

        <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
          <h3 className="text-sm font-medium text-amber-800 mb-2">
            IVA Estimado ({taxRate}%)
          </h3>
          <p className="text-2xl font-bold text-amber-700">
            {formatPrice(summary.iva)}
          </p>
        </div>

        <div className="p-4 bg-indigo-50 border border-indigo-200 rounded-lg">
          <h3 className="text-sm font-medium text-indigo-800 mb-2">
            Ingreso Neto
          </h3>
          <p className="text-2xl font-bold text-indigo-700">
            {formatPrice(summary.netIncome)}
          </p>
        </div>
      </div>

      {/* Modal de Transferencias */}
      {showTransfersModal && (
        <ModalPortal>
          <div className="bg-black bg-opacity-50 flex items-center justify-center h-full w-full overflow-y-auto py-10">
            <div className="bg-white rounded-lg shadow-xl max-w-3xl lg:max-w-4xl xl:max-w-5xl w-full mx-4 sm:mx-6 md:mx-8 p-4 sm:p-6 my-auto">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-medium text-gray-900">
                  Resumen de Transferencias
                </h3>
                <button
                  onClick={() => setShowTransfersModal(false)}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <X size={24} />
                </button>
              </div>

              <div className="space-y-6 overflow-y-auto max-h-[calc(100vh-200px)]">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <h4 className="text-sm font-medium text-blue-800 mb-2">Este Mes</h4>
                    <p className="text-2xl font-bold text-blue-700">{summary.uniqueTransfers}</p>
                    <p className="text-sm text-blue-600 mt-1">transferencias únicas</p>
                    {summary.limits.month && (
                      <p className="mt-2 text-sm text-red-600">
                        ⚠️ Deberás pagar impuestos
                      </p>
                    )}
                  </div>

                  <div className="p-4 bg-purple-50 rounded-lg">
                    <h4 className="text-sm font-medium text-purple-800 mb-2">Este Semestre</h4>
                    <p className="text-2xl font-bold text-purple-700">{summary.semesterTransfers}</p>
                    <p className="text-sm text-purple-600 mt-1">transferencias únicas</p>
                    {summary.limits.semester && (
                      <p className="mt-2 text-sm text-red-600">
                        ⚠️ Deberás pagar impuestos
                      </p>
                    )}
                  </div>
                </div>

                <div className="h-72 lg:h-80 bg-white rounded-lg p-4 border border-gray-200">
                  <Bar data={summary.chartData} options={summary.chartOptions} />
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-800 mb-2">Umbrales de Tributación SII</h4>
                  <ul className="space-y-2 text-sm text-gray-600">
                    <li>• Si recibes más de 50 transferencias únicas por mes, deberás pagar impuestos</li>
                    <li>• Si recibes más de 100 transferencias únicas por semestre, deberás pagar impuestos</li>
                    <li>• Estos límites son umbrales para determinar la obligación tributaria</li>
                  </ul>
                </div>
              </div>

              <div className="mt-6 flex justify-end">
                <button
                  onClick={() => setShowTransfersModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md"
                >
                  Cerrar
                </button>
              </div>
            </div>
          </div>
        </ModalPortal>
      )}
    </div>
  )
}
