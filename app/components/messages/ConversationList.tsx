import React, { useEffect } from 'react';
import { useConversations } from '@/contexts/ConversationsContext';
import { useAuth } from '@/contexts/AuthContext';
import { formatDistanceToNow } from 'date-fns';
import { es } from 'date-fns/locale';
import { syncProfileImages } from '@/app/services/profile-image-sync';
import { FaFacebookMessenger, FaInstagram, FaWhatsapp } from 'react-icons/fa';
import { Conversation, Platform } from '@/app/types/conversations';

export default function ConversationList() {
  const { conversations, selectedConversation, selectConversation, loading } = useConversations();
  const { user } = useAuth();

  const handleSelectConversation = (conversation: Conversation) => {
    selectConversation(conversation);
  };

  const getPlatformIcon = (platform: Platform) => {
    switch (platform) {
      case 'facebook':
        return <FaFacebookMessenger className="text-blue-500" />;
      case 'instagram':
        return <FaInstagram className="text-pink-500" />;
      case 'whatsapp':
        return <FaWhatsapp className="text-green-500" />;
      default:
        return null;
    }
  };

  const getParticipantName = (conversation: Conversation) => {
    // Si hay un nombre de participante, usarlo
    if (conversation.participant?.name) {
      return conversation.participant.name;
    }

    // Si hay un nombre en la conversación, usarlo
    if (conversation.participantName) {
      return conversation.participantName;
    }

    // Si hay un ID de cliente asociado, intentar usar eso
    if (conversation.clientId) {
      // Extraer el nombre del cliente del ID si es posible
      const parts = conversation.clientId.split('_');
      if (parts.length > 1) {
        return `Cliente ${parts[1].substring(0, 6)}`;
      }
      return `Cliente ${conversation.clientId.substring(0, 6)}`;
    }

    // Si hay un ID de participante, usarlo
    if (conversation.participantId) {
      return `Cliente ${conversation.participantId.substring(0, 6)}`;
    }

    // Usar la plataforma como prefijo si está disponible
    if (conversation.platform) {
      const platformNames: Record<Platform, string> = {
        'facebook': 'Facebook',
        'instagram': 'Instagram',
        'whatsapp': 'WhatsApp'
      };

      const platformName = platformNames[conversation.platform] || 'Cliente';

      // Usar los primeros 6 caracteres del ID de la conversación
      if (conversation.id) {
        return `${platformName} ${conversation.id.substring(0, 6)}`;
      }

      return platformName;
    }

    // Último recurso
    return 'Cliente sin nombre';
  };

  const getParticipantImage = (conversation: Conversation) => {
    // Crear objeto de información de depuración
    const debugInfo = {
      conversationId: conversation.id,
      hasParticipant: !!conversation.participant,
      hasProfilePictureUrl: !!conversation.profilePictureUrl,
      hasParticipantPicture: !!conversation.participantPicture,
      profilePictureUrl: conversation.profilePictureUrl,
      participantPicture: conversation.participantPicture,
      participantProfilePic: conversation.participant?.profilePic,
      profilePictureUpdatedAt: conversation.profilePictureUpdatedAt || 'N/A',
      lastImageUpdate: conversation.lastImageUpdate || 'N/A',
      platform: conversation.platform || 'desconocida',
      isInstagram: conversation.platform === 'instagram'
    };
    
    // Mostrar información de depuración en la consola
    console.log(`💾 [ConversationList] Imagen de perfil para conversación ${debugInfo.conversationId}:`, debugInfo);

    // Para Instagram, priorizar profilePictureUrl que es donde guarda Apify
    if (conversation.platform === 'instagram' && conversation.profilePictureUrl) {
      console.log(`📸 [${debugInfo.conversationId}] Usando profilePictureUrl para Instagram:`, conversation.profilePictureUrl);
      // Añadir timestamp para evitar caché del navegador
      const timestamp = conversation.profilePictureUpdatedAt || conversation.lastImageUpdate || Date.now();
      const cacheBuster = `?t=${timestamp}`;
      const imageUrl = `${conversation.profilePictureUrl}${cacheBuster}`;
      console.log(`🖼️ URL final con anti-caché: ${imageUrl}`);
      return imageUrl;
    }

    // Buscar la imagen en todas las posibles ubicaciones
    if (conversation.participant?.profilePic) {
      console.log(`✅ [${debugInfo.conversationId}] Usando participant.profilePic:`, conversation.participant.profilePic);
      return conversation.participant.profilePic;
    }

    // Buscar en profilePictureUrl (usado por el webhook de Unipile con el nuevo nombre)
    if (conversation.profilePictureUrl) {
      console.log(`✅ [${debugInfo.conversationId}] Usando profilePictureUrl:`, conversation.profilePictureUrl);
      // Añadir timestamp para evitar caché del navegador
      const cacheBuster = `?t=${conversation.profilePictureUpdatedAt || Date.now()}`;
      return `${conversation.profilePictureUrl}${cacheBuster}`;
    }
    
    // Buscar en profilePicUrl (usado por el webhook de Unipile con el nombre antiguo)
    if (conversation.profilePicUrl) {
      console.log(`✅ [${debugInfo.conversationId}] Usando profilePicUrl:`, conversation.profilePicUrl);
      // Añadir timestamp para evitar caché del navegador
      const cacheBuster = `?t=${conversation.profilePicUpdatedAt || Date.now()}`;
      return `${conversation.profilePicUrl}${cacheBuster}`;
    }

    // Buscar en participantPicture (también usado por el webhook)
    if (conversation.participantPicture) {
      console.log(`✅ [${debugInfo.conversationId}] Usando participantPicture:`, conversation.participantPicture);
      // Añadir timestamp para evitar caché del navegador
      const cacheBuster = `?t=${conversation.profilePictureUpdatedAt || Date.now()}`;
      return `${conversation.participantPicture}${cacheBuster}`;
    }

    // Buscar en participant.profilePictureUrl (otra posible ubicación)
    if (conversation.participant?.profilePictureUrl) {
      console.log(`✅ [${debugInfo.conversationId}] Usando participant.profilePictureUrl:`, conversation.participant.profilePictureUrl);
      return conversation.participant.profilePictureUrl;
    }

    // Si no se encuentra en ninguna ubicación, usar imagen predeterminada
    console.log(`⚠️ [${debugInfo.conversationId}] No se encontró imagen de perfil, usando predeterminada`);
    return 'https://via.placeholder.com/40';
  };

  if (loading) {
    return (
      <div className="flex flex-col h-full items-center justify-center p-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        <p className="mt-2 text-sm text-gray-600">Cargando conversaciones...</p>
      </div>
    );
  }

  // Ejecutar sincronización automática al cargar el componente
  useEffect(() => {
    const runAutoSync = async () => {
      if (!user?.uid) return;
      
      console.log('🔄 Iniciando sincronización automática de imágenes de perfil...');
      try {
        await syncProfileImages(user.uid);
        console.log('✅ Sincronización automática completada con éxito');
      } catch (error) {
        console.error('❌ Error en sincronización automática:', error);
      }
    };
    
    runAutoSync();
  }, [user]);

  if (conversations.length === 0) {
    return (
      <div className="flex flex-col h-full items-center justify-center p-4">
        <p className="text-gray-500">No hay conversaciones disponibles</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      <div className="overflow-y-auto flex-grow">
        {conversations.map((conversation) => (
          <div
            key={conversation.id}
            className={`flex items-center p-3 border-b cursor-pointer hover:bg-gray-50 transition-colors ${
              selectedConversation?.id === conversation.id ? 'bg-blue-50' : ''
            }`}
            onClick={() => handleSelectConversation(conversation)}
          >
          <div className="relative">
            {/* Usamos useEffect con llave vacía para ejecutar los logs solo una vez por renderizado */}
            <img
              src={(() => {
                // Logs de depuración fuera del JSX
                const imgUrl = getParticipantImage(conversation);
                console.log(`🧠 [DEBUG] Conversación ${conversation.id}:`, JSON.stringify(conversation));
                console.log(`🔍 [IMAGE] URL para ${conversation.id}: ${imgUrl}`);
                console.log(`🔢 [KEYS] Propiedades: ${Object.keys(conversation).join(', ')}`);
                return imgUrl;
              })()}
              alt={getParticipantName(conversation)}
              className="w-12 h-12 rounded-full object-cover"
              data-conversation-id={conversation.id}
              onError={(e) => {
                console.error(`🚫 Error al cargar imagen para ${conversation.id}:`, e);
                e.currentTarget.src = 'https://via.placeholder.com/40';
              }}
            />
            <div className="absolute -bottom-1 -right-1 bg-white rounded-full p-0.5">
              {getPlatformIcon(conversation.platform)}
            </div>
            {/* Indicador de mensaje no leído */}
            {conversation.unread && conversation.unreadCount > 0 && selectedConversation?.id !== conversation.id && (
              <div className="absolute top-0 right-0 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center border-2 border-white">
                <span className="text-[10px] font-bold text-white">{conversation.unreadCount}</span>
              </div>
            )}
          </div>
          <div className="ml-3 flex-1 min-w-0">
            <div className="flex justify-between items-center">
              <h3 className="text-sm font-medium text-gray-900 truncate">
                {getParticipantName(conversation)}
              </h3>
              {conversation.lastMessageAt && (
                <span className="text-xs text-gray-500">
                  {formatDistanceToNow(conversation.lastMessageAt, { locale: es, addSuffix: true })}
                </span>
              )}
            </div>
            <div className="flex justify-between items-center mt-1">
              <div className="flex flex-col">
                <p className="text-xs text-gray-500 truncate">
                  {conversation.lastMessage?.content || 'Sin mensajes'}
                </p>
              </div>
              {conversation.unread && conversation.unreadCount > 0 && selectedConversation?.id !== conversation.id && (
                <span className="inline-flex items-center justify-center min-w-5 h-5 px-1.5 text-xs font-bold text-white bg-red-500 rounded-full shadow-md">
                  {conversation.unreadCount}
                </span>
              )}
            </div>
          </div>
          </div>
        ))}
      </div>
    </div>
  );
}