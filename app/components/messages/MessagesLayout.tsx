'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'next/navigation';
import { ConversationsProvider, useConversations } from '../../contexts/ConversationsContext';
import { ClientProvider } from '../clients/ClientContext';
import { ArrowLeft } from '@phosphor-icons/react';
import MessagesList from './MessagesList';
import MessageView from './MessageView';
import LoadingSpinner from '../common/LoadingSpinner';
import type { Conversation } from '../../types/conversations';
import AIClientNotificationsInfo from './AIClientNotificationsInfo';

function MessagesContent() {
  const searchParams = useSearchParams();
  const {
    conversations,
    selectedConversation,
    messages,
    loading,
    error,
    selectConversation,
    sendMessage,
    loadMoreMessages,
    markAsRead,
  } = useConversations();

  const [showTools, setShowTools] = useState(false);
  const [replyText, setReplyText] = useState('');
  const [isClient, setIsClient] = useState(false);
  const [hasProcessedUrlParam, setHasProcessedUrlParam] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Efecto para manejar el parámetro de conversación en la URL
  useEffect(() => {
    if (!isClient || hasProcessedUrlParam || !conversations.length) return;

    const conversationId = searchParams.get('conversation');
    if (conversationId) {
      console.log('🔍 Parámetro conversation encontrado en URL:', conversationId);

      // Buscar la conversación en la lista
      const targetConversation = conversations.find(conv => conv.id === conversationId);

      if (targetConversation) {
        console.log('✅ Conversación encontrada, seleccionando automáticamente:', targetConversation.participantName);
        selectConversation(targetConversation);
        setHasProcessedUrlParam(true);
      } else {
        console.log('⚠️ Conversación no encontrada en la lista:', conversationId);
        // Marcar como procesado para evitar bucles infinitos
        setHasProcessedUrlParam(true);
      }
    } else {
      setHasProcessedUrlParam(true);
    }
  }, [isClient, conversations, searchParams, selectConversation, hasProcessedUrlParam]);

  const handleSelectConversation = useCallback(async (conversation: Conversation) => {
    await selectConversation(conversation);
  }, [selectConversation]);

  const handleBack = useCallback(() => {
    selectConversation(null);
  }, [selectConversation]);

  const handleMarkAsRead = useCallback(async () => {
    if (selectedConversation?.id) {
      await markAsRead(selectedConversation.id);
    }
  }, [selectedConversation, markAsRead]);

  const handleTemplateSelect = useCallback((content: string) => {
    setReplyText(content);
  }, []);

  if (!isClient) {
    return <LoadingSpinner />;
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-red-500 p-4 rounded-lg bg-red-100 dark:bg-red-900/20">
          {error}
          <button 
            className="block mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            onClick={() => window.location.reload()}
          >
            Reintentar
          </button>
        </div>
      </div>
    );
  }

  const isMobileView = selectedConversation !== null && selectedConversation.id !== '';

  return (
    <div className="h-full border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
      <div className="flex h-full">
        <div className={`${isMobileView ? 'hidden sm:block' : 'w-full'} sm:w-80 md:w-96 h-full border-r border-gray-200 dark:border-gray-700 overflow-y-auto`}>
          <div className="h-full flex flex-col">
            <div className="flex-1 overflow-y-auto">
              <MessagesList onSelect={handleSelectConversation} />
            </div>
          </div>
        </div>
        
        <div className={`${!isMobileView ? 'hidden sm:flex' : 'w-full'} flex-1 h-full overflow-hidden`}>
          {selectedConversation && selectedConversation.id ? (
            <div className="flex flex-col h-full w-full">
              <div className="sm:hidden p-3 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
                <button 
                  onClick={handleBack}
                  className="flex items-center text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
                >
                  <ArrowLeft className="w-5 h-5 mr-2" />
                  <span>Volver a conversaciones</span>
                </button>
              </div>
              
              <div className="flex-1 overflow-hidden">
                <MessageView
                  conversation={selectedConversation}
                  messages={messages}
                  onSendMessage={sendMessage}
                  onLoadMore={loadMoreMessages}
                  onMarkAsRead={handleMarkAsRead}
                  showTools={showTools}
                  setShowTools={setShowTools}
                  replyText={replyText}
                  setReplyText={setReplyText}
                  onTemplateSelect={handleTemplateSelect}
                />
              </div>
            </div>
          ) : (
            <div className="flex-1 flex items-center justify-center bg-white dark:bg-gray-800">
              <div className="text-center text-gray-500 dark:text-gray-400">
                <h3 className="text-lg font-medium">Selecciona una conversación</h3>
                <p>Elige una conversación de la lista para ver los mensajes</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

const MemoizedMessagesContent = React.memo(MessagesContent, (prevProps, nextProps) => {
  return true;
});

export default function MessagesLayout() {
  return (
    <ClientProvider>
      <ConversationsProvider>
        <MemoizedMessagesContent />
      </ConversationsProvider>
    </ClientProvider>
  );
}