'use client';

import { useConversations } from '../../contexts/ConversationsContext';
import { useClients } from '../clients/ClientContext';
import { useAuth } from '@/contexts/AuthContext';
import { useState } from 'react';
import {
  WhatsappLogo,
  InstagramLogo,
  FacebookLogo,
  MagnifyingGlass,
  User,
  Trash,
  Warning
} from '@phosphor-icons/react';
import clsx from 'clsx';
import { formatDistanceToNow } from 'date-fns';
import { es } from 'date-fns/locale';
import { Conversation } from '../../types/conversations';
import AccountErrorBanner from './AccountErrorBanner';
import { db } from '@/lib/firebase/config';
import { database as rtdb } from '@/lib/firebase/config';
import { deleteDoc, doc, collection, query, where, getDocs } from 'firebase/firestore';
import { ref, remove } from 'firebase/database';
import { toast } from 'react-hot-toast';
import ModalPortal from '../customize/ModalPortal';

interface MessagesListProps {
  onSelect?: (conversation: Conversation) => void;
}

export default function MessagesList({ onSelect }: MessagesListProps) {
  const { conversations, selectedConversation, loading, selectConversation, fetchConversations } = useConversations();
  const { clients, fetchClients } = useClients();
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [conversationToDelete, setConversationToDelete] = useState<Conversation | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [associatedClient, setAssociatedClient] = useState<any | null>(null);

  // Función para obtener la imagen de perfil adecuada
  const getParticipantImage = (conversation: Conversation) => {
    // Crear objeto de información de depuración
    const debugInfo = {
      conversationId: conversation.id,
      platform: conversation.platform || 'desconocida',
      profilePictureUrl: conversation.profilePictureUrl,
      participantPicture: conversation.participantPicture,
      participantProfilePic: conversation.participant?.profilePic,
      profilePictureUpdatedAt: conversation.profilePictureUpdatedAt,
      lastImageUpdate: conversation.lastImageUpdate
    };
    
    // Mostrar información de depuración en la consola
    console.log(`🖼️ [Debug MessagesList] Imagen para ${debugInfo.conversationId}:`, debugInfo);

    // PRIORIDAD 1: Para Instagram, usar profilePictureUrl (donde Apify guarda la imagen)
    if (conversation.profilePictureUrl) {
      console.log(`📸 [${debugInfo.conversationId}] Usando profilePictureUrl:`, conversation.profilePictureUrl);
      // Añadir timestamp para evitar caché del navegador
      const timestamp = conversation.profilePictureUpdatedAt || conversation.lastImageUpdate || Date.now();
      // Verificar si la URL ya tiene un parámetro de consulta
      const hasQueryParam = conversation.profilePictureUrl.includes('?');
      const cacheBuster = hasQueryParam ? `&t=${timestamp}` : `?t=${timestamp}`;
      const imageUrl = `${conversation.profilePictureUrl}${cacheBuster}`;
      console.log(`🖼️ URL final con anti-caché: ${imageUrl}`);
      return imageUrl;
    }
    
    // PRIORIDAD 2: Si hay una imagen de participante, usarla
    if (conversation.participantPicture) {
      console.log(`📷 [${debugInfo.conversationId}] Usando participantPicture:`, conversation.participantPicture);
      return conversation.participantPicture;
    }
    
    // PRIORIDAD 3: Si hay un objeto participant con profilePic, usarlo
    if (conversation.participant?.profilePic) {
      console.log(`👤 [${debugInfo.conversationId}] Usando participant.profilePic:`, conversation.participant.profilePic);
      return conversation.participant.profilePic;
    }
    
    // PRIORIDAD 4: Otros campos de respaldo
    if (conversation.participant?.picture) {
      return conversation.participant.picture;
    }
    
    if (conversation.participant?.profilePicture) {
      return conversation.participant.profilePicture;
    }
    
    if (conversation.participant?.image) {
      return conversation.participant.image;
    }
    
    // Si no hay imagen, retornar null
    console.log(`❌ [${debugInfo.conversationId}] No se encontró imagen de perfil`);
    return null;
  };

  const filteredConversations = conversations.filter(conversation =>
    conversation.participantName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    conversation.lastMessage?.content?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const isClient = (conversation: Conversation) => {
    // Buscar si algún cliente tiene este ID de conversación
    return clients.some(client => client.conversationId === conversation.id);
  };
  
  const getAssociatedClient = (conversation: Conversation) => {
    return clients.find(client => client.conversationId === conversation.id) || null;
  };

  const handleSelect = (conversation: Conversation) => {
    console.log('MessagesList: handleSelect called with conversation:', conversation);
    selectConversation(conversation);
    if (onSelect) {
      console.log('MessagesList: calling onSelect callback');
      onSelect(conversation);
    }
  };
  
  const handleDeleteClick = (e: React.MouseEvent, conversation: Conversation) => {
    e.stopPropagation(); // Evitar que se seleccione la conversación
    const client = getAssociatedClient(conversation);
    setConversationToDelete(conversation);
    setAssociatedClient(client);
    setShowDeleteModal(true);
  };
  
  const handleCancelDelete = () => {
    setShowDeleteModal(false);
    setConversationToDelete(null);
    setAssociatedClient(null);
  };
  
  const handleConfirmDelete = async () => {
    if (!conversationToDelete || !user?.uid) return;
    
    setIsDeleting(true);
    try {
      // Eliminar la conversación de la Realtime Database
      const conversationRef = ref(rtdb, `conversations/${user.uid}/${conversationToDelete.id}`);
      await remove(conversationRef);
      
      // Eliminar mensajes asociados de la Realtime Database
      const messagesRef = ref(rtdb, `messages/${conversationToDelete.id}`);
      await remove(messagesRef);
      
      // Si hay un cliente asociado, eliminarlo de Firestore
      if (associatedClient) {
        await deleteDoc(doc(db, 'clients', associatedClient.id));
        await fetchClients(); // Actualizar lista de clientes
      }
      
      // Actualizar la lista de conversaciones
      await fetchConversations();
      
      // Si la conversación eliminada era la seleccionada, deseleccionarla
      if (selectedConversation?.id === conversationToDelete.id) {
        selectConversation(null);
      }
      
      toast.success(associatedClient 
        ? 'Conversación y cliente eliminados correctamente' 
        : 'Conversación eliminada correctamente');
    } catch (error) {
      console.error('Error al eliminar la conversación:', error);
      toast.error('Error al eliminar la conversación');
    } finally {
      setIsDeleting(false);
      setShowDeleteModal(false);
      setConversationToDelete(null);
      setAssociatedClient(null);
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'facebook':
        return <FacebookLogo className="w-5 h-5 text-blue-600" />;
      case 'instagram':
        return <InstagramLogo className="w-5 h-5 text-pink-600" />;
      case 'whatsapp':
        return <WhatsappLogo className="w-5 h-5 text-green-600" />;
      default:
        return null;
    }
  };

  const getTimestamp = (timestamp: number) => {
    if (!timestamp) return new Date();
    return new Date(timestamp);
  };

  if (loading) {
    return (
      <div className="p-4">
        <div className="animate-pulse space-y-4">
          {[1, 2, 3].map((n) => (
            <div key={n} className="h-16 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col overflow-hidden">
      <div className="p-4 border-b border-gray-200 sticky top-0 bg-white z-10 flex-shrink-0">
        <h2 className="text-lg font-semibold text-gray-900">Conversaciones</h2>
        <div className="mt-2 relative">
          <input
            type="text"
            placeholder="Buscar conversaciones..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <MagnifyingGlass className="absolute left-3 top-2.5 text-gray-400" size={20} />
        </div>
      </div>

      <div className="flex-1 overflow-y-auto">
        {loading ? (
          <div className="flex justify-center items-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-gray-900"></div>
          </div>
        ) : (
          <>
            {/* Banner de errores de cuentas */}
            <AccountErrorBanner className="p-4 pb-0" />

            {filteredConversations.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                {searchTerm ? 'No se encontraron conversaciones.' : 'No hay conversaciones activas.'}
              </div>
            ) : (
              filteredConversations.map((conversation) => (
                <div
                  key={conversation.id}
                  className={clsx(
                    'p-4 border-b border-gray-200 cursor-pointer hover:bg-gray-50 transition-colors relative group',
                    {
                      'bg-blue-50': selectedConversation?.id === conversation.id,
                      'bg-green-50': conversation.unread && conversation.unreadCount > 0 && selectedConversation?.id !== conversation.id
                    }
                  )}
                  onClick={() => handleSelect(conversation)}
                >
                  {/* Botón de eliminar */}
                  <button
                    className="absolute right-2 top-2 p-1.5 rounded-full bg-red-100 text-red-600 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-200"
                    onClick={(e) => handleDeleteClick(e, conversation)}
                    title="Eliminar conversación"
                  >
                    <Trash size={16} />
                  </button>
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 relative">
                      {/* Indicador de mensaje no leído */}
                      {conversation.unread && conversation.unreadCount > 0 && selectedConversation?.id !== conversation.id && (
                        <div className="absolute top-0 right-0 w-4 h-4 bg-red-500 rounded-full animate-pulse border-2 border-white z-10"></div>
                      )}

                      {getParticipantImage(conversation) ? (
                        <img
                          src={getParticipantImage(conversation)}
                          alt={conversation.participantName || 'Usuario'}
                          className="w-10 h-10 rounded-full object-cover"
                          data-conversation-id={conversation.id}
                          onError={(e) => {
                            const imageUrl = conversation.profilePictureUrl || conversation.participantPicture || conversation.participant?.profilePic;
                            console.log(`❌ Error al cargar imagen de perfil: ${imageUrl}`);
                            console.log(`🔍 Detalles de la conversación:`, {
                              id: conversation.id,
                              platform: conversation.platform,
                              profilePictureUrl: conversation.profilePictureUrl,
                              participantPicture: conversation.participantPicture,
                              participantProfilePic: conversation.participant?.profilePic,
                              profilePictureUpdatedAt: conversation.profilePictureUpdatedAt,
                              lastImageUpdate: conversation.lastImageUpdate
                            });
                            
                            // Intentar identificar por qué falló la carga
                            const img = new Image();
                            img.onload = () => console.log(`✅ La imagen existe y se puede cargar, pero falló en el componente`);
                            img.onerror = () => console.log(`❌ La imagen no existe o no se puede cargar: ${imageUrl}`);
                            img.src = imageUrl || '';

                            // Si la imagen falla al cargar, mostrar la inicial
                            e.currentTarget.style.display = 'none';

                            // Verificar que nextElementSibling existe antes de acceder a su propiedad style
                            const nextElement = e.currentTarget.nextElementSibling;
                            if (nextElement) {
                              // Usar casting para indicar que sabemos que es un HTMLElement
                              (nextElement as HTMLElement).style.display = 'flex';
                            }

                            // Simplemente mostrar la inicial del usuario
                            console.log(`ℹ️ Mostrando inicial para: ${conversation.participantName || 'Usuario'}`);
                          }}
                        />
                      ) : null}
                      <div
                        className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center"
                        style={{ display: conversation.profilePictureUrl || conversation.participantPicture || conversation.participant?.profilePic ? 'none' : 'flex' }}
                      >
                        <span className="text-gray-500 text-lg">
                          {(conversation.participantName || 'U')[0].toUpperCase()}
                        </span>
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex justify-between items-start">
                        <div className="flex items-center space-x-2">
                          <h3 className="text-sm font-medium text-gray-900 truncate">
                            {conversation.participantName || 'Usuario'}
                          </h3>
                          {isClient(conversation) && (
                            <div title="Cliente registrado">
                              <User
                                className="w-4 h-4 text-emerald-500"
                                weight="fill"
                              />
                            </div>
                          )}
                        </div>
                        <div className="flex items-center space-x-2">
                          {getPlatformIcon(conversation.platform)}
                          <span className="text-xs text-gray-500">
                            {conversation.lastMessage?.timestamp ?
                              formatDistanceToNow(getTimestamp(conversation.lastMessage.timestamp), {
                                addSuffix: true,
                                locale: es
                              }) : ''}
                          </span>
                        </div>
                      </div>
                      <div className="flex flex-col">
                        <p className="text-sm text-gray-500 truncate">
                          {conversation.lastMessage?.content || 'No hay mensajes'}
                        </p>
                        {conversation.unread && conversation.unreadCount > 0 && selectedConversation?.id !== conversation.id && (
                          <span className="text-[10px] font-bold text-black mt-0.5">
                            Mensaje entrante!
                          </span>
                        )}
                      </div>
                      {conversation.unread && conversation.unreadCount > 0 && (
                        <span className="inline-flex items-center justify-center min-w-5 h-5 px-1.5 text-xs font-bold text-white bg-red-500 rounded-full">
                          {conversation.unreadCount}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </>
        )}
      </div>
      
      {/* Modal de confirmación para eliminar conversación */}
      {showDeleteModal && conversationToDelete && (
        <ModalPortal>
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center">
            <div 
              className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center mb-4">
                <div className="bg-red-100 p-2 rounded-full mr-3">
                  <Warning size={24} className="text-red-600" weight="fill" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900">
                  ¿Eliminar esta conversación?
                </h3>
              </div>
              <p className="text-gray-600 mb-4">
                Esta acción eliminará permanentemente la conversación con 
                <span className="font-medium"> {conversationToDelete.participantName || 'Usuario'}</span> 
                y todos sus mensajes asociados.
              </p>
              
              {associatedClient && (
                <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                  <p className="text-yellow-800 font-medium flex items-center">
                    <Warning size={18} className="mr-2 text-yellow-600" />
                    Atención: Cliente asociado
                  </p>
                  <p className="text-yellow-700 text-sm mt-1">
                    Esta conversación está asociada al cliente <span className="font-medium">{associatedClient.name || associatedClient.fullName || 'Sin nombre'}</span>. 
                    Si eliminas esta conversación, también se eliminará el cliente y toda su información.
                  </p>
                </div>
              )}
              
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={handleCancelDelete}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                  disabled={isDeleting}
                >
                  Cancelar
                </button>
                
                <button
                  onClick={handleConfirmDelete}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 flex items-center"
                  disabled={isDeleting}
                >
                  {isDeleting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Eliminando...
                    </>
                  ) : (
                    <>
                      <Trash size={16} className="mr-1" />
                      Eliminar {associatedClient ? 'conversación y cliente' : 'conversación'}
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </ModalPortal>
      )}
    </div>
  );
}