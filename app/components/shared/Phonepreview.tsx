'use client'

import { useState } from 'react';
import ArtistInfoPopup from './ArtistInfoPopup';

interface PhonepreviewProps {
  children: React.ReactNode
}

export default function Phonepreview({ children }: PhonepreviewProps) {
  const [showArtistInfo, setShowArtistInfo] = useState(false);

  return (
    <div className="flex justify-center items-center w-full h-full p-4">
      <div className="relative w-[320px] h-[640px] bg-white rounded-[3rem] shadow-xl overflow-hidden border-8 border-gray-800">
        {/* Notch */}
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-40 h-6 bg-gray-800 rounded-b-2xl z-50" />
        
        {/* Botón de información profesional */}
        <button 
          onClick={() => setShowArtistInfo(true)}
          className="absolute top-3 right-3 z-50 bg-blue-100 bg-opacity-80 rounded-full p-2 hover:bg-blue-200 transition-colors"
          aria-label="Información profesional"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
        </button>
        
        {/* Screen Content */}
        <div className="h-full overflow-y-auto">
          {children}
        </div>

        {/* Home Indicator */}
        <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-gray-300 rounded-full" />
      </div>
      
      {/* Popup de información profesional */}
      {showArtistInfo && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <ArtistInfoPopup onClose={() => setShowArtistInfo(false)} />
          </div>
        </div>
      )}
    </div>
  )
}
