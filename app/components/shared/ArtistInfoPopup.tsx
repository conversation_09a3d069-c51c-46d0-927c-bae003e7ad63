'use client';

import { useProfile } from '@/app/contexts/ProfileContext';

export default function ArtistInfoPopup({ onClose }: { onClose: () => void }) {
  const { profile, loading } = useProfile();
  
  // Mostrar un indicador de carga si el perfil está cargando
  if (loading) {
    return (
      <div className="p-4 text-center">
        <p>Cargando información...</p>
      </div>
    );
  }
  
  // Validar que el perfil exista
  if (!profile) {
    return (
      <div className="p-4 text-center">
        <p>No se pudo cargar la información del perfil.</p>
        <button onClick={onClose} className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
          Cerrar
        </button>
      </div>
    );
  }
  
  // Extraer datos profesionales con validaciones seguras
  const professional = profile?.profile?.professional || {};
  const specialties = Array.isArray(professional?.specialties) ? professional.specialties : [];
  const languages = Array.isArray(professional?.languages) ? professional.languages : [];
  
  // Extraer datos de ubicación con validaciones seguras
  const studio = profile?.profile?.contact?.studio || {};
  const fullAddress = studio?.fullAddress || 'No especificado';
  
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-start">
        <h2 className="text-xl font-bold text-gray-900">Información Profesional</h2>
        <button 
          onClick={onClose}
          className="text-gray-500 hover:text-gray-700"
          aria-label="Cerrar"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      {/* Especialidades */}
      <div>
        <h3 className="font-medium text-gray-700 mb-2">Especialidades</h3>
        <div className="flex flex-wrap gap-2">
          {specialties.length > 0 ? (
            specialties.map((specialty: string) => (
              <span key={specialty} className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                {specialty}
              </span>
            ))
          ) : (
            <p className="text-gray-500">No se han especificado especialidades</p>
          )}
        </div>
      </div>
      
      {/* Idiomas */}
      <div>
        <h3 className="font-medium text-gray-700 mb-2">Idiomas</h3>
        <div className="flex flex-wrap gap-2">
          {languages.length > 0 ? (
            languages.map((language: string) => (
              <span key={language} className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">
                {language}
              </span>
            ))
          ) : (
            <p className="text-gray-500">No se han especificado idiomas</p>
          )}
        </div>
      </div>
      
      {/* Ubicación */}
      <div>
        <h3 className="font-medium text-gray-700 mb-2">Ubicación del Estudio</h3>
        <p className="text-gray-800">{fullAddress}</p>
      </div>
    </div>
  );
}
