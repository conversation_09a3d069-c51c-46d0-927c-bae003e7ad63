'use client';

import { motion } from 'framer-motion';
import { UserPlus, Palette, Calendar, ArrowRight, Rocket, Sparkles, CheckCircle } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

const steps = [
  {
    icon: UserPlus,
    title: "Crea tu cuenta",
    description: "Regístrate en menos de un minuto y accede de inmediato a todas las funcionalidades de Tatu.Ink diseñadas para artistas independientes.",
    color: "from-gray-700 to-gray-900",
    image: "/step1-register.png",
    features: [
      "Registro rápido con email o Google",
      "Configuración inicial guiada",
      "Acceso inmediato a todas las herramientas"
    ]
  },
  {
    icon: Calendar,
    title: "Personaliza tu perfil",
    description: "Configura tus servicios, horarios y ajusta la configuración según tu estilo único y forma de trabajo como artista del tatuaje.",
    color: "from-gray-700 to-gray-900",
    image: "/step2-setup.png",
    features: [
      "Personalización de servicios y precios",
      "Configuración de horarios de disponibilidad",
      "Creación de tu portafolio digital"
    ]
  },
  {
    icon: Palette,
    title: "Enfócate en tu arte",
    description: "Mientras la plataforma gestiona automáticamente tus citas, comunicaciones y pagos, tú puedes dedicar más tiempo a lo que realmente importa: tu arte.",
    color: "from-gray-700 to-gray-900",
    image: "/step3-manage.png",
    features: [
      "Gestión automatizada de citas",
      "Filtrado inteligente de clientes",
      "Seguimiento de proyectos y pagos"
    ]
  }
];

export default function HowItWorks() {
  return (
    <section id="how-it-works" className="py-24 relative overflow-hidden bg-black">
      {/* Elementos decorativos de fondo */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-full h-full bg-[url('/tattoo-pattern.png')] bg-repeat opacity-5"></div>
        <div className="absolute top-40 -left-20 w-80 h-80 bg-gray-700 rounded-full filter blur-3xl opacity-10"></div>
        <div className="absolute bottom-40 -right-20 w-80 h-80 bg-gray-700 rounded-full filter blur-3xl opacity-10"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 relative z-10">
        {/* Encabezado de sección */}
        <div className="text-center mb-20">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="inline-flex items-center bg-white/10 backdrop-blur-sm px-4 py-1 rounded-full text-sm font-medium mb-6"
          >
            <Rocket className="w-4 h-4 mr-2 text-white" />
            <span className="text-white">COMIENZA EN MINUTOS</span>
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-4xl md:text-5xl font-bold text-white mb-6"
          >
            Tres simples pasos para
            <span className="text-white"> potenciar tu carrera</span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-xl text-gray-300 max-w-3xl mx-auto"
          >
            Empieza a gestionar tu trabajo como artista del tatuaje de forma profesional y automatizada, permitiéndote dedicar más tiempo a lo que realmente importa: tu arte
          </motion.p>
        </div>

        {/* Pasos */}
        <div className="space-y-24 mb-20">
          {steps.map((step, index) => {
            const Icon = step.icon;
            const isEven = index % 2 === 0;

            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.7, delay: 0.1 }}
                className="relative"
              >
                {/* Número de paso con efecto de resplandor */}
                <div className="absolute -top-10 -left-10 z-0 opacity-10">
                  <span className="text-[200px] font-bold text-white">{index + 1}</span>
                </div>

                <div className={`grid grid-cols-1 ${isEven ? 'lg:grid-cols-[1fr,1.2fr]' : 'lg:grid-cols-[1.2fr,1fr] lg:flex-row-reverse'} gap-12 items-center relative z-10`}>
                  {/* Columna de texto */}
                  <div className={`${isEven ? 'lg:pr-12' : 'lg:pl-12'}`}>
                    <div className={`inline-flex items-center bg-gradient-to-r ${step.color} p-3 rounded-xl shadow-lg mb-6`}>
                      <Icon className="w-6 h-6 text-white" />
                    </div>

                    <h3 className="text-3xl font-bold text-white mb-4">
                      <span className="mr-2 opacity-60">{index + 1}.</span> {step.title}
                    </h3>

                    <p className="text-xl text-gray-300 mb-8">
                      {step.description}
                    </p>

                    <ul className="space-y-3">
                      {step.features.map((feature, featureIndex) => (
                        <motion.li
                          key={featureIndex}
                          initial={{ opacity: 0, x: -10 }}
                          whileInView={{ opacity: 1, x: 0 }}
                          viewport={{ once: true }}
                          transition={{ duration: 0.3, delay: 0.1 * featureIndex + 0.3 }}
                          className="flex items-start"
                        >
                          <CheckCircle className={`w-5 h-5 mr-3 flex-shrink-0 bg-gradient-to-r ${step.color} text-transparent bg-clip-text`} />
                          <span className="text-gray-300">{feature}</span>
                        </motion.li>
                      ))}
                    </ul>
                  </div>

                  {/* Columna con UI estilizada */}
                  <motion.div
                    initial={{ opacity: 0, x: isEven ? 30 : -30 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.7, delay: 0.2 }}
                    className={`relative ${isEven ? 'lg:order-last' : 'lg:order-first'}`}
                  >
                    <div className="relative bg-gradient-to-r from-gray-800 to-gray-900 rounded-2xl p-1 shadow-2xl">
                      <div className="bg-gray-900 rounded-xl overflow-hidden aspect-[4/3] p-4">
                        {/* UI Estilizada basada en el paso */}
                        {index === 0 ? (
                          // Paso 1: Formulario de registro
                          <div className="relative bg-white rounded-2xl overflow-hidden border border-gray-200 shadow-2xl">
                            <div className="absolute inset-0 bg-gradient-to-br from-transparent to-gray-50 opacity-50"></div>
                            <div className="relative p-6 md:p-8">
                              <div className="flex items-center justify-between mb-6">
                                <div className="flex items-center">
                                  <div className="h-2 w-2 rounded-full bg-red-500 mr-1.5"></div>
                                  <div className="h-2 w-2 rounded-full bg-yellow-500 mr-1.5"></div>
                                  <div className="h-2 w-2 rounded-full bg-green-500"></div>
                                </div>
                                <div className="text-sm font-medium text-black">Tatu.Ink</div>
                              </div>
                              <div className="bg-white rounded-lg p-6 space-y-4">
                                <div className="text-center">
                                  <h3 className="text-3xl font-bold text-gray-900 mb-2">Crea tu cuenta gratis</h3>
                                  <p className="text-gray-600 text-sm mb-4">O <span className="font-medium text-black hover:text-gray-800">inicia sesión si ya tienes una cuenta</span></p>
                                </div>
                                <div className="flex items-center justify-center mb-2">
                                  <button className="w-full flex items-center justify-center gap-2 border border-gray-300 rounded-md py-2 px-4 text-gray-700 hover:bg-gray-50 transition-colors">
                                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M18.1711 8.36788H17.5V8.33329H10V11.6666H14.6906C14.0533 13.6071 12.1844 15 10 15C7.23859 15 5.00003 12.7614 5.00003 10C5.00003 7.23858 7.23859 5 10 5C11.2745 5 12.4344 5.48085 13.317 6.26391L15.6741 3.90681C14.1857 2.52811 12.2095 1.66663 10 1.66663C5.39795 1.66663 1.66669 5.39789 1.66669 9.99996C1.66669 14.602 5.39795 18.3333 10 18.3333C14.6021 18.3333 18.3334 14.602 18.3334 9.99996C18.3334 9.44546 18.2738 8.89879 18.1711 8.36788Z" fill="#FFC107"/>
                                      <path d="M2.62744 6.12445L5.36261 8.12937C6.10478 6.29508 7.90066 5 10 5C11.2745 5 12.4345 5.48085 13.317 6.26391L15.6741 3.90681C14.1857 2.52811 12.2095 1.66663 10 1.66663C6.79955 1.66663 4.02368 3.47897 2.62744 6.12445Z" fill="#FF3D00"/>
                                      <path d="M10 18.3334C12.1516 18.3334 14.0838 17.5168 15.5509 16.1984L13.0068 13.9868C12.1394 14.6335 11.0867 15.0001 10 15.0001C7.82492 15.0001 5.96125 13.6179 5.31709 11.6892L2.58301 13.8084C3.96384 16.5043 6.76144 18.3334 10 18.3334Z" fill="#4CAF50"/>
                                      <path d="M18.1711 8.36788H17.5V8.33329H10V11.6666H14.6906C14.3881 12.5992 13.7658 13.3982 12.9998 13.9999L12.9999 13.9999L15.544 16.2115C15.4025 16.3408 18.3333 14.1667 18.3333 10C18.3333 9.44546 18.2738 8.89879 18.1711 8.36788Z" fill="#1976D2"/>
                                    </svg>
                                    <span>Continuar con Google</span>
                                  </button>
                                </div>
                                <div className="relative">
                                  <div className="absolute inset-0 flex items-center">
                                    <div className="w-full border-t border-gray-300"></div>
                                  </div>
                                  <div className="relative flex justify-center text-sm">
                                    <span className="px-2 bg-white text-gray-500">O continúa con email</span>
                                  </div>
                                </div>
                                <div className="space-y-3">
                                  <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                    <div className="h-10 border border-gray-300 rounded-md w-full"></div>
                                  </div>
                                  <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Contraseña</label>
                                    <div className="h-10 border border-gray-300 rounded-md w-full"></div>
                                  </div>
                                  <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Confirmar contraseña</label>
                                    <div className="h-10 border border-gray-300 rounded-md w-full"></div>
                                  </div>
                                  <div className="flex items-start pt-2">
                                    <div className="flex items-center h-5">
                                      <div className="h-4 w-4 border border-gray-300 rounded"></div>
                                    </div>
                                    <div className="ml-3 text-sm text-gray-700">
                                      Acepto los <span className="font-bold text-black">términos de servicio</span> y <span className="font-bold text-black">políticas de privacidad</span>
                                    </div>
                                  </div>
                                </div>
                                <div className="pt-2">
                                  <button className="w-full bg-black text-white font-medium py-2.5 rounded-md hover:bg-gray-800 transition-colors">
                                    Crear cuenta
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        ) : index === 1 ? (
                          // Paso 2: Configuración de perfil (Onboarding)
                          <div className="relative bg-white rounded-2xl overflow-hidden border border-gray-200 shadow-2xl">
                            <div className="absolute inset-0 bg-gradient-to-br from-transparent to-gray-50 opacity-50"></div>
                            
                            <div className="relative p-6 md:p-8">
                              <div className="flex items-center justify-between mb-6">
                                <div className="flex items-center">
                                  <div className="h-2 w-2 rounded-full bg-red-500 mr-1.5"></div>
                                  <div className="h-2 w-2 rounded-full bg-yellow-500 mr-1.5"></div>
                                  <div className="h-2 w-2 rounded-full bg-green-500"></div>
                                </div>
                                <div className="text-sm font-medium text-black">Tatu.Ink</div>
                              </div>
                              
                              <div className="bg-white rounded-lg p-6 space-y-4">
                                <div className="flex items-center justify-center space-x-4 mb-4">
                                  <div className="w-8 h-8 rounded-full bg-black text-white flex items-center justify-center font-medium">1</div>
                                  <div className="h-0.5 w-8 bg-gray-300"></div>
                                  <div className="w-8 h-8 rounded-full bg-black text-white flex items-center justify-center font-medium">2</div>
                                  <div className="h-0.5 w-8 bg-gray-300"></div>
                                  <div className="w-8 h-8 rounded-full bg-gray-200 text-gray-500 flex items-center justify-center font-medium">3</div>
                                </div>
                                
                                <div className="text-center">
                                  <h3 className="text-2xl font-bold text-gray-900 mb-2">Personaliza tu perfil</h3>
                                  <p className="text-gray-600 text-sm mb-6">Configura tu nombre de usuario y personaliza tu perfil</p>
                                </div>
                                
                                <div className="space-y-4">
                                  <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Nombre de usuario</label>
                                    <div className="flex">
                                      <div className="h-10 border border-gray-300 rounded-md w-full flex items-center px-3 bg-white">
                                        <span className="text-gray-400 mr-1">tatu.ink/</span>
                                        <div className="h-4 bg-gray-100 rounded w-24"></div>
                                      </div>
                                    </div>
                                    <div className="flex items-center mt-1">
                                      <div className="w-4 h-4 text-green-500 mr-1">
                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clipRule="evenodd" />
                                        </svg>
                                      </div>
                                      <span className="text-xs text-green-600">Nombre de usuario disponible</span>
                                    </div>
                                  </div>
                                  
                                  <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Nombre completo</label>
                                    <div className="h-10 border border-gray-300 rounded-md w-full"></div>
                                  </div>
                                  
                                  <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Descripción</label>
                                    <div className="h-24 border border-gray-300 rounded-md w-full"></div>
                                    <p className="text-xs text-gray-500 mt-1">Describe brevemente tu estilo y experiencia como artista</p>
                                  </div>
                                </div>
                                
                                <div className="pt-4 flex justify-between">
                                  <button className="px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">
                                    Atrás
                                  </button>
                                  <button className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-black hover:bg-gray-800">
                                    Continuar
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        ) : (
                          // Paso 3: Dashboard de gestión con calendario moderno
                          <div className="relative bg-[#1e293b] rounded-2xl overflow-hidden border border-gray-700 shadow-2xl">
                            <div className="absolute inset-0 bg-gradient-to-br from-[#1e293b] to-[#0f172a] opacity-50"></div>
                            
                            <div className="relative p-6">
                              <div className="flex items-center justify-between mb-6 border-b border-gray-700 pb-3">
                                <div className="text-white font-medium text-lg">Dashboard</div>
                                <div className="flex space-x-1">
                                  <div className="w-2 h-2 rounded-full bg-red-500"></div>
                                  <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                                  <div className="w-2 h-2 rounded-full bg-green-500"></div>
                                </div>
                              </div>

                              {/* Contenido del dashboard */}
                              <div className="flex-1 flex flex-col">
                                {/* Cards de resumen */}
                                <div className="grid grid-cols-2 gap-3 mb-4">
                                  <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-xl p-3 shadow-lg">
                                    <div className="text-xs text-gray-400 mb-1">Citas Hoy</div>
                                    <div className="text-2xl text-white font-bold">3</div>
                                    <div className="h-1.5 bg-indigo-600/30 rounded-full w-3/4 mt-2"></div>
                                  </div>

                                  <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-xl p-3 shadow-lg">
                                    <div className="text-xs text-gray-400 mb-1">Mensajes</div>
                                    <div className="text-2xl text-white font-bold">12</div>
                                    <div className="h-1.5 bg-emerald-600/30 rounded-full w-1/2 mt-2"></div>
                                  </div>
                                </div>

                                {/* Calendario moderno */}
                                <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-xl p-3 shadow-lg mb-4">
                                  <div className="flex items-center justify-between mb-3">
                                    <div className="text-white font-medium">Calendario</div>
                                    <div className="flex items-center space-x-1">
                                      <button className="w-6 h-6 flex items-center justify-center rounded-md hover:bg-gray-700">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="w-4 h-4 text-gray-400">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                                        </svg>
                                      </button>
                                      <span className="text-sm text-gray-300">Junio 2025</span>
                                      <button className="w-6 h-6 flex items-center justify-center rounded-md hover:bg-gray-700">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="w-4 h-4 text-gray-400">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                        </svg>
                                      </button>
                                    </div>
                                  </div>
                                  
                                  <div className="grid grid-cols-7 gap-1 mb-2">
                                    {['L', 'M', 'X', 'J', 'V', 'S', 'D'].map((day, i) => (
                                      <div key={i} className="text-center text-xs text-gray-400 font-medium py-1">
                                        {day}
                                      </div>
                                    ))}
                                  </div>
                                  
                                  <div className="grid grid-cols-7 gap-1 mb-1">
                                    {/* Los primeros días vacíos para alinear */}
                                    {Array(3).fill(0).map((_, i) => (
                                      <div key={`empty-${i}`} className="h-8"></div>
                                    ))}
                                    
                                    {/* Los días del mes */}
                                    {Array(28).fill(0).map((_, i) => {
                                      // Días con citas (para el ejemplo)
                                      const hasMorningAppointment = i + 1 === 4 || i + 1 === 18 || i + 1 === 25; 
                                      const hasAfternoonAppointment = i + 1 === 5 || i + 1 === 12 || i + 1 === 26;
                                      
                                      return (
                                        <div key={i} 
                                          className={`h-8 relative rounded-md flex items-center justify-center
                                            ${i + 1 === 21 ? 'bg-indigo-500/50' : 'hover:bg-gray-700/50'}
                                          `}>
                                          <span className={`text-xs ${i + 1 === 21 ? 'text-white font-medium' : 'text-gray-300'}`}>
                                            {i + 1}
                                          </span>
                                          
                                          {/* Indicadores de citas */}
                                          <div className="absolute -bottom-1 flex justify-center space-x-0.5">
                                            {hasMorningAppointment && 
                                              <div className="w-1 h-1 rounded-full bg-emerald-500"></div>
                                            }
                                            {hasAfternoonAppointment && 
                                              <div className="w-1 h-1 rounded-full bg-indigo-500"></div>
                                            }
                                          </div>
                                        </div>
                                      );
                                    })}
                                  </div>
                                </div>
                                
                                {/* Lista de próximas citas */}
                                <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-xl p-3 shadow-lg">
                                  <div className="text-white font-medium mb-2">Próximas Citas</div>
                                  <div className="space-y-2">
                                    <div className="bg-gray-800/50 rounded-lg p-2 flex items-center">
                                      <div className="w-8 h-8 rounded-full bg-indigo-500/30 text-indigo-300 flex items-center justify-center mr-3 text-xs">21</div>
                                      <div className="flex-1">
                                        <div className="text-xs text-white font-medium">Carlos Mendoza</div>
                                        <div className="text-xs text-gray-400">14:00 - Tatuaje manga completa</div>
                                      </div>
                                    </div>
                                    <div className="bg-gray-800/50 rounded-lg p-2 flex items-center">
                                      <div className="w-8 h-8 rounded-full bg-emerald-500/30 text-emerald-300 flex items-center justify-center mr-3 text-xs">25</div>
                                      <div className="flex-1">
                                        <div className="text-xs text-white font-medium">Ana Torres</div>
                                        <div className="text-xs text-gray-400">10:30 - Tatuaje minimalista</div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Elementos decorativos alrededor de la UI */}
                    <div className={`absolute -top-4 ${isEven ? '-right-4' : '-left-4'} w-20 h-20 bg-gradient-to-r ${step.color} rounded-full filter blur-xl opacity-20`}></div>
                    <div className={`absolute -bottom-4 ${isEven ? '-left-4' : '-right-4'} w-20 h-20 bg-gradient-to-r ${step.color} rounded-full filter blur-xl opacity-20`}></div>
                  </motion.div>
                </div>

                {/* Línea conectora entre pasos */}
                {index < steps.length - 1 && (
                  <div className="hidden lg:block absolute left-1/2 bottom-0 transform -translate-x-1/2 translate-y-full w-px h-16 bg-gradient-to-b from-gray-700 to-transparent"></div>
                )}
              </motion.div>
            );
          })}
        </div>

        {/* CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center bg-white/5 backdrop-blur-sm p-10 rounded-2xl border border-gray-800"
        >
          <div className="inline-flex items-center bg-white/10 px-4 py-1 rounded-full text-sm font-medium mb-6">
            <Sparkles className="w-4 h-4 mr-2 text-white" />
            <span className="text-white">OFERTA ESPECIAL</span>
          </div>

          <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
            Comienza hoy y obtén <span className="text-white">1 mes gratis</span> del plan Artista del Tatuaje
          </h3>

          <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
            Sin compromisos, sin tarjeta de crédito. Prueba todas las funcionalidades premium y lleva tu carrera como artista del tatuaje al siguiente nivel.
          </p>

          <Link
            href="//app.tatu.ink/register"
            className="inline-flex items-center px-8 py-4 bg-white text-black rounded-full font-medium text-lg hover:bg-gray-100 transition-all duration-300 shadow-lg group"
          >
            Crear mi cuenta
            <ArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" />
          </Link>

          <p className="mt-6 text-gray-400 flex items-center justify-center">
            <CheckCircle className="w-4 h-4 mr-2 text-white" />
            Configuración en menos de 5 minutos
          </p>
        </motion.div>
      </div>
    </section>
  );
}