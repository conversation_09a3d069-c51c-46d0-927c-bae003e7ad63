'use client';

import { motion } from 'framer-motion';
import { Receipt, DollarSign, FileText, Calculator, PieChart, ArrowUpRight } from 'lucide-react';
import Image from 'next/image';

export default function BillingManagement() {
  // Características de la gestión de facturación
  const features = [
    {
      icon: DollarSign,
      title: "Registro de Ingresos",
      description: "Registra todos los pagos recibidos de tus clientes, categorizados por tipo de servicio."
    },
    {
      icon: FileText,
      title: "Control de Gastos",
      description: "Mantén un registro detallado de todos los gastos relacionados con tu estudio de tatuajes."
    },
    {
      icon: Calculator,
      title: "Cálculo de Impuestos",
      description: "Calcula automáticamente los impuestos aplicables según tu ubicación y tipo de negocio."
    },
    {
      icon: PieChart,
      title: "Reportes Financieros",
      description: "Genera informes detallados para visualizar el rendimiento financiero de tu estudio."
    }
  ];

  return (
    <section className="py-24 bg-gray-50 text-gray-900 relative overflow-hidden">
      {/* Elementos decorativos de fondo */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-full h-full bg-[url('/tattoo-pattern.png')] bg-repeat opacity-5"></div>
        <div className="absolute top-40 -right-20 w-80 h-80 bg-gray-200 rounded-full filter blur-3xl opacity-20"></div>
        <div className="absolute bottom-40 -left-20 w-80 h-80 bg-gray-200 rounded-full filter blur-3xl opacity-20"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Columna de texto */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7 }}
          >
            <div className="inline-flex items-center bg-gray-200 text-gray-900 px-4 py-1 rounded-full text-sm font-medium mb-6">
              <Receipt className="w-4 h-4 mr-2" />
              <span>GESTIÓN FINANCIERA</span>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              Controla las finanzas de
              <span className="text-black"> tu estudio</span>
            </h2>

            <p className="text-xl text-gray-600 mb-12">
              Mantén un registro completo de tus ingresos, gastos e impuestos para optimizar la rentabilidad de tu negocio
            </p>

            {/* Características en grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-8">
              {features.map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className="flex items-start"
                  >
                    <div className="flex-shrink-0 bg-white p-2 rounded-lg mr-4">
                      <Icon className="w-5 h-5 text-black" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">{feature.title}</h3>
                      <p className="text-gray-600 text-sm">{feature.description}</p>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>

          {/* Columna de imagen/dashboard */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7, delay: 0.2 }}
            className="relative"
          >
            <div className="bg-white rounded-2xl p-6 border border-gray-200 shadow-xl">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-bold">Dashboard Financiero</h3>
                <div className="flex space-x-2">
                  <button className="w-3 h-3 rounded-full bg-red-500"></button>
                  <button className="w-3 h-3 rounded-full bg-yellow-500"></button>
                  <button className="w-3 h-3 rounded-full bg-green-500"></button>
                </div>
              </div>

              {/* Resumen financiero */}
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-6 overflow-hidden">
                {[
                  { label: "Ingresos Mensuales", value: "$2,450", change: "+12%", positive: true, bg: 'bg-green-50', border:'border-green-200', text:'text-green-700', labelColor:'text-green-800' },
                  { label: "Gastos", value: "$850", change: "-5%", positive: false, bg: 'bg-rose-50', border:'border-rose-200', text:'text-rose-700', labelColor:'text-rose-800' },
                  { label: "Beneficio Neto", value: "$1,600", change: "+18%", positive: true, bg: 'bg-indigo-50', border:'border-indigo-200', text:'text-indigo-700', labelColor:'text-indigo-800' },
                  { label: "Impuestos", value: "$392", change: "+3%", positive: false, bg: 'bg-amber-50', border:'border-amber-200', text:'text-amber-700', labelColor:'text-amber-800' }
                ].map((item, index) => (
                  <div key={index} className={`${item.bg} ${item.border} rounded-lg p-3 border`}>
                    <div className={`text-sm font-medium ${item.labelColor} mb-1 truncate`}>{item.label}</div>
                    <div className="flex items-baseline flex-wrap">
                      <span className={`text-xl font-bold mr-1 ${item.text}`}>{item.value}</span>
                      <span className={`text-xs font-medium ${item.positive ? 'text-green-600' : 'text-red-600'}`}>
                        {item.change}
                      </span>
                    </div>
                  </div>
                ))}
              </div>

              {/* Gráfico de ingresos mensuales */}
              <div className="bg-white/5 rounded-xl p-4 mb-6">
                <div className="flex justify-between items-center mb-3">
                  <h4 className="text-sm font-medium text-gray-800">Ingresos Mensuales</h4>
                  <div className="text-xs text-gray-400">Últimos 6 meses</div>
                </div>
                <div className="h-40 relative">
                  {/* Gráfico de barras */}
                  <div className="absolute bottom-0 left-0 right-0 flex justify-between items-end h-32">
                    {[
                      { month: 'Ene', value: 65, amount: '$1,950' },
                      { month: 'Feb', value: 80, amount: '$2,400' },
                      { month: 'Mar', value: 55, amount: '$1,650' },
                      { month: 'Abr', value: 75, amount: '$2,250' },
                      { month: 'May', value: 90, amount: '$2,700' },
                      { month: 'Jun', value: 82, amount: '$2,450' }
                    ].map((item, i) => (
                      <div key={i} className="flex flex-col items-center w-1/6">
                        <div className="text-xs text-gray-400 mb-1">{item.amount}</div>
                        <div
                          className="w-8 bg-gradient-to-t from-white/30 to-white/10 rounded-t-sm"
                          style={{ height: `${item.value}%` }}
                        ></div>
                        <div className="text-xs text-gray-500 mt-2">{item.month}</div>
                      </div>
                    ))}
                  </div>

                  {/* Líneas de referencia */}
                  <div className="absolute left-0 right-0 top-0 bottom-0 flex flex-col justify-between pointer-events-none">
                    {[0, 1, 2, 3].map((_, i) => (
                      <div key={i} className="border-t border-white/5 w-full h-0"></div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Resumen de Transferencias */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="text-sm font-medium text-blue-800 mb-1">Transferencias Mensuales</h4>
                  <div className="flex items-baseline">
                    <p className="text-xl font-bold text-blue-700 mr-2">32</p>
                    <p className="text-xs text-blue-600">transferencias únicas</p>
                  </div>
                  <p className="mt-2 text-xs text-gray-600">Límite SII: 50 transferencias</p>
                </div>

                <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
                  <h4 className="text-sm font-medium text-purple-800 mb-1">Transferencias Semestre</h4>
                  <div className="flex items-baseline">
                    <p className="text-xl font-bold text-purple-700 mr-2">86</p>
                    <p className="text-xs text-purple-600">transferencias únicas</p>
                  </div>
                  <p className="mt-2 text-xs text-gray-600">Límite SII: 100 transferencias</p>
                </div>
              </div>

              {/* Transacciones recientes */}
              <div>
                <div className="flex justify-between items-center mb-3">
                  <h4 className="font-medium">Transacciones Recientes</h4>
                  <button className="text-xs text-blue-600 flex items-center">
                    Ver todas <ArrowUpRight className="w-3 h-3 ml-1" />
                  </button>
                </div>

                <div className="space-y-3">
                  {[
                    { client: "Carlos Mendez", service: "Tatuaje Completo", amount: "$350", date: "Hoy" },
                    { client: "Laura Gómez", service: "Diseño Personalizado", amount: "$120", date: "Ayer" },
                    { client: "Miguel Torres", service: "Sesión de Color", amount: "$280", date: "23 Jun" }
                  ].map((transaction, index) => (
                    <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100">
                      <div>
                        <div className="font-medium">{transaction.client}</div>
                        <div className="text-sm text-gray-500">{transaction.service}</div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">{transaction.amount}</div>
                        <div className="text-sm text-gray-500">{transaction.date}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
