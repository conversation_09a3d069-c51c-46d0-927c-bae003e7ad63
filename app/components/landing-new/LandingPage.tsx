'use client';

import { useEffect } from 'react';
import AOS from 'aos';
import 'aos/dist/aos.css';
import Navbar from './Navbar';
import Hero from './Hero';
import Features from './Features';
import AIAssistant from './AIAssistant';
import HowItWorks from './HowItWorks';

import BillingManagement from './BillingManagement';
import WebsiteAnalytics from './WebsiteAnalytics';
import MessageAutomation from './MessageAutomation';
import MessagingIntegration from './MessagingIntegration';
import FAQ from './FAQ';
import CTA from './CTA';
import Footer from './Footer';
import Pricing from './Pricing';

export default function LandingPage() {
  useEffect(() => {
    AOS.init({
      once: true,
      disable: 'phone',
      duration: 600,
      easing: 'ease-out-sine',
    });
  }, []);

  return (
    <main className="flex flex-col min-h-screen">
      <Navbar />
      <Hero />
      <Features />
      <AIAssistant />
      <HowItWorks />

      <BillingManagement />
      <WebsiteAnalytics />
      <MessageAutomation />
      <MessagingIntegration />
      <Pricing />
      <FAQ />
      <CTA />
      <Footer />
    </main>
  );
}