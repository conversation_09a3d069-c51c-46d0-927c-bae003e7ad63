'use client';

import { Instagram, MessageSquare } from 'lucide-react';
import Link from 'next/link';

export default function Footer() {
  return (
    <footer className="bg-black text-white">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 py-16">
        {/* Top section */}
        <div className="flex flex-col md:flex-row justify-between items-center mb-12">
          {/* Logo and description */}
          <div className="mb-8 md:mb-0">
            <img src="/logo-white.png" alt="Tatu.Ink" className="h-8 mb-6" />
            <p className="text-gray-400">
              La plataforma líder para artistas del tatuaje en Chile.
            </p>
          </div>

          {/* Navigation Links */}
          <div className="flex flex-col md:flex-row gap-8">
            <nav className="flex flex-col items-center md:items-start gap-4">
              <a href="#features" className="text-gray-300 hover:text-white transition-colors">
                Características
              </a>
              <a href="#how-it-works" className="text-gray-300 hover:text-white transition-colors">
                Cómo Funciona
              </a>
              <a href="#pricing" className="text-gray-300 hover:text-white transition-colors">
                Precios
              </a>
            </nav>

            {/* Social and Login Links */}
            <div className="flex flex-col items-center md:items-start gap-4">
              <a
                href="https://discord.gg/aKmzdTbJ"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-300 hover:text-white transition-colors flex items-center"
              >
                <MessageSquare className="w-5 h-5 mr-2" />
                <span>Discord</span>
              </a>
              <a
                href="https://instagram.com/tatu.ink.app"
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-300 hover:text-white transition-colors flex items-center"
              >
                <Instagram className="w-5 h-5 mr-2" />
                <span>Instagram</span>
              </a>
            </div>

            {/* Login/Studio Buttons */}
            <div className="flex flex-col gap-4">
              <Link
                href="//app.tatu.ink/login"
                className="text-white hover:text-gray-300 text-sm font-medium transition-colors whitespace-nowrap text-center"
              >
                Iniciar Sesión
              </Link>
              <Link
                href="//app.tatu.ink/register"
                className="bg-white text-black px-3 sm:px-4 py-2 rounded-full text-sm font-medium hover:bg-gray-100 transition-colors text-center whitespace-nowrap"
              >
                Registrarse
              </Link>
            </div>
          </div>
        </div>

        {/* Bottom section */}
        <div className="pt-8 border-t border-gray-800">
          <div className="flex flex-col items-center">
            <div className="flex flex-wrap justify-center gap-6 mb-4">
              <Link href="/terminos-de-servicio" className="text-gray-400 hover:text-white text-sm transition-colors">
                Términos de Servicio
              </Link>
              <Link href="/politicas-de-privacidad" className="text-gray-400 hover:text-white text-sm transition-colors">Políticas de Privacidad</Link>
              <Link href="/politica-de-reembolso" className="text-gray-400 hover:text-white text-sm transition-colors">Política de Reembolso</Link>
            </div>
            <div className="text-gray-400 text-sm">
              {new Date().getFullYear()} Tatu.ink Todos los derechos reservados.
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}