'use client';

import { motion } from 'framer-motion';
import { <PERSON><PERSON>hart, BarChart3 } from 'lucide-react';
import { ChartBarIcon, GlobeAltIcon, DevicePhoneMobileIcon, ArrowTrendingUpIcon } from '@heroicons/react/24/outline';
import Image from 'next/image';

export default function WebsiteAnalytics() {
  // Métricas de ejemplo para mostrar
  const metrics = [
    {
      icon: ChartBarIcon,
      label: "Visitantes",
      value: "1,234",
      change: "+12%",
      positive: true,
      bg: 'bg-indigo-50',
      border: 'border-indigo-200',
      text: 'text-indigo-700',
      labelColor: 'text-indigo-800'
    },
    {
      icon: GlobeAltIcon,
      label: "Top países",
      value: "Chile / 🇨🇱",
      change: "+5%",
      positive: true,
      bg: 'bg-emerald-50',
      border: 'border-emerald-200',
      text: 'text-emerald-700',
      labelColor: 'text-emerald-800'
    },
    {
      icon: DevicePhoneMobileIcon,
      label: "Dispositivos móviles",
      value: "78%",
      change: "+3%",
      positive: true,
      bg: 'bg-amber-50',
      border: 'border-amber-200',
      text: 'text-amber-700',
      labelColor: 'text-amber-800'
    },
    {
      icon: ArrowTrendingUpIcon,
      label: "Fuentes sociales",
      value: "40%",
      change: "+11%",
      positive: true,
      bg: 'bg-rose-50',
      border: 'border-rose-200',
      text: 'text-rose-700',
      labelColor: 'text-rose-800'
    }
  ];

  return (
    <section className="py-24 bg-gray-50 relative overflow-hidden">
      {/* Elementos decorativos de fondo */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-40 -right-20 w-80 h-80 bg-gray-200 rounded-full filter blur-3xl opacity-50"></div>
        <div className="absolute bottom-40 -left-20 w-80 h-80 bg-gray-200 rounded-full filter blur-3xl opacity-50"></div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 relative z-10">
        {/* Encabezado de sección */}
        <div className="text-center mb-16">


          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-4xl md:text-5xl font-bold text-gray-900 mb-6"
          >
            Conoce el rendimiento de tu
            <span className="text-black"> perfil online</span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-xl text-gray-600 max-w-3xl mx-auto mb-12"
          >
            Analiza el comportamiento de tus visitantes y optimiza tu presencia digital para atraer más clientes
          </motion.p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Dashboard de Analytics */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7 }}
            className="rounded-2xl overflow-hidden shadow-xl border border-gray-200 bg-white"
          >
            <div className="p-6 border-b border-gray-100">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-bold text-gray-900">Dashboard de Analytics</h3>
                <div className="flex items-center text-sm text-gray-500">
                  <span>Últimos 30 días</span>
                  <BarChart3 className="ml-2 w-4 h-4" />
                </div>
              </div>
            </div>

            <div className="p-6 space-y-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                {metrics.map((metric, index) => {
                  const Icon = metric.icon;
                  return (
                    <div key={index} className={`${metric.bg} ${metric.border} rounded-lg p-3 border min-w-0 overflow-hidden`}>
                      <div className="flex items-center mb-2 space-x-2">
                        <div className="w-8 h-8 rounded-lg bg-black flex items-center justify-center mr-3 shrink-0">
                          <Icon className="w-4 h-4 text-white" />
                        </div>
                        <span className={`text-sm font-medium leading-snug whitespace-normal break-words ${metric.labelColor}`}>{metric.label}</span>
                      </div>
                      <div className="flex items-baseline flex-wrap">
                        <span className={`text-xl sm:text-2xl font-bold mr-1 ${metric.text}`}>{metric.value}</span>
                        <span className={`text-xs font-medium ${metric.positive ? 'text-green-600' : 'text-red-600'}`}>
                          {metric.change}
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>

            </div>
          </motion.div>

          {/* Texto descriptivo */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7 }}
          >
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Toma decisiones basadas en datos</h3>

            <div className="space-y-6">
              {[
                {
                  title: "Analiza el tráfico de tu perfil",
                  description: "Conoce cuántas personas visitan tu perfil, de dónde vienen y qué páginas son las más populares."
                },
                {
                  title: "Mide las conversiones",
                  description: "Descubre cuántos visitantes se convierten en clientes potenciales y optimiza tu tasa de conversión."
                },
                {
                  title: "Entiende el comportamiento",
                  description: "Analiza cómo interactúan los usuarios con tu contenido para mejorar la experiencia y aumentar el engagement."
                },
                {
                  title: "Reportes personalizados",
                  description: "Crea informes a medida para visualizar los datos que más te interesan y compartirlos con tu equipo."
                }
              ].map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="flex"
                >
                  <div className="flex-shrink-0 flex items-center justify-center w-10 h-10 rounded-full bg-black text-white mr-4">
                    {index + 1}
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 mb-1">{item.title}</h4>
                    <p className="text-gray-600">{item.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
