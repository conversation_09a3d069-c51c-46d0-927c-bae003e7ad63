'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { Calendar, Users, Palette, ArrowRight, Star } from 'lucide-react';

export default function Hero() {
  const [dynamicWord, setDynamicWord] = useState('arte');
  const words = ['arte', 'talento', 'creatividad', 'portafolio', 'clientela'];
  const [wordIndex, setWordIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);
  const [typingSpeed, setTypingSpeed] = useState(150);

  useEffect(() => {
    const typeEffect = () => {
      // Palabra actual que se está escribiendo/borrando
      const currentWord = words[wordIndex];

      // Si está borrando, quita una letra, si no, añade una letra
      if (isDeleting) {
        setDynamicWord(prev => prev.substring(0, prev.length - 1));
        setTypingSpeed(80); // Más rápido al borrar
      } else {
        setDynamicWord(currentWord.substring(0, dynamicWord.length + 1));
        setTypingSpeed(150); // Más lento al escribir
      }

      // Si ha terminado de escribir la palabra completa
      if (!isDeleting && dynamicWord === currentWord) {
        // Pausa antes de empezar a borrar
        setTimeout(() => setIsDeleting(true), 1500);
      }
      // Si ha terminado de borrar la palabra
      else if (isDeleting && dynamicWord === '') {
        setIsDeleting(false);
        // Pasa a la siguiente palabra
        setWordIndex((prev) => (prev + 1) % words.length);
      }
    };

    const timer = setTimeout(typeEffect, typingSpeed);
    return () => clearTimeout(timer);
  }, [dynamicWord, isDeleting, wordIndex, words, typingSpeed]);

  // Características principales
  const mainFeatures = [
    {
      icon: Calendar,
      title: "Agenda Personal",
      description: "Gestiona tus citas y disponibilidad sin complicaciones con nuestro sistema intuitivo."
    },
    {
      icon: Users,
      title: "Filtro de Clientes",
      description: "Nuestro asistente de IA te ayuda a identificar clientes serios y comprometidos."
    },
    {
      icon: Palette,
      title: "Portafolio Digital",
      description: "Exhibe tu arte y estilo único para atraer a los clientes ideales para tu trabajo."
    }
  ];

  // Testimonios
  const testimonials = [
    {
      text: "Tatu.Ink ha transformado mi carrera como artista. Ahora tengo más tiempo para tatuar y menos para administrar mi agenda.",
      author: "Carlos Mendoza",
      role: "Artista del Tatuaje"
    },
    {
      text: "La integración con todos mis canales de mensajería ha hecho que la comunicación con mis clientes sea mucho más eficiente.",
      author: "Laura Gómez",
      role: "Tatuadora Independiente"
    }
  ];

  return (
    <section className="relative min-h-[100vh] overflow-hidden bg-black" aria-label="Sección principal para artistas del tatuaje">
      {/* Fondo con video */}
      <div className="absolute inset-0 z-0 overflow-hidden">
        <div className="absolute inset-0 w-full h-full">
          {/* Video como fondo - funciona en móvil y escritorio */}
          <video
            autoPlay
            loop
            muted
            playsInline
            className="absolute inset-0 w-full h-full object-cover"
            aria-hidden="true"
          >
            <source src="/media/hero-video.mp4" type="video/mp4" />
          </video>

          {/* Overlay oscuro para mejorar la visibilidad del contenido */}
          <div
            className="absolute inset-0 w-full h-full bg-black/50"
            aria-hidden="true"
          ></div>

          {/* Fondo oscuro como fallback si el video no carga */}
          <div
            className="absolute inset-0 w-full h-full video-fallback bg-black"
            style={{
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat'
            }}
            role="img"
            aria-label="Imagen de fondo alternativa para artistas del tatuaje"
          ></div>

          {/* Efectos dinámicos para simular movimiento */}
          <div className="absolute inset-0 w-full h-full opacity-30">
            <div className="absolute top-0 left-0 w-full h-full animate-pulse-slow">
              <div className="absolute top-1/4 left-1/4 w-96 h-96 rounded-full bg-purple-500/10 filter blur-3xl"></div>
              <div className="absolute bottom-1/4 right-1/4 w-96 h-96 rounded-full bg-blue-500/10 filter blur-3xl"></div>
            </div>
          </div>
        </div>

        {/* Overlay para oscurecer el fondo */}
        <div
          className="absolute inset-0 w-full h-full z-1"
          style={{
            background: 'linear-gradient(135deg, rgba(0,0,0,0.85) 0%, rgba(26,26,46,0.85) 100%)',
          }}
        />

        {/* Elementos gráficos adicionales */}
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-1/4 right-1/4 w-64 h-64 bg-white rounded-full mix-blend-overlay filter blur-3xl opacity-10 animate-pulse"></div>
          <div className="absolute bottom-1/3 left-1/3 w-96 h-96 bg-white rounded-full mix-blend-overlay filter blur-3xl opacity-5 animate-pulse animation-delay-2000"></div>
        </div>

        {/* Patrón de puntos */}
        <div className="absolute inset-0 opacity-10">
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-white rounded-full"
              style={{
                top: `${Math.random() * 100}%`,
                left: `${Math.random() * 100}%`,
                opacity: Math.random() * 0.5 + 0.5
              }}
            ></div>
          ))}
        </div>
      </div>

      {/* Contenido principal */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 pt-20 md:pt-24 pb-8 md:pb-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-10 items-center">
          {/* Columna de texto */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center lg:text-left"
          >
            {/* Badge */}
            <div className="inline-flex items-center bg-white/10 backdrop-blur-sm px-3 py-1 rounded-full text-xs md:text-sm font-medium mb-4 md:mb-6">
              <Star className="w-3 h-3 md:w-4 md:h-4 mr-1 md:mr-2 text-white" />
              <span className="text-white">¡YA DISPONIBLE!</span>
            </div>

            {/* Título principal con palabra destacada - Estructura mejorada para móvil */}
            <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold text-white mb-4 md:mb-6 leading-tight">
              {/* Versión móvil completamente rediseñada */}
              <div className="sm:hidden flex flex-col items-center text-center justify-center">
                <div className="mb-1">Para artistas que</div>
                <div className="text-white italic mb-1">transforman</div>
                <div className="flex items-center">
                  <span className="inline-block">su</span>
                  <span className="relative inline-block w-[120px] h-[1.2em] ml-2 text-left">
                    <span className="text-white absolute left-0 bottom-0 transition-opacity duration-300">{dynamicWord}</span>
                    <span className="absolute top-0 h-full w-[2px] bg-white animate-blink" style={{ left: `${dynamicWord.length * 0.6}em` }}></span>
                  </span>
                </div>
              </div>

              {/* Versión desktop - todo en línea */}
              <div className="hidden sm:block">
                <span>Para artistas que </span>
                <span className="text-white italic">transforman</span>
                <span> su </span>
                <span className="relative inline-block w-[150px] md:w-[180px] lg:w-[220px] h-[1.2em] text-left">
                  <span className="text-white absolute left-0 bottom-0 transition-opacity duration-300">{dynamicWord}</span>
                  <span className="absolute top-0 h-full w-[2px] bg-white animate-blink" style={{ left: `${dynamicWord.length * 0.6}em` }}></span>
                </span>
              </div>
            </h1>

            {/* Subtítulo */}
            <p className="text-lg md:text-xl text-gray-300 mb-6 md:mb-8 max-w-xl">
              Potencia tu carrera como artista del tatuaje con Tatu.Ink. Gestiona tu agenda, conecta con clientes serios, muestra tu portafolio y aumenta tus ingresos con nuestra plataforma diseñada para tatuadores independientes.
            </p>

            {/* Botones CTA */}
            <div className="flex flex-col sm:flex-row gap-3 md:gap-4 mb-6 md:mb-8">
              <Link
                href="https://app.tatu.ink/register"
                className="px-6 md:px-8 py-3 md:py-4 bg-white text-black rounded-full font-medium hover:bg-gray-100 transition-all duration-300 flex items-center justify-center group"
              >
                <span>Comenzar Ahora</span>
                <ArrowRight className="ml-2 w-4 h-4 md:w-5 md:h-5 group-hover:translate-x-1 transition-transform" />
              </Link>
              <a
                href="#features"
                className="px-6 md:px-8 py-3 md:py-4 bg-white/10 backdrop-blur-sm border border-white/20 text-white rounded-full font-medium hover:bg-white/20 transition-all duration-300"
              >
                Conoce Más
              </a>
            </div>
          </motion.div>

          {/* Columna de características y mockup */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative mt-4 md:mt-0"
          >
            {/* Mockup de la aplicación */}
            <div className="relative z-10 bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm rounded-xl md:rounded-2xl p-4 md:p-6 border border-white/10 shadow-2xl">
              {/* Barra superior del mockup */}
              <div className="flex justify-between items-center mb-4 md:mb-6">
                <div className="text-white text-sm md:text-base font-medium">Tatu.Ink</div>
                <div className="flex space-x-1.5 md:space-x-2">
                  <div className="w-2.5 h-2.5 md:w-3 md:h-3 rounded-full bg-red-500"></div>
                  <div className="w-2.5 h-2.5 md:w-3 md:h-3 rounded-full bg-yellow-500"></div>
                  <div className="w-2.5 h-2.5 md:w-3 md:h-3 rounded-full bg-green-500"></div>
                </div>
              </div>

              {/* Características principales */}
              <div className="space-y-3 md:space-y-4 mb-4 md:mb-6">
                {mainFeatures.map((feature, index) => {
                  const Icon = feature.icon;
                  return (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}
                      className="bg-white/5 rounded-lg md:rounded-xl p-3 md:p-4 hover:bg-white/10 transition-colors duration-300"
                    >
                      <div className="flex items-start">
                        <div className="flex-shrink-0 bg-white rounded-md md:rounded-lg p-1.5 md:p-2 mr-3 md:mr-4">
                          <Icon className="w-4 h-4 md:w-5 md:h-5 text-black" />
                        </div>
                        <div>
                          <h3 className="text-white text-sm md:text-base font-medium mb-0.5 md:mb-1">{feature.title}</h3>
                          <p className="text-gray-400 text-xs md:text-sm">{feature.description}</p>
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </div>

              {/* Testimonios */}
              <div className="bg-white/5 rounded-lg md:rounded-xl p-3 md:p-4">
                <h3 className="text-white text-sm md:text-base font-medium mb-2 md:mb-3">Lo que dicen nuestros usuarios</h3>
                <div className="space-y-2 md:space-y-3">
                  {testimonials.map((testimonial, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
                      className="bg-white/5 rounded-md md:rounded-lg p-2 md:p-3"
                    >
                      <p className="text-gray-300 text-xs md:text-sm italic mb-1.5 md:mb-2">"{testimonial.text}"</p>
                      <div className="flex items-center">
                        <div
                          className="w-5 h-5 md:w-6 md:h-6 rounded-full overflow-hidden mr-1.5 md:mr-2"
                          style={{
                            backgroundImage: `url(${testimonial.author === "Carlos Mendoza" ? "/media/testimonials/2.png" : "/media/testimonials/1.png"})`,
                            backgroundSize: 'cover',
                            backgroundPosition: 'center'
                          }}
                        ></div>
                        <div>
                          <div className="text-white text-xs font-medium">{testimonial.author}</div>
                          <div className="text-gray-400 text-xs">{testimonial.role}</div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>

            {/* Elementos decorativos alrededor del mockup */}
            <div className="absolute -top-6 -left-6 w-12 h-12 bg-white rounded-full opacity-20 blur-lg"></div>
            <div className="absolute -bottom-6 -right-6 w-12 h-12 bg-white rounded-full opacity-20 blur-lg"></div>
          </motion.div>
        </div>
      </div>

      {/* Marcas de confianza */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 1 }}
        className="relative z-10 border-t border-white/10 mt-4 md:mt-6"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 py-4 md:py-6">
          <div className="text-center mb-4 md:mb-6">
            <p className="text-gray-500 text-xs md:text-sm uppercase tracking-wider">Confían en nosotros</p>
          </div>
          <div className="flex flex-wrap justify-center items-center gap-4 md:gap-8">
            <div
              className="h-8 w-24 md:h-10 md:w-32 bg-transparent"
              style={{
                backgroundImage: 'url(/media/multicolorink.png)',
                backgroundSize: 'contain',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat'
              }}
              role="img"
              aria-label="Logo de Multicolor Ink - Marca de confianza para artistas del tatuaje"
            ></div>
            <div
              className="h-8 w-24 md:h-10 md:w-32 bg-transparent"
              style={{
                backgroundImage: 'url(/media/trustCompanies/Diseño_sin_título__40_-removebg-preview.png)',
                backgroundSize: 'contain',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat'
              }}
              role="img"
              aria-label="Logo de marca asociada - Confianza para artistas del tatuaje"
            ></div>
          </div>
        </div>
      </motion.div>
    </section>
  );
}