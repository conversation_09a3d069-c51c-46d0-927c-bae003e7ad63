'use client';

import React, { useEffect, useState } from 'react';
import PhonePreviewContainer from '../calendar/PhonePreview/PhonePreviewContainer';
import PortfolioPreview from '../portfolio/PortfolioPreview';
import { ReviewProvider } from '../reviews/ReviewContext';
import ReviewsCompact from '../reviews/ReviewsCompact';
import AddReviewModal from '../reviews/AddReviewModal';
import { useCustomization } from '../customize/CustomizationContext';
import { Instagram, Facebook, X, Info } from 'lucide-react';
import LinksPreview from '../links/LinksPreview';
import { PortfolioProvider } from '../portfolio/PortfolioContext';
import { useAuth } from '@/app/contexts/AuthContext';
import Image from 'next/image';
import { doc, updateDoc, increment, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import PublicArtistInfoPopup from '../public/PublicArtistInfoPopup';

interface PhonePreviewProps {
  className?: string;
  userId?: string;
}

interface PortfolioItem {
  id: string;
  title: string;
  imageUrl: string;
  category: string;
  featured: boolean;
}

export default function PhonePreview({ className, userId: propUserId }: PhonePreviewProps) {
  const { config } = useCustomization();
  const { user } = useAuth();
  const [showAddReview, setShowAddReview] = useState(false);
  const [selectedWork, setSelectedWork] = useState<PortfolioItem | null>(null);
  const [showArtistInfo, setShowArtistInfo] = useState(false);
  const [artistProfile, setArtistProfile] = useState<any>(null);

  // Usar el userId proporcionado o el del usuario autenticado
  const userId = propUserId || user?.uid;

  // Memoize the background style to prevent unnecessary re-renders
  const coverStyle = React.useMemo(() => ({
    backgroundColor: config.visual?.primaryColor || '#4F46E5',
    backgroundImage: config.visual?.coverImage ? `url(${config.visual.coverImage})` : undefined,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
  }), [config.visual?.primaryColor, config.visual?.coverImage]);

  // Memoize social media links to prevent unnecessary re-renders
  const socialLinks = React.useMemo(() => ({
    instagram: config.brand?.instagram,
    facebook: config.brand?.facebook,
    tiktok: config.brand?.tiktok
  }), [config.brand?.instagram, config.brand?.facebook, config.brand?.tiktok]);

  // Cargar el perfil del artista cuando se necesite mostrar la información profesional
  useEffect(() => {
    if (showArtistInfo && userId && !artistProfile) {
      const fetchArtistProfile = async () => {
        try {
          console.log('[PhonePreview] Intentando cargar perfil para userId:', userId);
          const profileDoc = await getDoc(doc(db, 'users', userId));
          if (profileDoc.exists()) {
            const data = profileDoc.data();
            console.log('[PhonePreview] Perfil cargado - estructura completa:', JSON.stringify(data, null, 2));
            console.log('[PhonePreview] Datos profesionales:', JSON.stringify(data?.profile?.professional, null, 2));
            console.log('[PhonePreview] Datos de estudio:', JSON.stringify(data?.profile?.contact?.studio, null, 2));
            setArtistProfile(data);
          } else {
            console.log('[PhonePreview] No se encontró perfil para userId:', userId);
          }
        } catch (error) {
          console.error('[PhonePreview] Error al cargar el perfil del artista:', error);
        }
      };
      
      fetchArtistProfile();
    }
  }, [showArtistInfo, userId, artistProfile]);

  const handleWorkSelect = async (work: PortfolioItem) => {
    setSelectedWork(work);
    // Increment view count
    try {
      const workRef = doc(db, 'portfolio', work.id);
      await updateDoc(workRef, {
        views: increment(1)
      });
    } catch (error) {
      console.error('Error incrementing views:', error);
    }
  };

  return (
    <div className={`bg-gray-50 p-6 ${className}`}>
      <div className="mx-auto max-w-[280px]">
        {/* Phone Frame */}
        <div className="rounded-[3rem] bg-gray-900 p-4 shadow-xl">
          {/* Phone Notch */}
          <div className="relative">
            <div className="absolute top-0 inset-x-0 flex justify-center">
              <div className="h-6 w-40 bg-black rounded-b-3xl"></div>
            </div>
          </div>

          {/* Phone Screen */}
          <div className="relative bg-white rounded-[2rem] overflow-hidden h-[580px]">
            {/* Preview Content */}
            <div className="h-full overflow-y-auto">
              {/* Studio Header with Cover and Logo */}
              <div className="relative">
                {/* Cover Image */}
                <div
                  className="w-full h-32 bg-gradient-to-r from-gray-200 to-gray-300 transition-all duration-300"
                  style={coverStyle}
                />

                {/* Info Pill Button */}
                <div className="absolute bottom-2 right-2 z-20">
                  <button
                    onClick={() => setShowArtistInfo(true)}
                    aria-label="Información profesional"
                    className="bg-white/70 backdrop-blur-sm hover:bg-white text-gray-800 rounded-full p-2 shadow transition-colors"
                  >
                    <Info className="w-4 h-4" />
                  </button>
                </div>

                {/* Logo Container - Positioned half over the cover */}
                <div className="absolute left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <div className="w-20 h-20 rounded-full border-4 border-white shadow-lg overflow-hidden bg-white transition-all duration-300">
                    {config.visual?.logo ? (
                      <img
                        src={config.visual.logo}
                        alt="Logo"
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                        <span className="text-gray-400 text-2xl">Logo</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Studio Info - Positioned below the logo */}
                <div className="text-center mt-12 px-4">
                  <h1 className="text-xl font-bold text-gray-900">{config.brand?.name || 'Studio Name'}</h1>
                  <div
                    className="text-sm text-gray-600 mt-1"
                    dangerouslySetInnerHTML={{ __html: config.brand?.slogan || 'Welcome to our studio' }}
                  />

                  {/* Social Media Links */}
                  <div className="flex justify-center gap-6 mt-2">
                    {socialLinks.instagram && (
                      <a
                        href={socialLinks.instagram}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-600 hover:text-gray-900 transition-colors"
                        aria-label="Instagram"
                      >
                        <Instagram className="w-5 h-5" />
                      </a>
                    )}
                    {socialLinks.facebook && (
                      <a
                        href={socialLinks.facebook}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-600 hover:text-gray-900 transition-colors"
                        aria-label="Facebook"
                      >
                        <Facebook className="w-5 h-5" />
                      </a>
                    )}
                    {socialLinks.tiktok && (
                      <a
                        href={socialLinks.tiktok}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-600 hover:text-gray-900 transition-colors"
                        aria-label="TikTok"
                      >
                        <svg
                          className="w-5 h-5"
                          viewBox="0 0 24 24"
                          fill="currentColor"
                        >
                          <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z"/>
                        </svg>
                      </a>
                    )}
                  </div>
                </div>
              </div>

              <div className="p-2">
                {/* Calendar Preview */}
                {config.app?.privacy?.showArtistCalendar !== false && (
                  <div className="mb-4">
                    <PhonePreviewContainer userId={userId} />
                  </div>
                )}

                {/* Links Preview */}
                <div className="mb-6">
                  <LinksPreview userId={userId} />
                </div>

                {/* Portfolio Preview */}
                {config.app?.privacy?.showFeaturedWorks !== false && (
                  <div className="mb-6">
                    <PortfolioProvider userId={userId}>
                      <PortfolioPreview userId={userId} onWorkSelect={handleWorkSelect} />
                    </PortfolioProvider>
                  </div>
                )}

                {/* Reviews Preview */}
                {config.app?.privacy?.showClientReviews !== false && (
                  <div className="mb-6">
                    <ReviewProvider artistId={userId || ''}>
                      <ReviewsCompact onAddReview={() => setShowAddReview(true)} />
                      {/* Modal dentro del teléfono */}
                      {showAddReview && (
                        <div className="absolute inset-0 bg-black bg-opacity-50 z-50 rounded-[2rem] overflow-hidden">
                          <div className="h-full w-full overflow-y-auto p-4">
                            <AddReviewModal onClose={() => setShowAddReview(false)} artistId={userId || ''} />
                          </div>
                        </div>
                      )}
                    </ReviewProvider>
                  </div>
                )}
              </div>
            </div>

            {/* Work Modal dentro del Phone Screen */}
            {selectedWork && (
              <div className="absolute inset-0 bg-black/50 z-50 rounded-[2rem] overflow-hidden">
                <div className="h-full w-full relative">
                  <Image
                    src={selectedWork.imageUrl}
                    alt={selectedWork.title}
                    fill
                    className="object-contain"
                    sizes="(max-width: 768px) 100vw, 42rem"
                  />

                  <button
                    onClick={() => setSelectedWork(null)}
                    className="absolute top-4 right-4 p-2 bg-black/50 hover:bg-black/70 rounded-full text-white z-10"
                  >
                    <X className="w-5 h-5" />
                  </button>

                  <div className="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent">
                    <h2 className="text-lg font-semibold text-white">{selectedWork.title}</h2>
                    <p className="text-sm text-gray-200">{selectedWork.category}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Artist Info Popup dentro del Phone Screen */}
            {showArtistInfo && (
              <div className="absolute inset-0 bg-black bg-opacity-50 z-50 rounded-[2rem] overflow-hidden flex items-center justify-center p-4">
                <div className="bg-white rounded-lg w-full max-w-md p-6">
                  {artistProfile ? (
                    <PublicArtistInfoPopup onClose={() => setShowArtistInfo(false)} profile={artistProfile} />
                  ) : (
                    <div className="text-center p-4">
                      <p>Cargando información profesional...</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
