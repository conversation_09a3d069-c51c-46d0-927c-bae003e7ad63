"use client";

import { Fragment, useEffect, useState } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import {
  HomeIcon,
  BuildingStorefrontIcon,
  CalendarIcon,
  ChatBubbleLeftIcon,
  PhotoIcon,
  BanknotesIcon,
  UserGroupIcon,
  StarIcon,
  BoltIcon,
  ChartBarIcon,
  LinkIcon,
  Cog6ToothIcon,
  XMarkIcon,
  UserCircleIcon,
  PaintBrushIcon,
  SparklesIcon,
  UserIcon,
  FolderIcon,
  ChatBubbleLeftRightIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline'
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth } from '@/app/contexts/AuthContext';
import { usePromo } from '@/app/contexts/PromoContext';
import { usePremiumFeatures } from '@/app/contexts/PremiumFeaturesContext';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { User as FirebaseUser } from 'firebase/auth';

// Definir las rutas que requieren suscripción Pro
const PRO_ROUTES = [
  '/messages',
  '/finances',
  '/clients',
  '/automations',
  '/analytics'
];

const mobileNavigation = [
  { name: 'Finanzas', href: '/finances', icon: BanknotesIcon, isPro: true },
  { name: 'Clientes', href: '/clients', icon: UserGroupIcon, isPro: true },
  { name: 'Reseñas', href: '/reviews', icon: StarIcon },
  { name: 'Automatizaciones', href: '/automations', icon: BoltIcon, isPro: true },
  { name: 'Análisis', href: '/analytics', icon: ChartBarIcon, isPro: true },
  { name: 'Personalización', href: '/customization', icon: PaintBrushIcon },
  { name: 'Enlaces', href: '/links', icon: LinkIcon },
  { name: 'Perfil', href: '/profile', icon: UserCircleIcon },
  { name: 'Configuración', href: '/settings', icon: Cog6ToothIcon },
];

const desktopNavigation = [
  { name: 'Estudio', href: '/studio', icon: BuildingStorefrontIcon },
  { name: 'Calendario', href: '/calendar', icon: CalendarIcon },
  { name: 'Mensajes', href: '/messages', icon: ChatBubbleLeftIcon, isPro: true },
  { name: 'Finanzas', href: '/finances', icon: BanknotesIcon, isPro: true },
  { name: 'Clientes', href: '/clients', icon: UserGroupIcon, isPro: true },
  { name: 'Portfolio', href: '/portfolio', icon: PhotoIcon },
  { name: 'Reseñas', href: '/reviews', icon: StarIcon },
  { name: 'Automatizaciones', href: '/automations', icon: BoltIcon, isPro: true },
  { name: 'Análisis', href: '/analytics', icon: ChartBarIcon, isPro: true },
  { name: 'Personalización', href: '/customization', icon: PaintBrushIcon },
  { name: 'Enlaces', href: '/links', icon: LinkIcon },
  { name: 'Perfil', href: '/profile', icon: UserCircleIcon },
  { name: 'Configuración', href: '/settings', icon: Cog6ToothIcon },
];

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
  className?: string;
}

// Obtener el id del item basado en la ruta
const getItemId = (href: string): string => {
  const path = href.split('/')[1];
  switch (path) {
    case 'studio': return 'studio';
    case 'calendar': return 'calendar';
    case 'messages': return 'messages';
    case 'finances': return 'finances';
    case 'automations': return 'automations';
    case 'customization': return 'customization';
    case 'links': return 'links';
    default: return path;
  }
};

export default function Sidebar({ isOpen, onClose, className = '' }: SidebarProps) {
  const pathname = usePathname();
  const { user, subscriptionStatus, checkingSubscription } = useAuth();
  const { openPromoModal } = usePromo();
  const { setShowPremiumOverlay } = usePremiumFeatures();
  const [isLoading, setIsLoading] = useState(false);

  // Determinar si el usuario tiene suscripción Pro
  const isPro = subscriptionStatus === 'active';

  // Función para manejar clics en elementos Pro
  const handleItemClick = (e: React.MouseEvent, itemIsPro: boolean, href: string) => {
    if (itemIsPro && !isPro) {
      e.preventDefault();
      setShowPremiumOverlay(true);
    }
  };

  // Determinar si mostrar el botón de promoción
  // Únicamente mostrar el botón si el usuario está autenticado pero NO tiene una suscripción activa
  const shouldShowPromoButton = !isLoading &&
                              !checkingSubscription &&
                              user &&
                              subscriptionStatus !== 'active';

  return (
    <>
      <Transition.Root show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50 lg:hidden" onClose={onClose}>
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-900/80" />
          </Transition.Child>

          <div className="fixed inset-0 flex">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
                <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-white px-6 pb-4">
                  <div className="flex h-16 shrink-0 items-center justify-between">
                    <div className="flex items-center">
                      <div className="flex items-center">
                        <img
                          src="/images/logo-black.png"
                          alt="Tatu.ink"
                          className="h-14 w-auto"
                          style={{ display: 'block' }}
                        />
                      </div>
                    </div>
                    <button
                      type="button"
                      className="lg:hidden"
                      onClick={onClose}
                    >
                      <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                    </button>
                  </div>
                  <nav className="flex flex-1 flex-col">
                    <ul role="list" className="flex flex-1 flex-col gap-y-7">
                      <li>
                        <ul role="list" className="-mx-2 space-y-1">
                          {/* Opciones de navegación móvil */}
                          {mobileNavigation.map((item) => (
                            <li key={item.name}>
                              <Link
                                href={item.href}
                                onClick={(e) => item.isPro && !isPro && handleItemClick(e, item.isPro, item.href)}
                                className={`
                                  group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold
                                  ${pathname?.startsWith(item.href)
                                    ? 'bg-gray-100 text-black'
                                    : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'
                                  }
                                `}
                                data-sidebar-item={getItemId(item.href)}
                              >
                                <item.icon
                                  className={`
                                    h-6 w-6 shrink-0
                                    ${pathname?.startsWith(item.href) ? 'text-black' : 'text-gray-400 group-hover:text-gray-900'}
                                  `}
                                  aria-hidden="true"
                                />
                                {item.name}
                                {item.isPro && !isPro && (
                                  <span className="ml-auto inline-flex items-center rounded-md bg-gray-100 px-2 py-1 text-xs font-medium text-gray-900 ring-1 ring-inset ring-gray-700/10">
                                    Pro
                                  </span>
                                )}
                              </Link>
                            </li>
                          ))}

                          {/* Botón de promoción al final en móvil */}
                          {shouldShowPromoButton && (
                            <li className="pt-5 mt-5 border-t border-gray-100">
                              <button
                                onClick={() => {
                                  openPromoModal();
                                  onClose();
                                }}
                                className="group flex w-full items-center gap-x-3 rounded-md bg-gradient-to-r from-gray-800 to-black p-2 text-sm font-semibold text-white hover:from-gray-700 hover:to-gray-900"
                              >
                                <SparklesIcon className="h-6 w-6 shrink-0 text-white" aria-hidden="true" />
                                Mejor tu Plan Gratis
                              </button>
                            </li>
                          )}
                        </ul>
                      </li>
                    </ul>
                  </nav>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>

      {/* Static sidebar for desktop */}
      <div className={`hidden lg:fixed lg:inset-y-0 lg:flex lg:flex-col ${className}`}>
        <div className="flex grow flex-col gap-y-5 overflow-y-auto border-r border-gray-200 bg-white px-6 pb-4">
          <div className="flex h-16 shrink-0 items-center">
            <div className="flex items-center">
              <img
                src="/images/logo-black.png"
                alt="Tatu.ink"
                className="h-14 w-auto"
                style={{ display: 'block' }}
              />
            </div>
          </div>
          <nav className="flex flex-1 flex-col">
            <ul role="list" className="flex flex-1 flex-col gap-y-7">
              <li>
                <ul role="list" className="-mx-2 space-y-1">
                  {/* Opciones de navegación desktop */}
                  {desktopNavigation.map((item) => (
                    <li key={item.name}>
                      <Link
                        href={item.href}
                        onClick={(e) => item.isPro && !isPro && handleItemClick(e, item.isPro, item.href)}
                        className={`
                          group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold
                          ${pathname?.startsWith(item.href)
                            ? 'bg-gray-100 text-black'
                            : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'
                          }
                        `}
                        data-sidebar-item={getItemId(item.href)}
                      >
                        <item.icon
                          className={`
                            h-6 w-6 shrink-0
                            ${pathname?.startsWith(item.href) ? 'text-black' : 'text-gray-400 group-hover:text-gray-900'}
                          `}
                          aria-hidden="true"
                        />
                        {item.name}
                        {item.isPro && !isPro && (
                          <span className="ml-auto inline-flex items-center rounded-md bg-gray-100 px-2 py-1 text-xs font-medium text-gray-900 ring-1 ring-inset ring-gray-700/10">
                            Pro
                          </span>
                        )}
                      </Link>
                    </li>
                  ))}

                  {/* Botón de promoción al final en desktop */}
                  {shouldShowPromoButton && (
                    <li className="pt-5 mt-5 border-t border-gray-100">
                      <button
                        onClick={openPromoModal}
                        className="group flex w-full items-center gap-x-3 rounded-md bg-gradient-to-r from-gray-800 to-black p-2 text-sm font-semibold text-white hover:from-gray-700 hover:to-gray-900"
                      >
                        <SparklesIcon className="h-6 w-6 shrink-0 text-white" aria-hidden="true" />
                        Mejor tu Plan Gratis
                      </button>
                    </li>
                  )}
                </ul>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </>
  )
}
