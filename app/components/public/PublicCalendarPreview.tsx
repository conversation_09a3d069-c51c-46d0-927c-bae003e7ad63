'use client'

import React from 'react'
import { database } from '@/app/firebase/config'
import { db } from '@/lib/firebase/config'
import { ref, get, onValue } from 'firebase/database'
import { ChevronLeftIcon, ChevronRightIcon, XMarkIcon } from '@heroicons/react/24/outline'
import { doc, getDoc, collection, getDocs } from 'firebase/firestore'

// Función auxiliar simple para evitar usar los imports directos que causan problemas
const queryFirestore = (collectionRef: any, conditions: any[] = []) => {
  if (!conditions || !conditions.length) return collectionRef;
  // Implementación mínima para este caso específico
  return {
    _collection: collectionRef,
    _conditions: conditions
  };
};

// Función auxiliar para where
const whereCondition = (field: string, operator: string, value: any) => {
  return { field, operator, value };
};

// Función para obtener documentos con query
const getDocsWithQuery = async (queryObj: any) => {
  // Si es una simple colección, retornar todos los documentos
  if (!queryObj._conditions) {
    return getDocs(queryObj);
  }
  
  // Implementación simplificada para este caso específico
  const snapshot = await getDocs(queryObj._collection);
  const results: any[] = [];
  
  snapshot.forEach(doc => {
    const data = doc.data() as Record<string, any>;
    const condition = queryObj._conditions[0]; // Solo maneja una condición para este caso
    
    if (data[condition.field] === condition.value) {
      results.push(doc);
    }
  });
  
  return {
    forEach: (callback: (doc: any) => void) => results.forEach(callback),
    docs: results,
    empty: results.length === 0
  };
};

interface Appointment {
  id: string
  date: string
  time: string
  artistId: string
  status: string
}

interface SocialIntegration {
  connected: boolean
  userId?: string
}

interface SocialIntegrations {
  instagram: SocialIntegration
  facebook: SocialIntegration
}

// Tipos para los días no laborables
interface DayOff {
  dayOfWeek: number; // 0 = domingo, 1 = lunes, ..., 6 = sábado
  isFullDay: boolean;
  startTime?: string;
  endTime?: string;
}

// Tipos para las vacaciones
interface Vacation {
  id: string;
  startDate: string; // formato "YYYY-MM-DD"
  endDate: string; // formato "YYYY-MM-DD"
  description: string;
}

interface ArtistProfile {
  customization?: {
    brand?: {
      name?: string
      slogan?: string
      facebook?: string
      instagram?: string
      tiktok?: string
    }
  }
}

interface PublicCalendarPreviewProps {
  artistId: string
}

export default function PublicCalendarPreview({ artistId }: PublicCalendarPreviewProps) {
  const [appointments, setAppointments] = React.useState<Appointment[]>([])
  const [loading, setLoading] = React.useState(true)
  const [currentDate, setCurrentDate] = React.useState(new Date())
  const [selectedDay, setSelectedDay] = React.useState<number | null>(null)
  const [isBookingModalOpen, setIsBookingModalOpen] = React.useState(false)
  const [isMessageModalOpen, setIsMessageModalOpen] = React.useState(false)
  const [selectedPlatform, setSelectedPlatform] = React.useState<'instagram' | 'facebook' | 'tiktok' | null>(null)
  const [isCopied, setIsCopied] = React.useState(false)
  const [artistUsername, setArtistUsername] = React.useState('')
  const [integrations, setIntegrations] = React.useState<SocialIntegrations>({
    instagram: { connected: false },
    facebook: { connected: false }
  })
  const [profile, setProfile] = React.useState<ArtistProfile>({})
  const [daysOff, setDaysOff] = React.useState<DayOff[]>([])
  const [vacations, setVacations] = React.useState<Vacation[]>([])

  React.useEffect(() => {
    // Obtener el nombre de usuario del artista y las integraciones sociales
    const fetchArtistProfile = async () => {
      if (!artistId) return
      
      try {
        // 1. Obtener perfil del artista
        const artistDoc = await getDoc(doc(db, 'users', artistId))
        if (artistDoc.exists()) {
          const artistData = artistDoc.data()
          setArtistUsername(artistData.username || '')
          
          // Guardar el perfil completo para acceder a la configuración de marca
          if (artistData.customization) {
            setProfile({ customization: artistData.customization })
          }
        }
        
        // 2. Consultar conexiones reales en metaConnections
        const metaConnectionsRef = collection(db, 'metaConnections')
        const connectionsQuery = queryFirestore(metaConnectionsRef, [
          whereCondition('userId', '==', artistId)
        ])
        const connectionsSnapshot = await getDocsWithQuery(connectionsQuery)
        
        // Inicializar integraciones vacías
        const newIntegrations = {
          instagram: { connected: false, userId: undefined },
          facebook: { connected: false, userId: undefined }
        }
        
        // Procesar cada conexión encontrada
        connectionsSnapshot.forEach((doc) => {
          const connection = doc.data()
          if (connection.platform === 'facebook' || connection.platform === 'instagram') {
            newIntegrations[connection.platform as keyof SocialIntegrations] = {
              connected: true,
              userId: connection.pageId
            }
          }
        })
        
        setIntegrations(newIntegrations)
        console.log('Conexiones sociales encontradas:', JSON.stringify(newIntegrations))
      } catch (error) {
        console.error('Error al obtener perfil o conexiones:', error)
      }
    }
    
    fetchArtistProfile()
  }, [artistId])

  // Cargar días libres y vacaciones
  React.useEffect(() => {
    if (!artistId) return;
    
    const fetchTimeOffData = async () => {
      try {
        const timeOffRef = doc(db, 'timeOff', artistId);
        const timeOffDoc = await getDoc(timeOffRef);
        
        if (timeOffDoc.exists()) {
          const data = timeOffDoc.data();
          setDaysOff(data.daysOff || []);
          setVacations(data.vacations || []);
        }
      } catch (error) {
        console.error('Error al cargar datos de días libres:', error);
      }
    };
    
    fetchTimeOffData();
  }, [artistId]);

  React.useEffect(() => {
    const fetchAppointments = async () => {
      if (!artistId) return

      try {
        const appointmentsRef = ref(database, 'appointments')
        
        // Usar onValue para escuchar cambios en tiempo real
        const unsubscribe = onValue(appointmentsRef, (snapshot) => {
          const appointmentsData: Appointment[] = []

          snapshot.forEach((childSnapshot) => {
            const data = childSnapshot.val()
            
            // Solo incluir citas del artista especificado
            if (data.artistId === artistId) {
              appointmentsData.push({
                id: childSnapshot.key || '',
                ...data
              })
            }
          })

          setAppointments(appointmentsData)
          setLoading(false)
        }, (error) => {
          console.error('Error al obtener citas:', error)
          setLoading(false)
        })
        
        return () => unsubscribe()
      } catch (error) {
        console.error('Error al obtener citas:', error)
        setLoading(false)
      }
    }

    fetchAppointments()
  }, [artistId])

  // Verificar si una fecha está dentro de un período de vacaciones
  const isVacationDay = (date: Date): boolean => {
    const dateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    
    return vacations.some(vacation => {
      return dateStr >= vacation.startDate && dateStr <= vacation.endDate;
    });
  };

  // Verificar si es un día no laborable
  const isDayOff = (date: Date): boolean => {
    const dayOfWeek = date.getDay();
    return daysOff.some(day => day.dayOfWeek === dayOfWeek);
  };

  // Función para verificar si una red social está configurada (o conectada)
  const hasSocialMedia = (platform: 'instagram' | 'facebook' | 'tiktok') => {
    // SOLUCIÓN CORREGIDA: Solo verificamos si la red social está realmente conectada
    // a través de metaConnections (para Instagram y Facebook)
    if (platform === 'instagram' || platform === 'facebook') {
      return integrations[platform]?.connected === true;
    }
    
    // Para TikTok, que no tiene integración directa, verificamos si hay un usuario configurado
    if (platform === 'tiktok' && profile?.customization?.brand?.tiktok && 
        profile.customization.brand.tiktok.trim() !== '') {
      return true;
    }
    
    return false;
  };

  // Función para obtener el ID o nombre de usuario de la red social
  const getSocialMediaId = (platform: 'instagram' | 'facebook' | 'tiktok') => {
    // Si está conectada en las integraciones, usar el userId de las integraciones
    if ((platform === 'instagram' || platform === 'facebook') && 
        integrations[platform]?.connected && integrations[platform]?.userId) {
      return integrations[platform].userId;
    }
    
    // Si no, usar el valor configurado en el perfil
    if (platform === 'instagram') {
      return profile?.customization?.brand?.instagram || artistUsername;
    }
    
    if (platform === 'facebook') {
      const fbValue = profile?.customization?.brand?.facebook || '';
      
      // Extraer el nombre de usuario o ID de Facebook si es una URL completa
      if (fbValue.includes('facebook.com/')) {
        // Extraer el nombre de usuario de la URL de Facebook
        const match = fbValue.match(/facebook\.com\/([^/?&]+)/);
        return match ? match[1] : fbValue;
      }
      
      return fbValue;
    }
    
    if (platform === 'tiktok') {
      const tkValue = profile?.customization?.brand?.tiktok || '';
      
      // Extraer el nombre de usuario si es una URL completa
      if (tkValue.includes('tiktok.com/')) {
        const match = tkValue.match(/@([^/?&]+)/);
        return match ? match[1] : tkValue;
      }
      
      // Si empieza con @ quitar el @
      if (tkValue.startsWith('@')) {
        return tkValue.substring(1);
      }
      
      return tkValue;
    }
    
    return artistUsername;
  };

  const getDaysInMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate()
  }

  const getFirstDayOfMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay()
  }

  const formatDate = (year: number, month: number, day: number) => {
    return `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`
  }

  const getAppointmentsForDay = (date: string) => {
    return appointments.filter(appointment => appointment.date === date)
  }

  const getDayStatus = (day: number) => {
    // Crear la fecha para el día actual
    const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day)
    
    // Verificar si es un día pasado
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    if (date < today) {
      return 'past'
    }
    
    // Verificar si es un día no laborable o de vacaciones
    if (isDayOff(date) || isVacationDay(date)) {
      return 'unavailable'
    }
    
    // Verificar si hay citas para este día
    const dateStr = formatDate(currentDate.getFullYear(), currentDate.getMonth(), day)
    const appointmentsForDay = getAppointmentsForDay(dateStr)
    
    // Si hay citas, el día está ocupado (booked)
    if (appointmentsForDay.length > 0) {
      return 'booked'
    }
    
    return 'available'
  }

  const getStatusStyles = (status: 'available' | 'unavailable' | 'past' | 'booked') => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800 hover:bg-green-200'
      case 'unavailable':
        return 'bg-gray-100 text-gray-500 cursor-not-allowed'
      case 'booked':
        return 'bg-red-100 text-red-800 cursor-not-allowed'
      case 'past':
        return 'bg-gray-50 text-gray-300 cursor-not-allowed'
      default:
        return ''
    }
  }

  const handleDayClick = (day: number) => {
    const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
    // Usar formato YYYY-MM-DD basado en la fecha local
    const dateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    const formattedDate = date.toLocaleDateString('es', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    
    setSelectedDay(day);
    setIsBookingModalOpen(true);
  }

  // Manejar el clic en un botón de red social
  const handleSocialClick = (platform: 'instagram' | 'facebook' | 'tiktok', e: React.MouseEvent) => {
    e.preventDefault();
    setSelectedPlatform(platform);
    setIsMessageModalOpen(true);
    setIsCopied(false); // Resetear el estado de copiado
  };

  // Función para copiar al portapapeles
  const handleCopyMessage = () => {
    navigator.clipboard.writeText(getPlainMessageText(selectedDay || 0));
    setIsCopied(true);
    
    // Limpiar el mensaje después de 2 segundos
    setTimeout(() => {
      setIsCopied(false);
    }, 2000);
  };

  // Obtener mensaje para copiar (texto plano)
  const getPlainMessageText = (day: number) => {
    const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
    const formattedDate = date.toLocaleDateString('es', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    
    return `¡Hola! Vi en Tatu.ink que tienes disponible el ${formattedDate}. Me gustaría agendar una cita para ese día. ¿Podríamos conversar sobre el diseño y el presupuesto?`;
  };

  // Versión codificada para URL
  const getMessageText = (day: number) => {
    const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
    const formattedDate = date.toLocaleDateString('es', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    
    return encodeURIComponent(`¡Hola! Vi en Tatu.ink que tienes disponible el ${formattedDate}. Me gustaría agendar una cita para ese día. ¿Podríamos conversar sobre el diseño y el presupuesto?`);
  }

  // Modificar el renderizado para mostrar un mensaje específico para días no laborables
  const getDayTooltip = (day: number): string => {
    const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
    
    if (isDayOff(date)) {
      return 'Día no laborable';
    }
    
    if (isVacationDay(date)) {
      // Buscar la descripción de las vacaciones si existe
      const dateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
      const vacation = vacations.find(v => dateStr >= v.startDate && dateStr <= v.endDate);
      
      if (vacation?.description) {
        return `No disponible: ${vacation.description}`;
      }
      
      return 'No disponible: Vacaciones';
    }
    
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    if (date < today) {
      return 'Fecha pasada';
    }
    
    const dateStr = formatDate(currentDate.getFullYear(), currentDate.getMonth(), day);
    const appointmentsForDay = getAppointmentsForDay(dateStr);
    
    if (appointmentsForDay.length > 0) {
      return 'No disponible: Agenda completa';
    }
    
    return 'Disponible';
  };

  const changeMonth = (increment: number) => {
    const newDate = new Date(currentDate)
      newDate.setMonth(newDate.getMonth() + increment)
    setCurrentDate(newDate)
  }

  if (loading) {
    return (
      <div className="animate-pulse space-y-4">
        <div className="h-4 w-48 bg-gray-200 rounded"></div>
        <div className="h-64 bg-gray-200 rounded-lg"></div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      {/* Cabecera del calendario */}
      <div className="p-4 bg-gray-50 border-b flex justify-between items-center">
          <button
            onClick={() => changeMonth(-1)}
          className="p-1 rounded-full hover:bg-gray-200"
          >
          <ChevronLeftIcon className="h-5 w-5 text-gray-600" />
          </button>
        
        <h3 className="text-lg font-medium text-gray-900">
          {currentDate.toLocaleDateString('es-ES', { month: 'long', year: 'numeric' })}
        </h3>
        
          <button
            onClick={() => changeMonth(1)}
          className="p-1 rounded-full hover:bg-gray-200"
          >
          <ChevronRightIcon className="h-5 w-5 text-gray-600" />
          </button>
      </div>

      {/* Días de la semana */}
      <div className="grid grid-cols-7 gap-1 p-2 text-center text-xs text-gray-500">
        {['D', 'L', 'M', 'X', 'J', 'V', 'S'].map((day, index) => (
          <div key={index} className="py-1">
            {day}
          </div>
        ))}
      </div>

      {/* Días del mes */}
      <div className="grid grid-cols-7 gap-1 p-2">
        {/* Espacios vacíos para el primer día del mes */}
        {Array.from({ length: getFirstDayOfMonth(currentDate) }).map((_, index) => (
          <div key={`empty-${index}`} className="h-9"></div>
        ))}
        
        {/* Días del mes */}
        {Array.from({ length: getDaysInMonth(currentDate) }).map((_, index) => {
          const day = index + 1
          const status = getDayStatus(day)
          const tooltip = getDayTooltip(day);
          const isToday = day === new Date().getDate() && 
                         currentDate.getMonth() === new Date().getMonth() &&
                         currentDate.getFullYear() === new Date().getFullYear();
          
          return (
            <button
              key={day}
              type="button"
              onClick={(e) => {
                e.preventDefault();
                if (status !== 'past') {
                  handleDayClick(day);
                }
              }}
              disabled={status === 'past'}
              className={`h-9 rounded-lg flex items-center justify-center text-sm transition-colors cursor-pointer relative
                ${isToday ? 'ring-2 ring-black' : ''}
                ${getStatusStyles(status)}`}
              title={tooltip}
            >
              <span className={`${selectedDay === day ? 'font-bold' : ''}`}>
                {day}
              </span>
              
              {/* Indicador para días no laborables o vacaciones */}
              {(isDayOff(new Date(currentDate.getFullYear(), currentDate.getMonth(), day)) || 
                isVacationDay(new Date(currentDate.getFullYear(), currentDate.getMonth(), day))) && (
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-gray-500 rounded-full"></div>
              )}
              
              {/* Indicador para días con sesiones (ocupados) */}
              {status === 'booked' && (
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-red-500 rounded-full"></div>
              )}
            </button>
          )
        })}
      </div>

      {/* Modal de Estado del Día */}
      {isBookingModalOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-white p-6 rounded-2xl shadow-xl max-w-[320px] w-full mx-4">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">
                {selectedDay} de {currentDate.toLocaleString('es', { month: 'long' })}
              </h2>
              <button 
                onClick={() => setIsBookingModalOpen(false)}
                className="text-gray-400 hover:text-gray-500 transition-colors"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>

            <div className="py-4">
              {getDayStatus(selectedDay || 0) === 'available' ? (
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-green-100 mb-4">
                    <div className="w-6 h-6 text-green-600">✓</div>
                  </div>
                  <p className="text-lg font-medium text-green-600 mb-2">Día Disponible</p>
                  <p className="text-sm text-gray-600 mb-6">
                    Este día está disponible para agendar citas.
                  </p>
                  
                  <div className="space-y-3">
                    {hasSocialMedia('instagram') && (
                      <a
                        href={`https://ig.me/m/${getSocialMediaId('instagram')}?text=${getMessageText(selectedDay || 0)}`}
                        onClick={(e) => handleSocialClick('instagram', e)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center justify-center w-full px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-purple-500 via-pink-500 to-orange-500 rounded-xl hover:opacity-90 transition-opacity"
                      >
                        <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M12 0C5.373 0 0 4.974 0 11.111c0 3.498 1.744 6.614 4.469 8.654V24l4.088-2.242c1.092.301 2.246.464 3.443.464 6.627 0 12-4.975 12-11.111S18.627 0 12 0zm1.191 14.963l-3.055-3.26-5.963 3.26L10.732 8l3.131 3.259L19.752 8l-6.561 6.963z"/>
                        </svg>
                        Agendar por Instagram
                      </a>
                    )}
                    
                    {hasSocialMedia('facebook') && (
                      <a
                        href={`https://m.me/${getSocialMediaId('facebook')}?ref=tatu_booking&text=${getMessageText(selectedDay || 0)}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center justify-center w-full px-4 py-2.5 text-sm font-medium text-white bg-[#0084ff] rounded-xl hover:opacity-90 transition-opacity"
                      >
                        <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M12 0C5.373 0 0 4.974 0 11.111c0 3.498 1.744 6.614 4.469 8.654V24l4.088-2.242c1.092.301 2.246.464 3.443.464 6.627 0 12-4.975 12-11.111S18.627 0 12 0zm1.191 14.963l-3.055-3.26-5.963 3.26L10.732 8l3.131 3.259L19.752 8l-6.561 6.963z"/>
                        </svg>
                        Agendar por Messenger
                      </a>
                    )}
                    
                    {hasSocialMedia('tiktok') && (
                      <a
                        href={`https://www.tiktok.com/@${getSocialMediaId('tiktok')}`}
                        onClick={(e) => handleSocialClick('tiktok', e)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center justify-center w-full px-4 py-2.5 text-sm font-medium text-white bg-[#010101] rounded-xl hover:opacity-90 transition-opacity"
                      >
                        <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z"/>
                        </svg>
                        Ver perfil en TikTok
                      </a>
                    )}
                  </div>
                </div>
              ) : getDayStatus(selectedDay || 0) === 'booked' ? (
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-red-100 mb-4">
                    <div className="w-6 h-6 text-red-600">×</div>
                  </div>
                  <p className="text-lg font-medium text-red-600 mb-2">Día Ocupado</p>
                  <p className="text-sm text-gray-600">
                    Este día ya está ocupado con otras sesiones.
                  </p>
                </div>
              ) : (
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-4">
                    <div className="w-6 h-6 text-gray-600">×</div>
                  </div>
                  <p className="text-lg font-medium text-gray-600 mb-2">Día No Laborable</p>
                  <p className="text-sm text-gray-600">
                    Este día no está disponible para agendar citas.
                  </p>
                </div>
              )}
            </div>

            <div className="mt-6">
              <button
                onClick={() => setIsBookingModalOpen(false)}
                className="w-full px-4 py-2.5 text-sm font-medium text-gray-700 bg-gray-100 rounded-xl hover:bg-gray-200 transition-colors"
              >
                Cerrar
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal para Copiar Mensaje */}
      {isMessageModalOpen && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[60] flex items-center justify-center">
          <div className="bg-white p-6 rounded-2xl shadow-xl max-w-[320px] w-full mx-4">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">
                Copiar mensaje
              </h2>
              <button 
                onClick={() => setIsMessageModalOpen(false)}
                className="text-gray-400 hover:text-gray-500 transition-colors"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>

            <div className="py-4">
              <p className="text-sm text-gray-600 mb-3">
                Copia este mensaje para enviarlo por {selectedPlatform === 'instagram' ? 'Instagram' : selectedPlatform === 'facebook' ? 'Messenger' : 'TikTok'}:
              </p>
              
              <div className="relative">
                <textarea
                  className="w-full p-3 border border-gray-300 rounded-lg text-sm text-gray-700 min-h-[100px] focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                  value={getPlainMessageText(selectedDay || 0)}
                  readOnly
                />
                
                <button
                  onClick={handleCopyMessage}
                  className="absolute top-2 right-2 bg-gray-100 text-gray-600 p-1.5 rounded-md hover:bg-gray-200 transition-colors"
                  title="Copiar al portapapeles"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                    <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                  </svg>
                </button>
              </div>
              
              {/* Mensaje de confirmación de copia */}
              {isCopied && (
                <div className="mt-2 text-sm text-green-600 flex items-center justify-center">
                  <svg className="w-4 h-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  ¡Mensaje copiado al portapapeles!
                </div>
              )}
            </div>

            <div className="flex justify-between mt-4 space-x-3">
              <button
                onClick={() => setIsMessageModalOpen(false)}
                className="flex-1 px-4 py-2.5 text-sm font-medium text-gray-700 bg-gray-100 rounded-xl hover:bg-gray-200 transition-colors"
              >
                Cancelar
              </button>
              
              <a
                href={
                  selectedPlatform === 'instagram' 
                    ? `https://www.instagram.com/${getSocialMediaId('instagram')}`
                    : selectedPlatform === 'facebook' 
                      ? `https://m.me/${getSocialMediaId('facebook')}`
                      : `https://www.tiktok.com/@${getSocialMediaId('tiktok')}`
                }
                target="_blank"
                rel="noopener noreferrer"
                className="flex-1 px-4 py-3 text-sm font-medium text-white bg-blue-600 rounded-xl hover:bg-blue-700 transition-colors text-center flex items-center justify-center shadow-md border border-blue-500"
                onClick={() => setIsMessageModalOpen(false)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                Ir a plataforma
              </a>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
