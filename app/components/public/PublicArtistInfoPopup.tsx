'use client';

import React from 'react';
import { X, Award, Clock } from 'lucide-react';
import { MapPin, Globe, Palette, CheckCircle, AlertCircle, XCircle } from 'lucide-react';

interface PublicArtistInfoPopupProps {
  onClose: () => void;
  profile: any;
}

// No más datos predeterminados

export default function PublicArtistInfoPopup({ onClose, profile }: PublicArtistInfoPopupProps) {
  // NOTA IMPORTANTE: Estamos recibiendo el perfil directamente desde Firestore
  // La estructura correcta debería ser profile (raíz del documento)
  console.log('[PublicArtistInfoPopup] Perfil recibido para depuración:', profile);
  
  // Si no hay perfil, mostrar un mensaje
  if (!profile) {
    console.log('[PublicArtistInfoPopup] No se recibió perfil');
    return (
      <div className="p-6 bg-white rounded-lg shadow-lg max-w-md mx-auto">
        <div className="flex justify-between items-start">
          <h3 className="text-xl font-semibold">Información Profesional</h3>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            ×
          </button>
        </div>
        <p className="mt-4 text-gray-600">No se pudo cargar la información del artista.</p>
      </div>
    );
  }
  
  // Obtener datos del documento de Firestore correctamente
  // La estructura real de datos es un documento con estos campos
  console.log('[PublicArtistInfoPopup] Analizando estructura de datos...');

  // 1. Intentar obtener datos profesionales de diferentes rutas posibles
  // La ruta correcta es profile.professional (sin profile.profile)
  const professionalData = profile?.profile?.professional || 
                         profile?.professional || 
                         {};
  console.log('[PublicArtistInfoPopup] Datos profesionales:', professionalData);

  // 2. Intentar obtener datos de estudio de diferentes rutas posibles
  // La ruta correcta es profile.contact.studio (sin profile.profile)
  const studioData = profile?.contact?.studio || 
                   profile?.profile?.contact?.studio || 
                   {};
  console.log('[PublicArtistInfoPopup] Datos de estudio:', studioData);

  // 3. Obtener certificaciones - requieren un manejo especial
  // La ruta correcta es profile.professional.requiredCertifications
  const requiredCertifications = professionalData?.requiredCertifications || {};
  const optionalCertifications = professionalData?.optionalCertifications || [];
  console.log('[PublicArtistInfoPopup] Certificaciones requeridas:', requiredCertifications);
  console.log('[PublicArtistInfoPopup] Certificaciones opcionales:', optionalCertifications);

  // Definir interfaces para los tipos de certificaciones
  interface RequiredCertData {
    id: string;
    title: string;
    status: string;
    issueDate: string;
    expiryDate?: string;
    isRequired: true;
  }

  interface OptionalCertData {
    id: string;
    title: string;
    status: string;
    issueDate: string;
    institution?: string;
    isRequired: false;
  }

  type CertificationData = RequiredCertData | OptionalCertData;

  // 4. Procesar certificaciones requeridas
  const processedRequiredCertifications: RequiredCertData[] = Object.keys(requiredCertifications).length > 0 
    ? Object.entries(requiredCertifications).map(([id, data]: [string, any]) => {
        return {
          id,
          title: getCertificationTitle(id),
          status: data.status || 'pending',
          issueDate: data.issueDate,
          expiryDate: data.expiryDate,
          isRequired: true as const
        };
      })
    : [];

  // 5. Procesar certificaciones opcionales
  const processedOptionalCertifications: OptionalCertData[] = Array.isArray(optionalCertifications) 
    ? optionalCertifications.map((cert: any, index: number) => {
        return {
          id: `optional-${index}`,
          title: cert.title || 'Certificación',
          status: 'verified', // Las opcionales se consideran siempre verificadas
          issueDate: cert.date,
          institution: cert.institution,
          isRequired: false as const
        };
      })
    : [];

  // 6. Combinar todos los tipos de certificaciones
  const processedCertifications: CertificationData[] = [...processedRequiredCertifications, ...processedOptionalCertifications];

  // 7. Obtener especialidades e idiomas
  const specialties = Array.isArray(professionalData?.specialties) ? professionalData.specialties : [];
  const languages = Array.isArray(professionalData?.languages) ? professionalData.languages : [];

  // 8. Obtener dirección del estudio
  const fullAddress = studioData?.fullAddress || 'No especificado';
  
  // Función para obtener el título legible de una certificación
  function getCertificationTitle(certId: string): string {
    const certTitles: {[key: string]: string} = {
      'seremiRegistry': 'Registro SEREMI de Salud',
      'biosafety': 'Curso de Bioseguridad',
      'firstAid': 'Primeros Auxilios',
      'bloodbornePathogens': 'Patógenos Transmitidos por Sangre',
      'crossContamination': 'Prevención de Contaminación Cruzada'
    };
    
    return certTitles[certId] || certId;
  }
  
  // Función para renderizar el icono según el estado de la certificación
  const renderStatusIcon = (status: string) => {
    switch (status) {
      case 'verified':
        return <CheckCircle size={14} className="text-green-500" />;
      case 'pending':
        return <Clock size={14} className="text-amber-500" />;
      case 'rejected':
        return <XCircle size={14} className="text-red-500" />;
      default:
        return <AlertCircle size={14} className="text-gray-400" />;
    }
  };
  
  // Función para obtener el texto de estado
  const getStatusText = (status: string) => {
    switch (status) {
      case 'verified':
        return 'Verificado';
      case 'pending':
        return 'En revisión';
      case 'rejected':
        return 'No verificado';
      default:
        return 'Desconocido';
    }
  };

  // Verificar si hay alguna información para mostrar
  const hasAnyInfo = processedCertifications.length > 0 || specialties.length > 0 || languages.length > 0 || fullAddress !== 'No especificado';

  return (
    <div className="space-y-4 max-h-[80vh] overflow-y-auto">
      <div className="flex justify-between items-start sticky top-0 bg-white py-2 z-10">
        <h3 className="text-lg font-medium">Información Profesional</h3>
        <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
          <X size={20} />
        </button>
      </div>
      
      {!hasAnyInfo ? (
        <div className="py-4 text-center">
          <p className="text-gray-500 text-sm">El artista aún no ha configurado su información profesional.</p>
        </div>
      ) : (
        <div className="space-y-5">
          {/* Sección de Certificaciones - Solo mostrar si hay certificaciones */}
          {processedCertifications.length > 0 && (
            <div className="mt-6">
              <h4 className="font-medium text-gray-900 mb-2">Certificaciones</h4>
              <div className="space-y-2">
                {processedCertifications.map((cert) => (
                  <div key={cert.id} className="p-3 bg-gray-50 rounded-md">
                    <div className="flex justify-between items-start">
                      <div>
                        <span className="font-medium">{cert.title}</span>
                        {!cert.isRequired && 'institution' in cert && cert.institution && (
                          <div className="text-sm text-gray-600">
                            Institución: {cert.institution}
                          </div>
                        )}
                        <div className="text-sm text-gray-500 mt-1">
                          {/* Certificaciones requeridas */}
                          {cert.isRequired ? (
                            <>
                              {cert.status === 'verified' && (
                                <div className="flex items-center text-green-600">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-4 w-4 mr-1"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                    />
                                  </svg>
                                  <span>Verificado por Tatu</span>
                                </div>
                              )}
                              {cert.status === 'pending' && (
                                <div className="flex items-center text-yellow-600">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-4 w-4 mr-1"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                    />
                                  </svg>
                                  <span>Pendiente de verificación</span>
                                </div>
                              )}
                              {cert.status === 'rejected' && (
                                <div className="flex items-center text-red-600">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="h-4 w-4 mr-1"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
                                    />
                                  </svg>
                                  <span>Verificación rechazada</span>
                                </div>
                              )}
                              <p className="mt-1">
                                Fecha: {new Date(cert.issueDate).toLocaleDateString()}
                                {cert.isRequired && 'expiryDate' in cert && cert.expiryDate && (
                                  <> · Vence: {new Date(cert.expiryDate).toLocaleDateString()}</>  
                                )}
                              </p>
                            </>
                          ) : (
                            /* Certificaciones opcionales */
                            <>
                              <div className="flex items-center text-blue-600">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-4 w-4 mr-1"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                  />
                                </svg>
                                <span>Certificación profesional</span>
                              </div>
                              <p className="mt-1">
                                Fecha: {new Date(cert.issueDate).toLocaleDateString()}
                              </p>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {/* Especialidades */}
          {specialties.length > 0 && (
            <div className="flex items-start">
              <Palette className="text-gray-500 mr-2 shrink-0 mt-1" size={18} />
              <div>
                <h4 className="text-sm font-medium">Especialidades</h4>
                <div className="flex flex-wrap gap-1 mt-1">
                  {specialties.map((specialty: string, index: number) => (
                    <span key={index} className="text-xs px-2 py-1 bg-gray-100 rounded-full">
                      {specialty}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          )}
          
          {/* Idiomas */}
          {languages.length > 0 && (
            <div className="flex items-start">
              <Globe className="text-gray-500 mr-2 shrink-0 mt-1" size={18} />
              <div>
                <h4 className="text-sm font-medium">Idiomas</h4>
                <div className="flex flex-wrap gap-1 mt-1">
                  {languages.map((language: string, index: number) => (
                    <span key={index} className="text-xs px-2 py-1 bg-gray-100 rounded-full">
                      {language}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          )}
          
          {/* Ubicación */}
          {fullAddress !== 'No especificado' && (
            <div className="flex items-start">
              <MapPin className="text-gray-500 mr-2 shrink-0 mt-1" size={18} />
              <div>
                <h4 className="text-sm font-medium">Ubicación del Estudio</h4>
                <p className="text-xs mt-1">{fullAddress}</p>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
