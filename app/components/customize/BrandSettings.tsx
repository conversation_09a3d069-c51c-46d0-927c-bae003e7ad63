'use client'

import React, { useEffect, useState } from 'react'
import { useCustomization } from './CustomizationContext'
import { BrandConfig } from './CustomizationContext'
import { Instagram, Facebook } from 'lucide-react'
import { useAuth } from '@/app/contexts/AuthContext'
import { doc, getDoc } from 'firebase/firestore'
import { db } from '@/lib/firebase/config'

export default function BrandSettings() {
  const { config, updateBrandConfig } = useCustomization()
  const { user } = useAuth()
  const [userContactInfo, setUserContactInfo] = useState<{instagram?: string, facebook?: string}>({})
  const [loading, setLoading] = useState(false)

  // Obtener la información de contacto del usuario desde el onboarding
  useEffect(() => {
    if (!user) return;
    
    const fetchUserContactInfo = async () => {
      setLoading(true);
      try {
        const userRef = doc(db, 'users', user.uid);
        const userDoc = await getDoc(userRef);
        
        if (userDoc.exists()) {
          const userData = userDoc.data();
          if (userData.contactInfo) {
            setUserContactInfo({
              instagram: userData.contactInfo.instagram,
              facebook: userData.contactInfo.facebook
            });
            
            // Actualizar la configuración con los datos del onboarding si los campos están vacíos
            if (!config.brand.instagram && userData.contactInfo.instagram) {
              updateBrandConfig({ instagram: userData.contactInfo.instagram });
            }
            if (!config.brand.facebook && userData.contactInfo.facebook) {
              updateBrandConfig({ facebook: userData.contactInfo.facebook });
            }
          }
        }
      } catch (error) {
        console.error('Error al cargar información de contacto del usuario:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchUserContactInfo();
  }, [user, config.brand.instagram, config.brand.facebook, updateBrandConfig]);

  const handleChange = (field: keyof BrandConfig, value: string) => {
    updateBrandConfig({ [field]: value })
  }

  return (
    <div className="space-y-6">
      {/* Nombre del Estudio */}
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
          Nombre del Estudio
        </label>
        <input
          type="text"
          id="name"
          value={config.brand.name}
          onChange={(e) => handleChange('name', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
          placeholder="Nombre de tu estudio"
        />
      </div>

      {/* Descripción */}
      <div>
        <label htmlFor="slogan" className="block text-sm font-medium text-gray-700 mb-2">
          Descripción
        </label>
        <textarea
          id="slogan"
          value={config.brand.slogan}
          onChange={(e) => handleChange('slogan', e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
          placeholder="Describe tu estudio en pocas palabras"
        />
      </div>

      {/* Redes Sociales */}
      <div className="space-y-4">
        <label className="block text-sm font-medium text-gray-700">
          Redes Sociales
        </label>

        {/* Instagram */}
        <div>
          <div className="flex items-center gap-3">
            <Instagram className="w-5 h-5 text-gray-400" />
            <input
              type="text"
              value={config.brand.instagram}
              onChange={(e) => handleChange('instagram', e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
              placeholder="Usuario de Instagram"
            />
          </div>
          {userContactInfo.instagram && userContactInfo.instagram !== config.brand.instagram && (
            <p className="mt-1 text-xs text-blue-600">
              Sugerencia: Usaste "{userContactInfo.instagram}" durante el registro. 
              <button 
                type="button"
                onClick={() => handleChange('instagram', userContactInfo.instagram || '')}
                className="ml-1 text-blue-700 hover:underline"
              >
                Usar este
              </button>
            </p>
          )}
        </div>

        {/* Facebook */}
        <div>
          <div className="flex items-center gap-3">
            <Facebook className="w-5 h-5 text-gray-400" />
            <input
              type="text"
              value={config.brand.facebook}
              onChange={(e) => handleChange('facebook', e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
              placeholder="Usuario o URL de Facebook"
            />
          </div>
          {userContactInfo.facebook && userContactInfo.facebook !== config.brand.facebook && (
            <p className="mt-1 text-xs text-blue-600">
              Sugerencia: Usaste "{userContactInfo.facebook}" durante el registro. 
              <button 
                type="button"
                onClick={() => handleChange('facebook', userContactInfo.facebook || '')}
                className="ml-1 text-blue-700 hover:underline"
              >
                Usar este
              </button>
            </p>
          )}
        </div>

        {/* TikTok */}
        <div className="flex items-center gap-3">
          <svg 
            className="w-5 h-5 text-gray-400" 
            viewBox="0 0 24 24" 
            fill="currentColor"
          >
            <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z"/>
          </svg>
          <input
            type="text"
            value={config.brand.tiktok}
            onChange={(e) => handleChange('tiktok', e.target.value)}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
            placeholder="Usuario de TikTok"
          />
        </div>
      </div>
    </div>
  )
}
