'use client'

import { createContext, useContext, useState, useEffect } from 'react'
import { db } from '@/lib/firebase/config'
import { collection, query, where, getDocs, addDoc, serverTimestamp, doc, updateDoc } from 'firebase/firestore'
import { useAuth } from '@/contexts/AuthContext'

interface ContactInfo {
  email?: string
  phone?: string
  instagram?: string
  facebook?: string
}

interface Client {
  id: string
  name: string
  lastName: string
  contactInfo: ContactInfo
  conversationId: string | null
  profilePicture?: string
  type: 'new' | 'recurring'
  totalSessions: number
  totalSpent: number
  paymentStatus: 'up_to_date' | 'pending'
  createdAt: Date
  artistId: string
}

interface Filters {
  type: string
  paymentStatus: string
  dateRange: string
}

interface ClientContextType {
  clients: Client[]
  addClient: (client: Omit<Client, 'id' | 'createdAt' | 'artistId' | 'type' | 'totalSessions' | 'totalSpent' | 'paymentStatus'> & { conversationId?: string; profilePicture?: string }) => Promise<void>
  searchQuery: string
  setSearchQuery: (query: string) => void
  filters: Filters
  setFilters: (filters: Filters) => void
  selectedClient: Client | null
  setSelectedClient: (client: Client | null) => void
  updateClient: (client: Client) => Promise<void>
  fetchClients: () => Promise<void>
}

const ClientContext = createContext<ClientContextType | undefined>(undefined)

export function ClientProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth()
  const [clients, setClients] = useState<Client[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedClient, setSelectedClient] = useState<Client | null>(null)
  const [filters, setFilters] = useState<Filters>({
    type: 'all',
    paymentStatus: 'all',
    dateRange: 'all'
  })

  // Función para cargar clientes
  const loadClients = async () => {
    if (!user) return
    
    try {
      const q = query(
        collection(db, 'clients'),
        where('artistId', '==', user.uid)
      )
      const querySnapshot = await getDocs(q)
      const loadedClients = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date()
      })) as Client[]
      console.log('Clientes cargados:', loadedClients)
      setClients(loadedClients)
    } catch (error) {
      console.error('Error al cargar clientes:', error)
    }
  }
  
  // Función pública para recargar clientes
  const fetchClients = async () => {
    return await loadClients()
  }
  
  // Cargar clientes al iniciar
  useEffect(() => {
    if (!user) return
    loadClients()
  }, [user])

  // Agregar cliente
  const addClient = async (clientData: Omit<Client, 'id' | 'createdAt' | 'artistId' | 'type' | 'totalSessions' | 'totalSpent' | 'paymentStatus'> & { conversationId?: string; profilePicture?: string }) => {
    if (!user) throw new Error('No hay usuario autenticado')

    try {
      const newClientData = {
        ...clientData,
        artistId: user.uid,
        type: 'new' as const,
        totalSessions: 0,
        totalSpent: 0,
        paymentStatus: 'up_to_date' as const,
        createdAt: serverTimestamp(),
        conversationId: clientData.conversationId || null,
        profilePicture: clientData.profilePicture || null
      }

      const docRef = await addDoc(collection(db, 'clients'), newClientData)

      const newClient: Client = {
        id: docRef.id,
        ...clientData,
        artistId: user.uid,
        type: 'new',
        totalSessions: 0,
        totalSpent: 0,
        paymentStatus: 'up_to_date',
        createdAt: new Date(),
        conversationId: clientData.conversationId || null,
        profilePicture: clientData.profilePicture || undefined
      }

      setClients(prev => [...prev, newClient])
    } catch (error) {
      console.error('Error al agregar cliente:', error)
      throw error
    }
  }

  // Actualizar cliente
  const updateClient = async (updatedClient: Client) => {
    if (!user) return

    const clientRef = doc(db, 'clients', updatedClient.id)
    await updateDoc(clientRef, updatedClient)

    // Actualizar el estado local
    setClients(prevClients =>
      prevClients.map(client =>
        client.id === updatedClient.id ? updatedClient : client
      )
    )

    // Si el cliente actualizado es el seleccionado, actualizar también selectedClient
    if (selectedClient?.id === updatedClient.id) {
      setSelectedClient(updatedClient)
    }
  }

  // Filtrar clientes
  const filteredClients = clients
    .filter(client => {
      // Búsqueda por texto
      const searchText = searchQuery.toLowerCase()
      const matchesSearch =
        client.name.toLowerCase().includes(searchText) ||
        client.lastName.toLowerCase().includes(searchText) ||
        client.contactInfo.email?.toLowerCase().includes(searchText) ||
        client.contactInfo.phone?.toLowerCase().includes(searchText) ||
        client.contactInfo.instagram?.toLowerCase().includes(searchText) ||
        client.contactInfo.facebook?.toLowerCase().includes(searchText)

      if (!matchesSearch) return false

      // Filtros
      if (filters.type !== 'all' && client.type !== filters.type) return false
      if (filters.paymentStatus !== 'all' && client.paymentStatus !== filters.paymentStatus) return false

      // Filtro por fecha
      if (filters.dateRange !== 'all') {
        const now = new Date()
        const clientDate = client.createdAt
        const monthsDiff = (now.getTime() - clientDate.getTime()) / (1000 * 60 * 60 * 24 * 30)

        switch (filters.dateRange) {
          case 'last_month':
            if (monthsDiff > 1) return false
            break
          case 'last_3_months':
            if (monthsDiff > 3) return false
            break
          case 'last_6_months':
            if (monthsDiff > 6) return false
            break
        }
      }

      return true
    })
    .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())

  const value = {
    clients: filteredClients,
    addClient,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    selectedClient,
    setSelectedClient,
    updateClient,
    fetchClients
  }

  return <ClientContext.Provider value={value}>{children}</ClientContext.Provider>
}

export function useClients() {
  const context = useContext(ClientContext)
  if (context === undefined) {
    throw new Error('useClients debe ser usado dentro de un ClientProvider')
  }
  return context
}
