'use client'

import React, { useEffect, useState } from 'react'
import { useAuth } from '@/app/contexts/AuthContext'
import { db } from '@/lib/firebase'
import { doc, getDoc, updateDoc } from 'firebase/firestore'

interface NotificationPreferences {
  email: {
    updates: boolean;
    promotions: boolean;
  };
  push: {
    potentialClients: boolean;
  };
}

const defaultPreferences: NotificationPreferences = {
  email: {
    updates: true,
    promotions: true,
  },
  push: {
    potentialClients: true,
  },
}

export default function NotificationsSection() {
  const { user } = useAuth()
  const [preferences, setPreferences] = useState<NotificationPreferences>(defaultPreferences)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    const fetchPreferences = async () => {
      if (!user?.uid) return

      try {
        const userRef = doc(db, 'users', user.uid)
        const userDoc = await getDoc(userRef)

        if (userDoc.exists()) {
          const userData = userDoc.data()
          if (userData.notificationPreferences) {
            setPreferences(userData.notificationPreferences)
          } else {
            // Si no existen preferencias, establecer las predeterminadas
            await updateDoc(userRef, {
              notificationPreferences: defaultPreferences,
              updatedAt: new Date(),
            })
          }
        }
      } catch (error) {
        console.error('Error fetching notification preferences:', error)
      }
    }

    fetchPreferences()
  }, [user])

  const handleToggle = async (type: 'email' | 'push', setting: string) => {
    if (!user?.uid) return

    setIsLoading(true)
    try {
      // Actualizar estado local
      const newPreferences = {
        ...preferences,
        [type]: {
          ...preferences[type],
          [setting]: !preferences[type][setting as keyof typeof preferences[typeof type]],
        },
      }
      setPreferences(newPreferences)

      // Preparar datos para actualizar en Firestore
      const updateData: any = {
        notificationPreferences: newPreferences,
        updatedAt: new Date(),
      }
      
      // Si es la preferencia de promociones, actualizar también marketingConsent
      if (type === 'email' && setting === 'promotions') {
        const marketingConsentValue = !preferences.email.promotions
        updateData.marketingConsent = marketingConsentValue
        console.log(`Actualizando marketingConsent a: ${marketingConsentValue}`)
      }

      // Actualizar en Firestore
      const userRef = doc(db, 'users', user.uid)
      await updateDoc(userRef, updateData)

      console.log(`${type}.${setting} toggled successfully`)
    } catch (error) {
      console.error('Error updating notification preference:', error)
      // Revertir cambio local si hay error
      setPreferences(preferences)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900">Notificaciones</h3>
        <p className="mt-1 text-sm text-gray-500">
          Personaliza cómo y cuándo quieres recibir notificaciones.
        </p>
      </div>

      {/* Notificaciones Generales */}
      <div className="bg-white shadow rounded-lg p-6">
        <h4 className="text-base font-medium text-gray-900 mb-4">
          Notificaciones Generales
        </h4>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">Actualizaciones</p>
              <p className="text-sm text-gray-500">
                Recibe notificaciones sobre nuevas funcionalidades y mejoras.
              </p>
            </div>
            <button
              onClick={() => handleToggle('email', 'updates')}
              disabled={isLoading}
              className={`relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black ${
                preferences.email.updates ? 'bg-black' : 'bg-gray-200'
              }`}
            >
              <span
                className={`pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200 ${
                  preferences.email.updates ? 'translate-x-5' : 'translate-x-0'
                }`}
              />
            </button>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">Promociones</p>
              <p className="text-sm text-gray-500">
                Recibe notificaciones sobre ofertas y promociones.
              </p>
            </div>
            <button
              onClick={() => handleToggle('email', 'promotions')}
              disabled={isLoading}
              className={`relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black ${
                preferences.email.promotions ? 'bg-black' : 'bg-gray-200'
              }`}
            >
              <span
                className={`pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200 ${
                  preferences.email.promotions ? 'translate-x-5' : 'translate-x-0'
                }`}
              />
            </button>
          </div>
        </div>
      </div>

      {/* Notificaciones de Tatu.Ink */}
      <div className="bg-white shadow rounded-lg p-6">
        <h4 className="text-base font-medium text-gray-900 mb-4">
          Notificaciones de Tatu.Ink
        </h4>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">Clientes potenciales</p>
              <p className="text-sm text-gray-500">
                Recibe notificaciones y correos sobre nuevos clientes potenciales detectados por la IA.
              </p>
            </div>
            <button
              onClick={() => handleToggle('push', 'potentialClients')}
              disabled={isLoading}
              className={`relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black ${
                preferences.push.potentialClients ? 'bg-black' : 'bg-gray-200'
              }`}
            >
              <span
                className={`pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200 ${
                  preferences.push.potentialClients ? 'translate-x-5' : 'translate-x-0'
                }`}
              />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
