'use client'

import React, { useState, useEffect, useRef } from 'react'
import { useAuth } from '@/app/contexts/AuthContext'
import { doc, getDoc, setDoc, updateDoc, collection, query, where, getDocs, onSnapshot } from 'firebase/firestore'
import { db, rtdb } from '@/lib/firebase'
import { ref, get, remove } from 'firebase/database'
import { toast } from 'react-hot-toast'
import { CheckCircleIcon, ArrowPathIcon } from '@heroicons/react/24/outline'
import Image from 'next/image'
import ModalPortal from '../customize/ModalPortal'

// Tipo para la información de la cuenta
interface AccountInfo {
  id: string;
  platform: string;
  status?: string;
  errorMessage?: string;
}

// Componente para mostrar una tarjeta de plataforma
interface PlatformCardProps {
  name: string;
  icon: string;
  iconFallback: string;
  connected: boolean;
  hasError?: boolean;
  errorMessage?: string;
  onDelete?: (platform: string) => void;
  onReconnect?: (platform: string) => void;
}

function PlatformCard({ name, icon, iconFallback, connected, hasError, errorMessage, onDelete, onReconnect }: PlatformCardProps) {
  // Determinar el estilo basado en el estado
  const getCardStyle = () => {
    if (hasError) {
      return 'border-red-200 bg-red-50 shadow-sm';
    } else if (connected) {
      return 'border-green-200 bg-green-50 shadow-sm cursor-pointer hover:bg-green-100';
    } else {
      return 'border-gray-200 bg-gray-50 hover:border-gray-300 hover:bg-gray-100';
    }
  };

  return (
    <div
      className={`rounded-lg p-4 flex flex-col items-center justify-center border transition-all duration-300 ${getCardStyle()}`}
      onClick={connected && onDelete && !hasError ? () => onDelete(name.toLowerCase()) : undefined}
    >
      <div className="relative h-14 w-14 mb-3">
        {/* Indicador de estado (conectado/desconectado/error) */}
        <div className={`absolute -top-1 -right-1 h-5 w-5 rounded-full border-2 border-white z-10 ${
          hasError ? 'bg-red-500' : (connected ? 'bg-green-500' : 'bg-gray-300')
        }`}></div>

        {/* Icono de la plataforma */}
        {icon ? (
          <div className="relative h-full w-full rounded-full overflow-hidden bg-white p-2 shadow-sm">
            <Image
              src={icon}
              alt={name}
              width={48}
              height={48}
              className="object-contain h-full w-full"
              onError={(e) => {
                // Fallback para cuando la imagen no carga
                e.currentTarget.style.display = 'none';
                const parent = e.currentTarget.parentElement;
                if (parent) {
                  const fallbackEl = document.createElement('div');
                  fallbackEl.className = 'h-full w-full flex items-center justify-center bg-gray-100 rounded-full';
                  fallbackEl.innerHTML = `<span class="text-gray-500 text-xl font-bold">${iconFallback.charAt(0).toUpperCase()}</span>`;
                  parent.appendChild(fallbackEl);
                }
              }}
            />
          </div>
        ) : (
          <div className="h-full w-full flex items-center justify-center bg-gray-200 rounded-full">
            <span className="text-gray-500 text-xl font-bold">{iconFallback.charAt(0).toUpperCase()}</span>
          </div>
        )}
      </div>

      {/* Nombre de la plataforma */}
      <span className="text-sm font-medium text-gray-700">{name}</span>

      {/* Estado de conexión */}
      <div className={`mt-2 flex items-center text-xs ${
        hasError ? 'text-red-600' : (connected ? 'text-green-600' : 'text-gray-500')
      }`}>
        {hasError ? (
          <>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            Error
          </>
        ) : connected ? (
          <>
            <CheckCircleIcon className="h-4 w-4 mr-1" />
            Conectado
          </>
        ) : (
          <>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
            </svg>
            Sin conectar
          </>
        )}
      </div>

      {/* Indicadores de acción */}
      {hasError ? (
        <div className="mt-2 text-xs text-red-600">
          <button
            onClick={(e) => {
              e.stopPropagation();
              if (onReconnect) onReconnect(name.toLowerCase());
            }}
            className="flex items-center hover:text-red-700 transition-colors"
          >
            <ArrowPathIcon className="h-3 w-3 mr-1" />
            Reconectar
          </button>
        </div>
      ) : connected ? (
        <div className="mt-2 text-xs text-gray-500">
          <span className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            Eliminar
          </span>
        </div>
      ) : null}
    </div>
  )
}

export default function UnipileIntegration() {
  const { user, loading } = useAuth()
  const [isConfigured, setIsConfigured] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [accountToDelete, setAccountToDelete] = useState<AccountInfo | null>(null)
  const [connectedAccounts, setConnectedAccounts] = useState<{
    whatsapp: boolean;
    instagram: boolean;
    messenger: boolean;
    telegram: boolean;
  }>({
    whatsapp: false,
    instagram: false,
    messenger: false,
    telegram: false
  })

  // Almacenar información detallada de las cuentas
  const [accountsInfo, setAccountsInfo] = useState<Record<string, AccountInfo | null>>({
    whatsapp: null,
    instagram: null,
    messenger: null,
    telegram: null
  })

  // Estado para manejar errores de cuentas
  const [accountErrors, setAccountErrors] = useState<Record<string, boolean>>({
    whatsapp: false,
    instagram: false,
    messenger: false,
    telegram: false
  })

  // Estado para manejar reconexiones en progreso
  const [isReconnecting, setIsReconnecting] = useState(false)

  // Estado para mostrar el conteo de conversaciones que serán eliminadas
  const [conversationsToDeleteCount, setConversationsToDeleteCount] = useState<number>(0)

  // Referencia para el unsubscribe del listener
  const unsubscribeRef = useRef<(() => void) | null>(null)

  // Cargar el estado de la configuración cuando el componente se monta
  // y configurar el listener para actualizaciones en tiempo real
  useEffect(() => {
    if (user?.uid) {
      fetchData()
      setupRealtimeListener()
    }

    // Limpiar el listener cuando el componente se desmonta
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current()
        unsubscribeRef.current = null
      }
    }
  }, [user])

  // Configurar listener en tiempo real para la colección unipileConnections
  const setupRealtimeListener = () => {
    if (!user?.uid) return

    try {
      // Limpiar cualquier listener existente
      if (unsubscribeRef.current) {
        unsubscribeRef.current()
      }

      // Crear una consulta para obtener solo los documentos del usuario actual
      const connectionsRef = collection(db, 'unipileConnections')
      const connectionsQuery = query(connectionsRef, where('userId', '==', user.uid))

      // Configurar el listener
      const unsubscribe = onSnapshot(connectionsQuery, (snapshot) => {
        console.log('🔴 Cambios detectados en unipileConnections')

        // Inicializar el estado de las conexiones
        const connections = {
          whatsapp: false,
          instagram: false,
          messenger: false,
          telegram: false
        }

        // Inicializar la información de las cuentas
        const accounts = {
          whatsapp: null,
          instagram: null,
          messenger: null,
          telegram: null
        }

        // Inicializar el estado de errores
        const errors = {
          whatsapp: false,
          instagram: false,
          messenger: false,
          telegram: false
        }

        // Procesar los documentos actualizados
        snapshot.forEach((doc) => {
          const connectionData = doc.data()
          console.log('Documento actualizado:', doc.id, connectionData)

          // Determinar el tipo de plataforma
          const platform = connectionData.platform?.toLowerCase() || ''
          console.log('Plataforma detectada (tiempo real):', platform)

          // Almacenar la información de la cuenta
          const accountInfo: AccountInfo = {
            id: connectionData.accountId || '',
            platform: platform,
            status: connectionData.status || 'unknown',
            errorMessage: connectionData.errorMessage || ''
          }

          // Determinar el estado de la cuenta
          const isActive = connectionData.active === true
          const hasError = connectionData.status === 'error'

          if (platform.includes('whatsapp')) {
            connections.whatsapp = isActive && !hasError
            accounts.whatsapp = accountInfo
            errors.whatsapp = hasError
            console.log(`WhatsApp - Conectado: ${isActive}, Error: ${hasError}`)
          } else if (platform.includes('instagram')) {
            connections.instagram = isActive && !hasError
            accounts.instagram = accountInfo
            errors.instagram = hasError
            console.log(`Instagram - Conectado: ${isActive}, Error: ${hasError}`)
          } else if (platform.includes('messenger') || platform.includes('facebook')) {
            connections.messenger = isActive && !hasError
            accounts.messenger = accountInfo
            errors.messenger = hasError
            console.log(`Messenger - Conectado: ${isActive}, Error: ${hasError}`)
          } else if (platform.includes('telegram')) {
            connections.telegram = isActive && !hasError
            accounts.telegram = accountInfo
            errors.telegram = hasError
            console.log(`Telegram - Conectado: ${isActive}, Error: ${hasError}`)
          }
        })

        // Actualizar el estado con las conexiones encontradas
        console.log('Estado final de conexiones (tiempo real):', connections)
        console.log('Estado final de errores (tiempo real):', errors)
        setConnectedAccounts(connections)
        setAccountsInfo(accounts)
        setAccountErrors(errors)

        // Verificar si hay alguna cuenta conectada para marcar como configurado
        if (connections.whatsapp || connections.instagram || connections.messenger || connections.telegram) {
          setIsConfigured(true)
        }
      }, (error) => {
        console.error('Error en el listener de Firestore:', error)
      })

      // Guardar la función de cancelación para limpiar cuando sea necesario
      unsubscribeRef.current = unsubscribe
      console.log('💬 Listener de Firestore configurado para unipileConnections')
    } catch (error) {
      console.error('Error al configurar el listener:', error)
    }
  }

  // Cargar datos de configuración (carga inicial)
  const fetchData = async () => {
    if (!user?.uid) return

    try {
      console.log('Consultando conexiones para el usuario:', user.uid)

      // Inicializar el estado de las conexiones
      const connections = {
        whatsapp: false,
        instagram: false,
        messenger: false,
        telegram: false
      }

      // Inicializar la información de las cuentas
      const accounts = {
        whatsapp: null,
        instagram: null,
        messenger: null,
        telegram: null
      }

      // Inicializar el estado de errores
      const errors = {
        whatsapp: false,
        instagram: false,
        messenger: false,
        telegram: false
      }

      // Consultar la colección unipileConnections para este usuario
      const connectionsRef = collection(db, 'unipileConnections')
      const connectionsQuery = query(connectionsRef, where('userId', '==', user.uid))
      const connectionsSnapshot = await getDocs(connectionsQuery)

      console.log(`Encontrados ${connectionsSnapshot.size} documentos en unipileConnections para el usuario ${user.uid}`)

      // Procesar los resultados de la consulta
      connectionsSnapshot.forEach((doc) => {
        const connectionData = doc.data()
        console.log('Documento encontrado:', doc.id, connectionData)

        // Determinar el tipo de plataforma
        const platform = connectionData.platform?.toLowerCase() || ''
        console.log('Plataforma detectada:', platform)

        // Almacenar la información de la cuenta
        const accountInfo = {
          id: connectionData.accountId || '',
          platform: platform,
          status: connectionData.status || 'unknown',
          errorMessage: connectionData.errorMessage || ''
        }

        // Determinar el estado de la cuenta
        const isActive = connectionData.active === true
        // Considerar tanto 'error' como problemas de credenciales como estados de error
        const hasError = connectionData.status === 'error' || connectionData.errorMessage?.includes('credenciales')

        if (platform.includes('whatsapp')) {
          connections.whatsapp = isActive && !hasError
          accounts.whatsapp = accountInfo
          errors.whatsapp = hasError
          console.log(`WhatsApp - Conectado: ${isActive}, Error: ${hasError}`)
        } else if (platform.includes('instagram')) {
          connections.instagram = isActive && !hasError
          accounts.instagram = accountInfo
          errors.instagram = hasError
          console.log(`Instagram - Conectado: ${isActive}, Error: ${hasError}`)
        } else if (platform.includes('messenger') || platform.includes('facebook')) {
          connections.messenger = isActive && !hasError
          accounts.messenger = accountInfo
          errors.messenger = hasError
          console.log(`Messenger - Conectado: ${isActive}, Error: ${hasError}`)
        } else if (platform.includes('telegram')) {
          connections.telegram = isActive && !hasError
          accounts.telegram = accountInfo
          errors.telegram = hasError
          console.log(`Telegram - Conectado: ${isActive}, Error: ${hasError}`)
        }
      })

      // Actualizar el estado con las conexiones encontradas
      console.log('Estado final de conexiones:', connections)
      console.log('Estado final de errores:', errors)
      setConnectedAccounts(connections)
      setAccountsInfo(accounts)
      setAccountErrors(errors)

      // Verificar si hay alguna cuenta conectada para marcar como configurado
      if (connections.whatsapp || connections.instagram || connections.messenger || connections.telegram) {
        setIsConfigured(true)
      }

      // También verificar en el documento del usuario por compatibilidad
      const docRef = doc(db, 'users', user.uid)
      const docSnap = await getDoc(docRef)

      if (docSnap.exists()) {
        const data = docSnap.data()

        if (data.unipileConfigured) {
          setIsConfigured(true)
        }
      }
    } catch (error) {
      console.error('Error al cargar los datos de Unipile:', error)
      toast.error('Error al cargar la información de conexiones')
    }
  }

  // Función para abrir una ventana popup
  const openPopup = (url: string) => {
    const width = 600
    const height = 700
    const left = window.innerWidth / 2 - width / 2
    const top = window.innerHeight / 2 - height / 2

    // Detectar si es un dispositivo móvil
    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent)

    if (isMobile) {
      // En móviles, abrir en la misma ventana
      window.location.href = url
    } else {
      // En desktop, abrir popup
      window.open(
        url,
        'unipile',
        `width=${width},height=${height},top=${top},left=${left},resizable=yes,scrollbars=yes,status=yes`
      )
    }
  };

  // Función para manejar la reconexión de una cuenta
  const handleReconnectAccount = async (platform: string) => {
    if (!user?.uid) return;

    // Obtener la información de la cuenta según la plataforma
    let accountInfo: AccountInfo | null = null;

    if (platform === 'whatsapp') {
      accountInfo = accountsInfo.whatsapp;
    } else if (platform === 'instagram') {
      accountInfo = accountsInfo.instagram;
    } else if (platform === 'messenger') {
      accountInfo = accountsInfo.messenger;
    } else if (platform === 'telegram') {
      accountInfo = accountsInfo.telegram;
    }

    if (!accountInfo || !accountInfo.id) {
      toast.error(`No se encontró información de la cuenta de ${platform}`);
      return;
    }

    setIsReconnecting(true);

    try {
      console.log(`🔄 Iniciando reconexión para ${platform} (${accountInfo.id})`);

      // Llamar a la API de reconexión
      const response = await fetch('/api/unipile/reconnect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          accountId: accountInfo.id,
          userId: user.uid
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error al generar URL de reconexión');
      }

      const responseData = await response.json();

      if (responseData.url) {
        // Abrir la URL de reconexión en una ventana popup
        openPopup(responseData.url);
        toast.success(`Reconectando ${platform}...`, { duration: 5000 });
      } else {
        throw new Error('No se recibió una URL válida para la reconexión');
      }
    } catch (error) {
      console.error('Error al reconectar cuenta:', error);
      toast.error('Error al reconectar: ' + (error instanceof Error ? error.message : 'Error desconocido'));
    } finally {
      setIsReconnecting(false);
    }
  };

  // Función para contar conversaciones que serán eliminadas
  const countConversationsToDelete = async (accountId: string): Promise<number> => {
    try {
      console.log(`🔍 Iniciando conteo para accountId: ${accountId}`);
      console.log(`👤 Usuario actual: ${user?.uid}`);

      if (!user?.uid) {
        console.log('❌ No hay usuario autenticado');
        return 0;
      }

      const conversationsRef = ref(rtdb, `conversations/${user.uid}`);
      console.log(`📂 Consultando path: conversations/${user.uid}`);

      const conversationsSnapshot = await get(conversationsRef);
      console.log(`📊 Snapshot exists: ${conversationsSnapshot.exists()}`);

      if (conversationsSnapshot.exists()) {
        const conversationsData = conversationsSnapshot.val();
        console.log(`📋 Datos de conversaciones:`, Object.keys(conversationsData || {}));
        let count = 0;

        Object.entries(conversationsData).forEach(([conversationId, conversationData]: [string, any]) => {
          console.log(`🔍 Revisando conversación ${conversationId}:`, {
            accountId: conversationData?.accountId,
            targetAccountId: accountId,
            matches: conversationData?.accountId === accountId
          });

          if (conversationData && typeof conversationData === 'object' && conversationData.accountId === accountId) {
            count++;
            console.log(`✅ Conversación ${conversationId} coincide - Total: ${count}`);
          }
        });

        console.log(`📊 Total de conversaciones a eliminar: ${count}`);
        return count;
      }

      console.log('ℹ️ No existen conversaciones para este usuario');
      return 0;
    } catch (error) {
      console.error('❌ Error contando conversaciones:', error);
      return 0;
    }
  };

  // Función para mostrar el modal de confirmación de eliminación
  const handleDeleteAccount = async (platform: string) => {
    // Obtener la información de la cuenta según la plataforma
    let accountInfo: AccountInfo | null = null;

    if (platform === 'whatsapp') {
      accountInfo = accountsInfo.whatsapp;
    } else if (platform === 'instagram') {
      accountInfo = accountsInfo.instagram;
    } else if (platform === 'messenger') {
      accountInfo = accountsInfo.messenger;
    } else if (platform === 'telegram') {
      accountInfo = accountsInfo.telegram;
    }

    if (!accountInfo || !accountInfo.id) {
      toast.error(`No se encontró información de la cuenta de ${platform}`);
      return;
    }

    // Contar las conversaciones que serán eliminadas
    console.log(`🔍 Contando conversaciones para la cuenta: ${accountInfo.id}`);
    const count = await countConversationsToDelete(accountInfo.id);
    console.log(`📊 Conversaciones encontradas: ${count}`);
    setConversationsToDeleteCount(count);

    // Mostrar el modal de confirmación
    setAccountToDelete(accountInfo);
    setShowDeleteModal(true);
  };

  // Función para confirmar la eliminación de la cuenta
  const confirmDeleteAccount = async () => {
    if (!accountToDelete || !accountToDelete.id) {
      toast.error('No se encontró información de la cuenta a eliminar');
      setShowDeleteModal(false);
      return;
    }

    setIsDeleting(true);

    try {
      // Obtener la URL de la API y la clave API desde las variables de entorno
      const apiUrl = process.env.NEXT_PUBLIC_UNIPILE_API_URL || 'https://api10.unipile.com:14039';
      const apiKey = process.env.NEXT_PUBLIC_UNIPILE_API_KEY;

      if (!apiKey) {
        throw new Error('La clave API de Unipile no está configurada en las variables de entorno');
      }

      // Variables para controlar el flujo y los mensajes
      let accountExistsInUnipile = true;
      let unipileDeleteSuccess = false;

      try {
        // Realizar la solicitud DELETE a la API de Unipile
        const response = await fetch(`${apiUrl}/api/v1/accounts/${accountToDelete.id}`, {
          method: 'DELETE',
          headers: {
            'X-API-KEY': apiKey,
            'accept': 'application/json'
          }
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          console.error(`Error en API de Unipile: ${response.status} ${response.statusText}`, errorData);

          // Si el error es 404 (cuenta no encontrada)
          if (response.status === 404) {
            console.log('Cuenta no encontrada en Unipile, pero continuaremos con la actualización en Firestore');
            accountExistsInUnipile = false;
            // Continuamos con la actualización en Firestore
          } else {
            // Para otros errores, lanzamos la excepción
            throw new Error(`Error al eliminar la cuenta: ${response.statusText}. ${JSON.stringify(errorData)}`);
          }
        } else {
          // La eliminación en Unipile fue exitosa
          unipileDeleteSuccess = true;
        }
      } catch (apiError: any) {
        // Si el error es de red o cualquier otro error que no sea 404
        if (apiError.message && !apiError.message.includes('404')) {
          throw apiError; // Propagar el error
        }
        // Si es un error 404 o relacionado, continuamos con la actualización en Firestore
        console.warn('Error al comunicarse con Unipile, pero continuaremos con la actualización en Firestore:', apiError);
        accountExistsInUnipile = false;
      }

      // Mostrar mensaje apropiado según el resultado
      if (unipileDeleteSuccess) {
        toast.success(`Cuenta de ${accountToDelete.platform} desconectada correctamente`);
      } else if (!accountExistsInUnipile) {
        toast.success(`La cuenta ya no existe en Unipile. Se actualizará el estado local.`);
      }

      // Actualizar Firestore: marcar la cuenta como borrada
      try {
        if (!user?.uid) throw new Error('Usuario no autenticado');
        const connectionsRef = collection(db, 'unipileConnections');
        const q = query(
          connectionsRef,
          where('userId', '==', user.uid),
          where('accountId', '==', accountToDelete.id)
        );
        const snapshot = await getDocs(q);
        if (snapshot.empty) {
          console.warn('No se encontró el documento de la cuenta en Firestore para marcar como borrada');
        } else {
          snapshot.forEach(async (docu) => {
            await updateDoc(docu.ref, {
              active: false,
              deletedAt: new Date()
            });
          });
        }
      } catch (firestoreError) {
        console.error('Error actualizando Firestore tras eliminar la cuenta:', firestoreError);
        toast.error('Error actualizando Firestore tras eliminar la cuenta');
      }

      // Eliminar todas las conversaciones asociadas a esta cuenta
      try {
        if (!user?.uid) throw new Error('Usuario no autenticado');

        console.log(`🗑️ Iniciando eliminación de conversaciones para la cuenta ${accountToDelete.id}`);

        // Obtener todas las conversaciones del usuario desde Realtime Database
        const conversationsRef = ref(rtdb, `conversations/${user.uid}`);
        const conversationsSnapshot = await get(conversationsRef);

        if (conversationsSnapshot.exists()) {
          const conversationsData = conversationsSnapshot.val();
          const conversationsToDelete: string[] = [];

          // Buscar conversaciones que pertenezcan a esta cuenta
          Object.entries(conversationsData).forEach(([conversationId, conversationData]: [string, any]) => {
            if (conversationData && typeof conversationData === 'object' && conversationData.accountId === accountToDelete.id) {
              conversationsToDelete.push(conversationId);
              console.log(`📝 Conversación marcada para eliminación: ${conversationId} (${conversationData.participantName || 'Sin nombre'})`);
            }
          });

          console.log(`🔢 Total de conversaciones a eliminar: ${conversationsToDelete.length}`);

          if (conversationsToDelete.length > 0) {
            // Eliminar cada conversación y sus mensajes asociados
            for (const conversationId of conversationsToDelete) {
              try {
                // Eliminar la conversación
                const conversationRef = ref(rtdb, `conversations/${user.uid}/${conversationId}`);
                await remove(conversationRef);
                console.log(`✅ Conversación eliminada: ${conversationId}`);

                // Eliminar los mensajes asociados a esta conversación
                const messagesRef = ref(rtdb, `messages/${conversationId}`);
                const messagesSnapshot = await get(messagesRef);
                if (messagesSnapshot.exists()) {
                  await remove(messagesRef);
                  console.log(`✅ Mensajes eliminados para conversación: ${conversationId}`);
                }
              } catch (deleteError) {
                console.error(`❌ Error eliminando conversación ${conversationId}:`, deleteError);
              }
            }

            console.log(`✅ Eliminación completada. ${conversationsToDelete.length} conversaciones eliminadas.`);
            toast.success(`${conversationsToDelete.length} conversación${conversationsToDelete.length > 1 ? 'es' : ''} eliminada${conversationsToDelete.length > 1 ? 's' : ''} correctamente`);
          } else {
            console.log('ℹ️ No se encontraron conversaciones asociadas a esta cuenta');
          }
        } else {
          console.log('ℹ️ No se encontraron conversaciones para este usuario');
        }
      } catch (conversationsError) {
        console.error('❌ Error eliminando conversaciones:', conversationsError);
        toast.error('Error al eliminar las conversaciones asociadas');
      }

      // Cerrar el modal
      setShowDeleteModal(false);
      setAccountToDelete(null);

      // El listener de Firestore actualizará automáticamente la interfaz
    } catch (error) {
      console.error('Error al eliminar la cuenta:', error);
      toast.error(error instanceof Error ? error.message : 'Error al eliminar la cuenta');
    } finally {
      setIsDeleting(false);
    }
  };

  // Conectar cuentas a través de Unipile
  const handleConnectAccounts = async () => {
    if (!user?.uid) return

    setIsConnecting(true)

    try {
      // Calcular la fecha de expiración (24 horas después de la hora actual)
      const expiresOn = new Date()
      expiresOn.setHours(expiresOn.getHours() + 24)
      const expiresOnISO = expiresOn.toISOString()

      // Obtener la URL de la API y la clave API desde las variables de entorno
      const apiUrl = process.env.NEXT_PUBLIC_UNIPILE_API_URL || 'https://api10.unipile.com:14039';
      const apiKey = process.env.NEXT_PUBLIC_UNIPILE_API_KEY;

      if (!apiKey) {
        throw new Error('La clave API de Unipile no está configurada en las variables de entorno');
      }

      // Filtrar solo las plataformas que no están conectadas actualmente
      const availablePlatforms = [
        { id: "WHATSAPP", connected: connectedAccounts.whatsapp },
        { id: "INSTAGRAM", connected: connectedAccounts.instagram },
        { id: "MESSENGER", connected: connectedAccounts.messenger },
        { id: "TELEGRAM", connected: connectedAccounts.telegram }
      ];

      const platformsToConnect = availablePlatforms
        .filter(platform => !platform.connected)
        .map(platform => platform.id);

      // Verificar si hay plataformas para conectar
      if (platformsToConnect.length === 0) {
        toast.success('Todas las plataformas ya están conectadas');
        setIsConnecting(false);
        return;
      }

      console.log('Plataformas a conectar:', platformsToConnect);

      // Preparar los datos para la solicitud a Unipile
      const requestData = {
        type: "create",
        providers: platformsToConnect,
        expiresOn: expiresOnISO,
        api_url: apiUrl,
        name: user.uid, // ID del usuario activo (tatuador)
        notify_url: "https://app.tatu.ink/api/webhooks/unipile", // URL para notificaciones de eventos de cuenta
        webhook_url: "https://app.tatu.ink/api/webhooks/unipile", // URL única para todos los usuarios (mantener por compatibilidad)
        success_redirect_url: "https://app.tatu.ink/studio",
        failure_redirect_url: "https://app.tatu.ink/studio"
      }

      // Detectar si es un dispositivo móvil para pre-autorizar la ventana emergente
      const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent)
      let popupWindow = null;
      
      // En dispositivos de escritorio, abrir la ventana emergente antes de la operación asíncrona
      if (!isMobile) {
        popupWindow = window.open('', 'unipile', 'width=600,height=700');
        if (popupWindow) {
          popupWindow.document.write('<html><body><h3>Conectando con Unipile...</h3><p>Por favor espera mientras procesamos tu solicitud.</p><div style="text-align: center; margin-top: 20px;"><div style="display: inline-block; border: 4px solid rgba(0, 0, 0, 0.1); border-left-color: #000; border-radius: 50%; width: 30px; height: 30px; animation: spin 1s linear infinite;"></div></div><style>@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }</style></body></html>');
        }
      }

      // Realizar la solicitud a la API de Unipile
      const response = await fetch(`${apiUrl}/api/v1/hosted/accounts/link`, {
        method: 'POST',
        headers: {
          'X-API-KEY': apiKey,
          'accept': 'application/json',
          'content-type': 'application/json'
        },
        body: JSON.stringify(requestData)
      })

      if (!response.ok) {
        // Cerrar la ventana emergente si hubo un error
        if (popupWindow) popupWindow.close();
        throw new Error(`Error al conectar con Unipile: ${response.statusText}`)
      }

      const responseData = await response.json()

      // Actualizar el documento del usuario en Firestore
      const userRef = doc(db, 'users', user.uid)
      const userDoc = await getDoc(userRef)

      if (userDoc.exists()) {
        await updateDoc(userRef, {
          unipileConfigured: true,
          unipileLinkUrl: responseData.url,
          updatedAt: new Date()
        })
      } else {
        await setDoc(userRef, {
          unipileConfigured: true,
          unipileLinkUrl: responseData.url,
          createdAt: new Date(),
          updatedAt: new Date()
        })
      }

      setIsConfigured(true)

      // Abrir la URL en una ventana popup o redirigir en móvil
      if (responseData.url) {
        if (isMobile) {
          // En móviles, redirigir directamente
          window.location.href = responseData.url;
        } else if (popupWindow) {
          // En desktop, actualizar la ventana emergente ya abierta
          popupWindow.location.href = responseData.url;
        } else {
          // Fallback si la ventana emergente fue bloqueada
          openPopup(responseData.url);
        }
        toast.success('Conectando con Unipile...', { duration: 5000 })
      } else {
        // Cerrar la ventana emergente si no hay URL
        if (popupWindow) popupWindow.close();
        throw new Error('No se recibió una URL válida de Unipile')
      }
    } catch (error) {
      console.error('Error connecting to Unipile:', error)
      toast.error('Error al conectar con Unipile: ' + (error instanceof Error ? error.message : 'Error desconocido'))
    } finally {
      setIsConnecting(false)
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      {/* Cabecera */}
      <div className="bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4 flex justify-between items-center">
        <div>
          <h3 className="text-xl font-bold text-white">Integración con Unipile</h3>
          <p className="text-indigo-100 text-sm mt-1">
            Conecta tus redes sociales para gestionar todas tus conversaciones desde un solo lugar
          </p>
        </div>
        <div className="h-12 w-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        </div>
      </div>

      {/* Contenido */}
      <div className="p-6">
        <div className="mb-6">
          <h4 className="text-lg font-medium text-gray-900 mb-2">Conecta tus redes sociales</h4>
          <p className="text-gray-600">
            Unipile te permite conectar todas tus redes sociales para gestionar tus conversaciones desde Tatu.Ink.
            Conecta WhatsApp, Instagram, Facebook Messenger y Telegram con un solo clic.
          </p>
        </div>

        {/* Plataformas disponibles */}
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-6">
          <PlatformCard
            name="WhatsApp"
            icon="/images/social/whatsapp.svg"
            iconFallback="whatsapp"
            connected={connectedAccounts.whatsapp}
            hasError={accountErrors.whatsapp}
            errorMessage={accountsInfo.whatsapp?.errorMessage}
            onDelete={handleDeleteAccount}
            onReconnect={handleReconnectAccount}
          />
          <PlatformCard
            name="Instagram"
            icon="/images/social/instagram.svg"
            iconFallback="instagram"
            connected={connectedAccounts.instagram}
            hasError={accountErrors.instagram}
            errorMessage={accountsInfo.instagram?.errorMessage}
            onDelete={handleDeleteAccount}
            onReconnect={handleReconnectAccount}
          />
          <PlatformCard
            name="Messenger"
            icon="/images/social/messenger.svg"
            iconFallback="messenger"
            connected={connectedAccounts.messenger}
            hasError={accountErrors.messenger}
            errorMessage={accountsInfo.messenger?.errorMessage}
            onDelete={handleDeleteAccount}
            onReconnect={handleReconnectAccount}
          />
          <PlatformCard
            name="Telegram"
            icon="/images/social/telegram.svg"
            iconFallback="telegram"
            connected={connectedAccounts.telegram}
            hasError={accountErrors.telegram}
            errorMessage={accountsInfo.telegram?.errorMessage}
            onDelete={handleDeleteAccount}
            onReconnect={handleReconnectAccount}
          />
        </div>

        {/* Botón de conexión */}
        <button
          onClick={handleConnectAccounts}
          disabled={isConnecting || isReconnecting}
          className="w-full py-3 px-4 rounded-lg font-medium text-white bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 shadow-md transform transition-transform duration-200 hover:scale-[1.02] disabled:opacity-70 disabled:cursor-not-allowed disabled:hover:scale-100"
        >
          {isConnecting ? (
            <span className="flex items-center justify-center">
              <ArrowPathIcon className="h-5 w-5 mr-2 animate-spin" />
              Conectando...
            </span>
          ) : isReconnecting ? (
            <span className="flex items-center justify-center">
              <ArrowPathIcon className="h-5 w-5 mr-2 animate-spin" />
              Reconectando...
            </span>
          ) : (
            <span className="flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
              </svg>
              Conectar todas mis redes
            </span>
          )}
        </button>

        {/* Información sobre errores si los hay */}
        {(accountErrors.whatsapp || accountErrors.instagram || accountErrors.messenger || accountErrors.telegram) && (
          <div className="mt-6 bg-red-50 border border-red-200 rounded-lg p-4 text-sm">
            <div className="flex items-start">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <div>
                <p className="text-red-800 font-medium mb-2">
                  Algunas de tus cuentas requieren reconexión
                </p>
                <p className="text-red-700">
                  Haz clic en el botón "Reconectar" en las cuentas marcadas con error para restaurar la conexión.
                  Esto puede suceder cuando cambias tu contraseña o por problemas de autenticación.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Información adicional */}
        <div className="mt-6 bg-gray-50 rounded-lg p-4 text-sm text-gray-600">
          <p className="flex items-start">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Al conectar tus redes sociales, podrás recibir y responder mensajes directamente desde Tatu.Ink,
            sin necesidad de cambiar entre aplicaciones. Todas tus conversaciones estarán centralizadas en un solo lugar.
          </p>
        </div>
      </div>

      {/* Modal de confirmación para eliminar cuenta */}
      {showDeleteModal && accountToDelete && (
        <ModalPortal>
          <div
            className="fixed inset-0 bg-black/50 flex items-center justify-center"
            onClick={() => {
              setShowDeleteModal(false);
              setAccountToDelete(null);
            }}
          >
            <div
              className="bg-white rounded-lg p-6 max-w-md w-full mx-4"
              onClick={(e) => e.stopPropagation()}
            >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              ¿Eliminar cuenta de {accountToDelete.platform}?
            </h3>

            <p className="text-gray-600 mb-4">
              Esta acción desconectará tu cuenta de {accountToDelete.platform} de Tatu.Ink.
              Ya no podrás recibir ni responder mensajes de esta plataforma desde la aplicación.
            </p>
            <p className="text-gray-600 mb-4 font-medium">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 inline-block mr-1 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              Importante: {(() => {
                console.log(`🎯 Modal - conversationsToDeleteCount: ${conversationsToDeleteCount}`);
                return conversationsToDeleteCount > 0 ? (
                  <>Se eliminarán {conversationsToDeleteCount} conversación{conversationsToDeleteCount > 1 ? 'es' : ''} y todos sus mensajes asociados.</>
                ) : (
                  <>Todas las conversaciones, chats y mensajes asociados a esta cuenta serán eliminados permanentemente.</>
                );
              })()}
            </p>

            <div className="border-t border-gray-200 pt-4">
              <p className="text-sm text-gray-500 mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 inline-block mr-1 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                Esta acción no puede deshacerse. Tendrás que volver a conectar la cuenta si deseas utilizarla en el futuro.
              </p>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowDeleteModal(false);
                  setAccountToDelete(null);
                }}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                disabled={isDeleting}
              >
                Cancelar
              </button>

              <button
                onClick={confirmDeleteAccount}
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                disabled={isDeleting}
              >
                {isDeleting ? (
                  <span className="flex items-center">
                    <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                    Eliminando...
                  </span>
                ) : (
                  'Eliminar cuenta'
                )}
              </button>
            </div>
            </div>
          </div>
        </ModalPortal>
      )}
    </div>
  )
}
