'use client'

import React, { useState, useEffect } from 'react'
import { useProfile } from '@/app/contexts/ProfileContext'
import { useAuth } from '@/app/contexts/AuthContext'
import { logUserActivity } from '@/app/utils/activity'
import dynamic from 'next/dynamic'
import AddressAutocomplete from './AddressAutocomplete'

// Importar el mapa dinámicamente para evitar problemas de SSR
const Map = dynamic(() => import('./Map'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-[300px] bg-gray-100 rounded-lg animate-pulse flex items-center justify-center">
    </div>
  ),
})

export default function StudioLocationSection() {
  const { profile, updateProfile, loading: profileLoading } = useProfile()
  const { user } = useAuth()
  const [coordinates, setCoordinates] = useState<{ lat: number; lng: number } | null>({
    lat: -33.4569400,
    lng: -70.6482700
  }); // Coordenadas por defecto de Santiago, Chile
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Renderizar un loader mientras se carga el perfil
  if (profileLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <p>Cargando información del estudio...</p>
      </div>
    );
  }

  // Inicializar las coordenadas con los valores guardados si existen
  useEffect(() => {
    if (!profile) return;
    
    try {
      const studioCoordinates = profile?.profile?.contact?.studio?.coordinates;
      if (studioCoordinates && typeof studioCoordinates.lat === 'number' && typeof studioCoordinates.lng === 'number') {
        setCoordinates({
          lat: studioCoordinates.lat,
          lng: studioCoordinates.lng
        });
      }
    } catch (err) {
      console.error('Error al cargar coordenadas:', err);
    }
  }, [profile]);

  const handleAddressSelect = async (address: string, lat: number, lng: number) => {
    // Limpiar errores previos
    setError(null);
    setSaving(true);

    try {
      // Verificar si el usuario está autenticado
      if (!user || !user.uid) {
        setError("Usuario no autenticado. Por favor, inicie sesión.");
        return;
      }

      // Verificar si el perfil existe
      if (!profile) {
        setError("No se pudo cargar el perfil. Intente nuevamente.");
        return;
      }

      // Asegurarnos de tener un objeto profile y contact válidos
      const profileData = profile.profile || {};
      const contactData = profileData.contact || {};
      const studioData = contactData.studio || {};

      // Crear una copia segura del perfil con toda la estructura necesaria
      const updatedProfile = {
        ...profile,
        profile: {
          ...profileData,
          contact: {
            ...contactData,
            studio: {
              ...studioData,
              fullAddress: address,
              coordinates: { lat, lng },
              address: address,
              city: '',
              country: '',
              name: studioData.name || ''
            }
          }
        }
      };

      console.log("Guardando perfil actualizado:", updatedProfile);
      
      // Actualizar Firestore
      await updateProfile(updatedProfile);
      
      // Actualizar coordenadas locales para refrescar el mapa
      setCoordinates({ lat, lng });
      
      console.log("Ubicación actualizada con éxito");
      
      // Registrar actividad del usuario
      if (user && user.uid) {
        await logUserActivity(user.uid, {
          type: 'profile_update',
          description: 'Actualización de la ubicación del estudio',
          metadata: {
            address: address,
            coordinates: { lat, lng },
            city: '',
            country: ''
          }
        });
      }
    } catch (error) {
      console.error("Error al actualizar la ubicación:", error);
      setError("Error al guardar la ubicación. Intente nuevamente.");
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Ubicación del Estudio</h3>
        {error && (
          <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
            {error}
          </div>
        )}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Dirección del Estudio
            </label>
            <AddressAutocomplete
              onSelect={(address: any) => handleAddressSelect(address.display_name, address.lat, address.lon)}
              placeholder="Buscar dirección del estudio..."
              initialValue={profile?.profile?.contact?.studio?.fullAddress || ''}
            />
            {saving && (
              <p className="mt-2 text-sm text-blue-600">Guardando ubicación...</p>
            )}
          </div>

          <div className="mt-4">
            {coordinates ? (
              <Map center={coordinates} zoom={15} />
            ) : (
              <div className="w-full h-[300px] bg-gray-100 rounded-lg flex items-center justify-center">
                <p className="text-gray-500 text-sm">
                  Ingresa una dirección válida para ver el mapa
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
