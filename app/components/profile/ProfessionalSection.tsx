'use client'

import React, { useState } from 'react'
import { useProfile } from '@/app/contexts/ProfileContext'
import ProfessionalInfoSection from './ProfessionalInfoSection'
import { useAuth } from '@/app/contexts/AuthContext'
import { logUserActivity } from '@/app/utils/activity'

const TATTOO_STYLES = [
  'Tradicional',
  'Neotradicional',
  'Realismo',
  'Blackwork',
  'Dotwork',
  'Minimalista',
  'Acuarela',
  'Japonés',
  'Tribal',
  'Geométrico',
  'Old School',
  'New School',
  'Lettering',
  'Ornamental',
  'Surrealista',
  'Ilustrativo',
] as const

const LANGUAGES = [
  'Español',
  'Inglés',
  'Portugués',
  'Francés',
  'Alemán',
  'Italiano',
  'Chino',
  'Japonés',
  'Coreano',
] as const

export default function ProfessionalSection() {
  const { profile, updateProfile } = useProfile()
  const { user } = useAuth()
  const [savingSpecialty, setSavingSpecialty] = useState<string | null>(null)
  const [savingLanguage, setSavingLanguage] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)

  // Asegurarnos de que specialties y languages existan
  const specialties = profile?.profile?.professional?.specialties || []
  const languages = profile?.profile?.professional?.languages || []

  const handleSpecialtyToggle = async (specialty: string) => {
    if (!user?.uid) {
      setError('Usuario no autenticado')
      return
    }
    
    try {
      setSavingSpecialty(specialty)
      setError(null)
      
      // Crear una copia segura de las especialidades actuales
      const current = new Set(specialties)
      
      // Alternar la especialidad
      if (current.has(specialty)) {
        current.delete(specialty)
      } else {
        current.add(specialty)
      }
      
      // Crear una copia segura del perfil
      const updatedProfile = {
        ...profile,
        profile: {
          ...(profile?.profile || {}),
          professional: {
            ...(profile?.profile?.professional || {}),
            specialties: Array.from(current) as string[]
          }
        }
      }
      
      // Guardar en Firestore
      await updateProfile(updatedProfile)
      
      // Registrar la actividad
      await logUserActivity(user.uid, {
        type: 'profile_update',
        description: current.has(specialty) ? 'Añadida especialidad' : 'Eliminada especialidad',
        metadata: { specialty }
      })
    } catch (err) {
      console.error('Error al actualizar especialidad:', err)
      setError('Error al guardar la especialidad. Intente nuevamente.')
    } finally {
      setSavingSpecialty(null)
    }
  }

  const handleLanguageToggle = async (language: string) => {
    if (!user?.uid) {
      setError('Usuario no autenticado')
      return
    }
    
    try {
      setSavingLanguage(language)
      setError(null)
      
      // Crear una copia segura de los idiomas actuales
      const current = new Set(languages)
      
      // Alternar el idioma
      if (current.has(language)) {
        current.delete(language)
      } else {
        current.add(language)
      }
      
      // Crear una copia segura del perfil
      const updatedProfile = {
        ...profile,
        profile: {
          ...(profile?.profile || {}),
          professional: {
            ...(profile?.profile?.professional || {}),
            languages: Array.from(current) as string[]
          }
        }
      }
      
      // Guardar en Firestore
      await updateProfile(updatedProfile)
      
      // Registrar la actividad
      await logUserActivity(user.uid, {
        type: 'profile_update',
        description: current.has(language) ? 'Añadido idioma' : 'Eliminado idioma',
        metadata: { language }
      })
    } catch (err) {
      console.error('Error al actualizar idioma:', err)
      setError('Error al guardar el idioma. Intente nuevamente.')
    } finally {
      setSavingLanguage(null)
    }
  }

  return (
    <div className="w-full space-y-12">
      {/* Certificaciones y Requisitos Legales */}
      <ProfessionalInfoSection />

      {/* Mensaje de error */}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md text-red-700 text-sm">
          {error}
        </div>
      )}

      {/* Specialties */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-6">Especialidades</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {TATTOO_STYLES.map(style => (
            <button
              key={style}
              onClick={() => handleSpecialtyToggle(style)}
              disabled={savingSpecialty !== null}
              className={`px-4 py-2 rounded-lg text-sm font-medium relative ${
                specialties.includes(style)
                  ? 'bg-black text-white'
                  : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
              } ${
                savingSpecialty !== null ? 'cursor-not-allowed opacity-70' : ''
              }`}
            >
              {savingSpecialty === style ? (
                <>
                  <span className="opacity-0">{style}</span>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <svg className="animate-spin h-4 w-4 text-current" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  </div>
                </>
              ) : (
                style
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Languages */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-6">Idiomas</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {LANGUAGES.map(language => (
            <button
              key={language}
              onClick={() => handleLanguageToggle(language)}
              disabled={savingLanguage !== null}
              className={`px-4 py-2 rounded-lg text-sm font-medium relative ${
                languages.includes(language)
                  ? 'bg-black text-white'
                  : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
              } ${
                savingLanguage !== null ? 'cursor-not-allowed opacity-70' : ''
              }`}
            >
              {savingLanguage === language ? (
                <>
                  <span className="opacity-0">{language}</span>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <svg className="animate-spin h-4 w-4 text-current" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  </div>
                </>
              ) : (
                language
              )}
            </button>
          ))}
        </div>
      </div>
    </div>
  )
}
