'use client';

import { useState, useEffect } from 'react';
import { useProfile } from '@/app/contexts/ProfileContext';
import { useAuth } from '@/app/contexts/AuthContext';
import { CheckCircleIcon, XCircleIcon, ExclamationCircleIcon } from '@heroicons/react/24/outline';
import { doc, updateDoc, setDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { logUserActivity } from '@/app/utils/activity';

declare global {
  interface Window {
    pdfjsLib: any;
  }
}

const convertPdfToImage = async (file: File): Promise<string> => {
  return new Promise(async (resolve, reject) => {
    try {
      console.log('Iniciando conversión de PDF a imagen...');
      
      // Esperar a que pdf.js esté disponible
      let attempts = 0;
      while (!window.pdfjsLib && attempts < 50) {
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
      }

      if (!window.pdfjsLib) {
        throw new Error('PDF.js no se pudo cargar después de varios intentos');
      }

      // Convertir el archivo a ArrayBuffer
      const arrayBuffer = await file.arrayBuffer();
      console.log('Archivo convertido a ArrayBuffer');
      
      // Cargar el PDF usando una URL temporal
      const blob = new Blob([arrayBuffer], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);
      console.log('URL temporal creada:', url);

      // Cargar el PDF usando la URL
      const loadingTask = window.pdfjsLib.getDocument(url);
      console.log('Tarea de carga iniciada');

      const pdf = await loadingTask.promise;
      console.log('PDF cargado, número de páginas:', pdf.numPages);
      
      // Obtener la primera página
      const page = await pdf.getPage(1);
      console.log('Primera página obtenida');
      
      // Configurar el canvas con un tamaño máximo
      const viewport = page.getViewport({ scale: 1.0 });
      const maxWidth = 1200;
      const scale = maxWidth / viewport.width;
      const scaledViewport = page.getViewport({ scale });
      
      console.log('Viewport configurado:', {
        originalWidth: viewport.width,
        originalHeight: viewport.height,
        scaledWidth: scaledViewport.width,
        scaledHeight: scaledViewport.height
      });

      const canvas = document.createElement('canvas');
      canvas.width = scaledViewport.width;
      canvas.height = scaledViewport.height;
      
      const context = canvas.getContext('2d');
      if (!context) {
        throw new Error('No se pudo obtener el contexto del canvas');
      }
      
      console.log('Canvas creado y configurado');

      try {
        // Renderizar la página en el canvas
        await page.render({
          canvasContext: context,
          viewport: scaledViewport
        }).promise;
        
        console.log('Página renderizada en el canvas');
        
        // Convertir el canvas a imagen con calidad ajustada
        const imageData = canvas.toDataURL('image/jpeg', 0.7);
        console.log('Canvas convertido a imagen');
        
        // Limpiar
        URL.revokeObjectURL(url);
        
        resolve(imageData);
      } catch (renderError) {
        console.error('Error al renderizar la página:', renderError);
        reject(renderError);
      }
    } catch (error) {
      console.error('Error detallado al convertir PDF a imagen:', error);
      reject(error);
    }
  });
};

interface CertInputs {
  [key: string]: {
    documentNumber: string;
    issueDate: string;
    expiryDate?: string;
  };
}

interface RequiredCertification {
  // Estado de verificación: 'pending' (esperando verificación), 'verified' (verificado), 'rejected' (rechazado)
  status: 'pending' | 'verified' | 'rejected';
  documentNumber: string;
  issueDate: string;
  expiryDate?: string | null;
  submittedAt: string; // Fecha de envío por el usuario
  verifiedAt?: string; // Fecha de verificación por el administrador
  rejectionReason?: string; // Razón de rechazo si status === 'rejected'
  adminNotes?: string; // Notas internas del administrador
}

const getCertificationStatus = (issueDate: string | null | undefined, expiryDate: string | null | undefined, renewalPeriod: number): { status: string; message: string; monthsLeft: number } | null => {
  if (!issueDate) return null;

  const today = new Date();
  const issue = new Date(issueDate);
  let expiry: Date;

  if (expiryDate) {
    expiry = new Date(expiryDate);
  } else {
    expiry = new Date(issue);
    expiry.setFullYear(expiry.getFullYear() + (renewalPeriod || 0));
  }

  const monthsUntilExpiry = (expiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24 * 30);
  const monthsLeft = Math.round(monthsUntilExpiry);

  if (today > expiry) {
    return { 
      status: 'expired', 
      message: 'VENCIDO - Requiere renovación',
      monthsLeft: 0
    };
  } else if (monthsUntilExpiry <= 3) {
    return { 
      status: 'expiring-soon', 
      message: `Por vencer en ${monthsLeft} ${monthsLeft === 1 ? 'mes' : 'meses'}`,
      monthsLeft
    };
  }
  return { 
    status: 'valid', 
    message: `Vigente (${monthsLeft} ${monthsLeft === 1 ? 'mes' : 'meses'} restantes)`,
    monthsLeft
  };
};

const validateCertificationDates = (certId: string, issueDate: string, expiryDate?: string): string | null => {
  const issue = new Date(issueDate);
  const today = new Date();
  
  // Validar que la fecha de emisión no sea futura
  if (issue > today) {
    return 'La fecha de emisión no puede ser futura';
  }

  if (expiryDate) {
    const expiry = new Date(expiryDate);
    
    // Validar que la fecha de vencimiento sea posterior a la emisión
    if (expiry <= issue) {
      return 'La fecha de vencimiento debe ser posterior a la fecha de emisión';
    }

    // Validar el período de renovación según el tipo de certificación
    let validPeriod = true;
    let periodMessage = '';

    if (certId === 'hepatitisB') {
      const fiveYearsFromIssue = new Date(issue);
      fiveYearsFromIssue.setFullYear(fiveYearsFromIssue.getFullYear() + 5);
      validPeriod = Math.abs(expiry.getTime() - fiveYearsFromIssue.getTime()) <= 24 * 60 * 60 * 1000;
      periodMessage = 'La fecha de vencimiento debe ser 5 años después de la fecha de emisión';
    } else if (certId === 'biosafety') {
      const twoYearsFromIssue = new Date(issue);
      twoYearsFromIssue.setFullYear(twoYearsFromIssue.getFullYear() + 2);
      validPeriod = Math.abs(expiry.getTime() - twoYearsFromIssue.getTime()) <= 24 * 60 * 60 * 1000;
      periodMessage = 'La fecha de vencimiento debe ser 2 años después de la fecha de emisión';
    }

    if (!validPeriod) {
      return periodMessage;
    }
  }

  return null;
};

export default function ProfessionalInfoSection() {
  const { profile, updateProfile } = useProfile();
  const { user } = useAuth();
  const [certInputs, setCertInputs] = useState<CertInputs>({});
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({});
  const [loadingCert, setLoadingCert] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const requiredCertsList = [
    {
      id: 'seremiRegistry',
      title: 'Registro SEREMI de Salud',
      description: 'Inscripción en el Libro de Registro de Tatuadores',
      requiresRenewal: false,
      documentType: 'Número de registro o certificado de inscripción'
    },
    {
      id: 'hepatitisB',
      title: 'Vacuna Hepatitis B',
      description: 'Esquema completo de 3 dosis, requiere refuerzo cada 5 años',
      requiresRenewal: true,
      renewalPeriod: 5,
      documentType: 'Carnet de vacunación o certificado médico'
    },
    {
      id: 'biosafety',
      title: 'Certificado de Bioseguridad',
      description: 'Manejo de residuos peligrosos y medidas de bioseguridad',
      requiresRenewal: true,
      renewalPeriod: 2,
      documentType: 'Certificado de curso de bioseguridad'
    }
  ];

  const handleInputChange = (certId: string, field: string, value: string) => {
    setCertInputs(prev => {
      const newInputs = {
        ...prev,
        [certId]: {
          ...prev[certId],
          [field]: value
        }
      };

      // Validar fechas para certificaciones con renovación
      if ((field === 'issueDate' || field === 'expiryDate') && 
          requiredCertsList.find(c => c.id === certId)?.requiresRenewal) {
        const error = validateCertificationDates(
          certId,
          newInputs[certId].issueDate,
          newInputs[certId].expiryDate
        );
        setValidationErrors(prev => ({
          ...prev,
          [certId]: error || ''
        }));
      }

      return newInputs;
    });
  };

  const handleVerification = async (certId: string) => {
    if (!user) return;
    
    const certInput = certInputs[certId];
    
    // Validar campos requeridos
    if (!certInput?.documentNumber || !certInput?.issueDate) {
      alert('Por favor complete todos los campos requeridos');
      return;
    }

    // Validar fechas para certificaciones con renovación
    if (requiredCertsList.find(c => c.id === certId)?.requiresRenewal) {
      const error = validateCertificationDates(
        certId,
        certInput.issueDate,
        certInput.expiryDate
      );
      if (error) {
        setError(error);
        return;
      }
    }

    setLoadingCert(certId);

    try {
      // Preparar los datos de la certificación
      const certData: RequiredCertification = {
        status: 'pending', // Inicialmente en estado pendiente de verificación
        documentNumber: certInput.documentNumber,
        issueDate: certInput.issueDate,
        expiryDate: certInput.expiryDate || null,
        submittedAt: new Date().toISOString()
      };
      
      // Crear una copia segura del perfil actual con la estructura necesaria
      const updatedProfile = {
        ...profile,
        profile: {
          ...(profile?.profile || {}),
          professional: {
            ...(profile?.profile?.professional || {}),
            requiredCertifications: {
              ...(profile?.profile?.professional?.requiredCertifications || {}),
              [certId]: certData
            }
          }
        }
      };

      // Actualizar en Firestore usando la colección 'profiles' en lugar de 'users'
      const profileRef = doc(db, 'profiles', user.uid);
      
      // Usar setDoc con merge:true para asegurar que se creen los campos si no existen
      await setDoc(profileRef, updatedProfile, { merge: true });
      
      // Actualizar el estado local usando el contexto
      await updateProfile(updatedProfile);

      // Registrar la actividad
      await logUserActivity(user.uid, {
        type: 'profile_update',
        description: `Envío de ${requiredCertsList.find(cert => cert.id === certId)?.title} para verificación`,
        metadata: {
          certificationId: certId,
          documentNumber: certInput.documentNumber,
          issueDate: certInput.issueDate,
          expiryDate: certInput.expiryDate,
          status: 'pending'
        }
      });

      // Limpiar los inputs y errores
      setCertInputs(prev => ({
        ...prev,
        [certId]: {
          documentNumber: '',
          issueDate: '',
          expiryDate: ''
        }
      }));
      setValidationErrors(prev => ({
        ...prev,
        [certId]: ''
      }));

    } catch (err) {
      console.error('Error al verificar la certificación:', err);
      setError('Hubo un error al verificar la certificación. Por favor, intente nuevamente.');
    } finally {
      setLoadingCert(null);
    }
  };

  return (
    <div className="space-y-6">
      {/* Mensaje de error */}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md text-red-700 text-sm mb-4">
          {error}
          <button 
            className="ml-2 font-medium underline" 
            onClick={() => setError(null)}
          >
            Cerrar
          </button>
        </div>
      )}
      <div>
        <h3 className="text-lg font-semibold mb-4">Requisitos Legales</h3>
        <div className="grid grid-cols-1 gap-4">
          {requiredCertsList.map((cert) => (
            <div
              key={cert.id}
              className="p-4 bg-white rounded-lg border border-gray-200 hover:border-gray-300 transition-colors"
            >
              <div className="flex items-start justify-between">
                <div className="flex-grow">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium text-gray-900">{cert.title}</h4>
                    {cert.requiresRenewal && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        Renovar cada {cert.renewalPeriod} años
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-500 mt-1">{cert.description}</p>
                  <div className="mt-3 space-y-3">
                    <label className="block text-sm font-medium text-gray-700">
                      {cert.documentType}
                    </label>
                    <div className="flex items-center space-x-3">
                      <input
                        type="text"
                        placeholder={`Ingrese ${cert.documentType.toLowerCase()}`}
                        value={certInputs[cert.id]?.documentNumber || ''}
                        onChange={(e) => handleInputChange(cert.id, 'documentNumber', e.target.value)}
                        className="flex-grow px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black"
                      />
                      <input
                        type="date"
                        value={certInputs[cert.id]?.issueDate || ''}
                        onChange={(e) => handleInputChange(cert.id, 'issueDate', e.target.value)}
                        className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black"
                        placeholder="Fecha de emisión"
                      />
                      {cert.requiresRenewal && (
                        <input
                          type="date"
                          value={certInputs[cert.id]?.expiryDate || ''}
                          onChange={(e) => handleInputChange(cert.id, 'expiryDate', e.target.value)}
                          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-black"
                          placeholder="Fecha de vencimiento"
                        />
                      )}
                    </div>

                    {validationErrors[cert.id] && (
                      <p className="mt-2 text-sm text-red-600">
                        {validationErrors[cert.id]}
                      </p>
                    )}

                    <div className="mt-3">
                      <button
                        type="button"
                        disabled={loadingCert === cert.id || profile?.profile?.professional?.requiredCertifications?.[cert.id]?.status === 'pending'}
                        className={`px-4 py-2 bg-black text-white text-sm rounded-md hover:bg-gray-800 transition-colors relative ${
                          loadingCert === cert.id || profile?.profile?.professional?.requiredCertifications?.[cert.id]?.status === 'pending' 
                            ? 'opacity-75 cursor-not-allowed' 
                            : ''
                        }`}
                        onClick={() => handleVerification(cert.id)}
                      >
                        {loadingCert === cert.id ? (
                          <>
                            <span className="opacity-0">Enviar</span>
                            <div className="absolute inset-0 flex items-center justify-center">
                              <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                            </div>
                          </>
                        ) : profile?.profile?.professional?.requiredCertifications?.[cert.id]?.status === 'pending' ? (
                          'Enviado para verificación'
                        ) : profile?.profile?.professional?.requiredCertifications?.[cert.id]?.status === 'rejected' ? (
                          'Volver a enviar'
                        ) : (
                          'Enviar para verificación'
                        )}
                      </button>
                    </div>

                    {profile?.profile?.professional?.requiredCertifications?.[cert.id] && (
                      <div className="mt-2 flex items-center text-sm">
                        {(() => {
                          const certStatus = profile?.profile?.professional?.requiredCertifications?.[cert.id]?.status;
                          
                          // Mostrar el estado de verificación
                          if (certStatus === 'pending') {
                            return (
                              <div className="flex items-center text-yellow-600">
                                <ExclamationCircleIcon className="w-4 h-4 mr-1" />
                                <span>Esperando verificación</span>
                              </div>
                            );
                          } else if (certStatus === 'rejected') {
                            return (
                              <div className="flex items-center text-red-600">
                                <XCircleIcon className="w-4 h-4 mr-1" />
                                <span>Verificación rechazada</span>
                                {profile?.profile?.professional?.requiredCertifications?.[cert.id]?.rejectionReason && (
                                  <span className="ml-2 text-xs">- {profile.profile.professional.requiredCertifications[cert.id].rejectionReason}</span>
                                )}
                              </div>
                            );
                          } else if (certStatus === 'verified') {
                            // Si está verificado y requiere renovación, mostrar estado de expiración
                            if (cert.requiresRenewal) {
                              const status = getCertificationStatus(
                                profile?.profile?.professional?.requiredCertifications?.[cert.id]?.issueDate,
                                profile?.profile?.professional?.requiredCertifications?.[cert.id]?.expiryDate,
                                cert.renewalPeriod || 0
                              );
                              return status ? (
                                <div className={`flex items-center ${
                                  status?.status === 'expired' ? 'text-red-600' :
                                  status?.status === 'expiring-soon' ? 'text-yellow-600' :
                                  'text-green-600'
                                }`}>
                                  {status?.status === 'expired' ? (
                                    <XCircleIcon className="w-4 h-4 mr-1" />
                                  ) : status?.status === 'expiring-soon' ? (
                                    <ExclamationCircleIcon className="w-4 h-4 mr-1" />
                                  ) : (
                                    <CheckCircleIcon className="w-4 h-4 mr-1" />
                                  )}
                                  <span>Verificado - {status?.message}</span>
                                </div>
                              ) : null;
                            } else {
                              return (
                                <div className="flex items-center text-green-600">
                                  <CheckCircleIcon className="w-4 h-4 mr-1" />
                                  <span>Verificado</span>
                                </div>
                              );
                            }
                          }
                          
                          return null;
                        })()}
                        <span className="ml-2 text-gray-500">
                          • Documento: {profile?.profile?.professional?.requiredCertifications?.[cert.id]?.documentNumber || ''}
                          • Emitido: {new Date(profile?.profile?.professional?.requiredCertifications?.[cert.id]?.issueDate || '').toLocaleDateString()}
                          {profile?.profile?.professional?.requiredCertifications?.[cert.id]?.expiryDate && (
                            <> • Vence: {new Date(profile?.profile?.professional?.requiredCertifications?.[cert.id]?.expiryDate || '').toLocaleDateString()}</>
                          )}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
