'use client';

import { useState, useEffect } from 'react';
import { 
  XMarkIcon, 
  ChevronLeftIcon, 
  ChevronRightIcon,
} from '@heroicons/react/24/outline';
import { useAuth } from '@/app/contexts/AuthContext';
import { useSocialIntegrations } from '@/app/contexts/SocialIntegrationsContext';
import { useAppointments } from '@/app/components/appointments/AppointmentsContext';
import { ref, onValue } from 'firebase/database';
import { database } from '@/app/firebase/config';
import { db } from '@/lib/firebase/config';
import { doc, getDoc } from 'firebase/firestore';
import { useCustomization } from '@/app/components/customize/CustomizationContext';

interface CalendarPreviewProps {
  studioId?: string;
}

// Tipos para los días no laborables
interface DayOff {
  dayOfWeek: number; // 0 = domingo, 1 = lunes, ..., 6 = sábado
  isFullDay: boolean;
  startTime?: string;
  endTime?: string;
}

// Tipos para las vacaciones
interface Vacation {
  id: string;
  startDate: string; // formato "YYYY-MM-DD"
  endDate: string; // formato "YYYY-MM-DD"
  description: string;
}

export default function CalendarPreview({ studioId }: CalendarPreviewProps) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDay, setSelectedDay] = useState<number | null>(null);
  const [isBookingModalOpen, setIsBookingModalOpen] = useState(false);
  const [isMessageModalOpen, setIsMessageModalOpen] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState<'instagram' | 'facebook' | 'tiktok' | null>(null);
  const [isCopied, setIsCopied] = useState(false);
  const [bookedDays, setBookedDays] = useState<{ [key: string]: boolean }>({});
  const [realtimeAppointments, setRealtimeAppointments] = useState<any[]>([]);
  const [daysOff, setDaysOff] = useState<DayOff[]>([]);
  const [vacations, setVacations] = useState<Vacation[]>([]);
  const { user } = useAuth();
  const { appointments } = useAppointments();
  const { integrations } = useSocialIntegrations();
  const { config } = useCustomization();
  
  // El studioId puede venir como prop o del usuario autenticado
  const effectiveStudioId = studioId || user?.uid || null;

  // Función para verificar si una red social está configurada (o conectada)
  const hasSocialMedia = (platform: 'instagram' | 'facebook' | 'tiktok') => {
    // SOLUCIÓN CORREGIDA: Solo verificamos si la red social está realmente conectada
    // a través de las integraciones (para Instagram y Facebook)
    if (platform === 'instagram' || platform === 'facebook') {
      return integrations[platform]?.connected === true;
    }
    
    // Para TikTok, que no tiene integración directa, verificamos si hay un usuario configurado
    if (platform === 'tiktok' && config.brand.tiktok && config.brand.tiktok.trim() !== '') {
      return true;
    }
    
    return false;
  };

  // Función para obtener el ID o nombre de usuario de la red social
  const getSocialMediaId = (platform: 'instagram' | 'facebook' | 'tiktok') => {
    // Si está conectada en las integraciones, usar el userId de las integraciones
    if ((platform === 'instagram' || platform === 'facebook') && 
        integrations[platform]?.connected && integrations[platform]?.userId) {
      return integrations[platform].userId;
    }
    
    // Si no, usar el valor configurado en CustomizationContext
    if (platform === 'instagram') {
      return config.brand.instagram;
    }
    
    if (platform === 'facebook') {
      const fbValue = config.brand.facebook;
      
      // Extraer el nombre de usuario o ID de Facebook si es una URL completa
      if (fbValue.includes('facebook.com/')) {
        // Extraer el nombre de usuario de la URL de Facebook
        const match = fbValue.match(/facebook\.com\/([^/?&]+)/);
        return match ? match[1] : fbValue;
      }
      
      return fbValue;
    }
    
    if (platform === 'tiktok') {
      const tkValue = config.brand.tiktok;
      
      // Extraer el nombre de usuario si es una URL completa
      if (tkValue.includes('tiktok.com/')) {
        const match = tkValue.match(/@([^/?&]+)/);
        return match ? match[1] : tkValue;
      }
      
      // Si empieza con @ quitar el @
      if (tkValue.startsWith('@')) {
        return tkValue.substring(1);
      }
      
      return tkValue;
    }
    
    return user?.displayName || '';
  };

  // Cargar días libres y vacaciones
  useEffect(() => {
    if (!effectiveStudioId) return;
    
    const fetchTimeOffData = async () => {
      try {
        const timeOffRef = doc(db, 'timeOff', effectiveStudioId);
        const timeOffDoc = await getDoc(timeOffRef);
        
        if (timeOffDoc.exists()) {
          const data = timeOffDoc.data();
          setDaysOff(data.daysOff || []);
          setVacations(data.vacations || []);
        }
      } catch (error) {
        console.error('Error al cargar datos de días libres:', error);
      }
    };
    
    fetchTimeOffData();
  }, [effectiveStudioId]);

  // Obtener citas desde realtime
  useEffect(() => {
    if (!effectiveStudioId) return;
    
    const appointmentsRef = ref(database, 'appointments');
    
    const unsubscribe = onValue(appointmentsRef, (snapshot) => {
      const appointmentsData: any[] = [];
      
      snapshot.forEach((childSnapshot) => {
        const data = childSnapshot.val();
        // Verificar si el artistId coincide
        if (data.artistId === effectiveStudioId) {
          appointmentsData.push({
            id: childSnapshot.key || '',
            ...data
          });
        }
      });
      
      console.log('Realtime appointments found:', appointmentsData);
      setRealtimeAppointments(appointmentsData);
    }, (error) => {
      console.error('Error fetching realtime appointments:', error);
    });
    
    return () => unsubscribe();
  }, [effectiveStudioId]);

  // Combinar citas del contexto y de realtime
  useEffect(() => {
    const allAppointments = [...(appointments || []), ...realtimeAppointments];
    
    // Crear un mapa de días ocupados
    const bookedDaysMap: { [key: string]: boolean } = {};
    allAppointments.forEach(appointment => {
      try {
        // Asegurarse de que la fecha sea válida
        let appointmentDate: Date;
        
        if (appointment.date instanceof Date) {
          appointmentDate = appointment.date;
        } else if (typeof appointment.date === 'string') {
          // Crear la fecha y ajustarla a la zona horaria local
          const parsedDate = new Date(appointment.date);
          
          // Ajustar la fecha para que use la fecha local, no UTC
          const localDate = new Date(
            parsedDate.getFullYear(),
            parsedDate.getMonth(),
            parsedDate.getDate(),
            12, 0, 0 // Mediodía para evitar problemas con horario de verano
          );
          
          appointmentDate = localDate;
        } else {
          console.warn('Formato de fecha no válido:', appointment.date);
          return; // Saltar esta cita
        }
        
        // Verificar que la fecha sea válida
        if (isNaN(appointmentDate.getTime())) {
          console.warn('Fecha inválida:', appointment.date);
          return; // Saltar esta cita
        }
        
        // Usar formato YYYY-MM-DD pero basado en la fecha local
        const dateStr = `${appointmentDate.getFullYear()}-${String(appointmentDate.getMonth() + 1).padStart(2, '0')}-${String(appointmentDate.getDate()).padStart(2, '0')}`;
        bookedDaysMap[dateStr] = true;
      } catch (error) {
        console.error('Error al procesar fecha de cita:', error, appointment);
      }
    });

    setBookedDays(bookedDaysMap);
  }, [appointments, realtimeAppointments]);

  const handleDayClick = async (day: number) => {
    const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
    // Usar formato YYYY-MM-DD basado en la fecha local
    const dateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    const formattedDate = date.toLocaleDateString('es', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    
    setSelectedDay(day);
    setIsBookingModalOpen(true);
  };

  const getMessageText = (day: number) => {
    const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
    const formattedDate = date.toLocaleDateString('es', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    
    return encodeURIComponent(`¡Hola! Vi en Tatu.ink que tienes disponible el ${formattedDate}. Me gustaría agendar una cita para ese día. ¿Podríamos conversar sobre el diseño y el presupuesto?`);
  };

  // Obtener el mensaje sin codificar para el modal
  const getPlainMessageText = (day: number) => {
    const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
    const formattedDate = date.toLocaleDateString('es', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    
    return `¡Hola! Vi en Tatu.ink que tienes disponible el ${formattedDate}. Me gustaría agendar una cita para ese día. ¿Podríamos conversar sobre el diseño y el presupuesto?`;
  };

  // Manejar el clic en un botón de red social
  const handleSocialClick = (platform: 'instagram' | 'facebook' | 'tiktok', e: React.MouseEvent) => {
    e.preventDefault();
    setSelectedPlatform(platform);
    setIsMessageModalOpen(true);
    setIsCopied(false); // Resetear el estado de copiado
  };

  // Función para copiar al portapapeles
  const handleCopyMessage = () => {
    navigator.clipboard.writeText(getPlainMessageText(selectedDay || 0));
    setIsCopied(true);
    
    // Limpiar el mensaje después de 2 segundos
    setTimeout(() => {
      setIsCopied(false);
    }, 2000);
  };

  const handlePrevMonth = () => {
    const newDate = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);
    setCurrentDate(newDate);
  };

  const handleNextMonth = () => {
    const newDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1);
    setCurrentDate(newDate);
  };

  // Verificar si una fecha está dentro de un período de vacaciones
  const isVacationDay = (date: Date): boolean => {
    const dateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    
    return vacations.some(vacation => {
      return dateStr >= vacation.startDate && dateStr <= vacation.endDate;
    });
  };

  // Verificar si es un día no laborable
  const isDayOff = (date: Date): boolean => {
    const dayOfWeek = date.getDay();
    return daysOff.some(day => day.dayOfWeek === dayOfWeek);
  };

  const getDayStatus = (day: number) => {
    // Crear la fecha para el día actual
    const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
    
    // Verificar si es un día pasado
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    if (date < today) {
      return 'past';
    }
    
    // Verificar si es un día no laborable o de vacaciones
    if (isDayOff(date) || isVacationDay(date)) {
      return 'unavailable';
    }
    
    // Verificar si hay citas para este día - ahora usamos 'booked' en lugar de 'unavailable'
    const dateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    if (bookedDays[dateStr]) {
      return 'booked';
    }
    
    return 'available';
  };

  const getStatusStyles = (status: 'available' | 'unavailable' | 'past' | 'booked') => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'unavailable':
        return 'bg-gray-100 text-gray-500 cursor-not-allowed';
      case 'booked':
        return 'bg-red-100 text-red-800 cursor-not-allowed';
      case 'past':
        return 'bg-gray-50 text-gray-300 cursor-not-allowed';
      default:
        return '';
    }
  };

  const weekDays = [
    { id: 'dom', label: 'D' },
    { id: 'lun', label: 'L' },
    { id: 'mar', label: 'M' },
    { id: 'mie', label: 'X' },
    { id: 'jue', label: 'J' },
    { id: 'vie', label: 'V' },
    { id: 'sab', label: 'S' }
  ];

  const daysInMonth = new Date(
    currentDate.getFullYear(),
    currentDate.getMonth() + 1,
    0
  ).getDate();

  const firstDayOfMonth = new Date(
    currentDate.getFullYear(),
    currentDate.getMonth(),
    1
  ).getDay();

  // Añadir un indicador visual para los días con citas (booked)
  const getDayBookedIndicator = (day: number) => {
    const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
    const dateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    
    return bookedDays[dateStr];
  };
  
  // Modificar el renderizado para mostrar un mensaje específico para días no laborables
  const getDayTooltip = (day: number): string => {
    const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
    
    if (isDayOff(date)) {
      return 'Día no laborable';
    }
    
    if (isVacationDay(date)) {
      // Buscar la descripción de las vacaciones si existe
      const dateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
      const vacation = vacations.find(v => dateStr >= v.startDate && dateStr <= v.endDate);
      
      if (vacation?.description) {
        return `No disponible: ${vacation.description}`;
      }
      
      return 'No disponible: Vacaciones';
    }
    
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    if (date < today) {
      return 'Fecha pasada';
    }
    
    const dateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    if (bookedDays[dateStr]) {
      return 'No disponible: Agenda completa';
    }
    
    return 'Disponible';
  };

  return (
    <div className="p-2">
      <div className="flex flex-col mb-3">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Calendario</h3>
        <div className="flex items-center justify-between">
          <button
            onClick={handlePrevMonth}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <ChevronLeftIcon className="w-5 h-5" />
          </button>
          <span className="text-sm font-medium">
            {currentDate.toLocaleString('es', { month: 'short' }).toUpperCase()} - {currentDate.getFullYear()}
          </span>
          <button
            onClick={handleNextMonth}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <ChevronRightIcon className="w-5 h-5" />
          </button>
        </div>
      </div>
      
      <div className="grid grid-cols-7 gap-1 mb-2">
        {weekDays.map(day => (
          <div key={day.id} className="text-center text-xs font-medium text-gray-600 py-1">
            {day.label}
          </div>
        ))}
      </div>
      
      <div className="grid grid-cols-7 gap-2">
        {[...Array(firstDayOfMonth)].map((_, i) => (
          <div key={`empty-${i}`} className="h-9 bg-gray-50 rounded" />
        ))}
        
        {[...Array(daysInMonth)].map((_, i) => {
          const day = i + 1;
          const status = getDayStatus(day);
          const isToday = day === new Date().getDate() && 
                         currentDate.getMonth() === new Date().getMonth() &&
                         currentDate.getFullYear() === new Date().getFullYear();
          const styles = getStatusStyles(status);
          const tooltip = getDayTooltip(day);
          
          return (
            <button 
              key={`day-${i}`}
              type="button"
              onClick={(e) => {
                e.preventDefault();
                if (status !== 'past') {
                  handleDayClick(day);
                }
              }}
              disabled={status === 'past'}
              className={`h-9 rounded-lg flex items-center justify-center text-sm transition-colors cursor-pointer relative
                ${isToday ? 'ring-2 ring-black' : ''}
                ${styles}`}
              title={tooltip}
            >
              <span className={`text-sm ${selectedDay === day ? 'font-bold' : ''}`}>
                {day}
              </span>
              
              {/* Indicador para días no laborables o vacaciones */}
              {(isDayOff(new Date(currentDate.getFullYear(), currentDate.getMonth(), day)) || 
                isVacationDay(new Date(currentDate.getFullYear(), currentDate.getMonth(), day))) && (
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-gray-500 rounded-full"></div>
              )}
              
              {/* Indicador para días con sesiones (ocupados) */}
              {status === 'booked' && (
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-red-500 rounded-full"></div>
              )}
            </button>
          );
        })}
      </div>

      {/* Modal de Estado del Día */}
      {isBookingModalOpen && (
        <div className="absolute inset-0 bg-black/30 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-white p-6 rounded-2xl shadow-xl max-w-[280px] w-full mx-4">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">
                {selectedDay} de {currentDate.toLocaleString('es', { month: 'long' })}
              </h2>
              <button 
                onClick={() => setIsBookingModalOpen(false)}
                className="text-gray-400 hover:text-gray-500 transition-colors"
              >
                <XMarkIcon className="w-5 h-5" />
              </button>
            </div>

            <div className="py-4">
              {getDayStatus(selectedDay || 0) === 'available' ? (
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-green-100 mb-4">
                    <div className="w-6 h-6 text-green-600">✓</div>
                  </div>
                  <p className="text-lg font-medium text-green-600 mb-2">Día Disponible</p>
                  <p className="text-sm text-gray-600 mb-6">
                    Este día está disponible para agendar citas.
                  </p>
                  
                  <div className="space-y-3">
                    {hasSocialMedia('instagram') && (
                      <a
                        href={`https://ig.me/m/${getSocialMediaId('instagram')}?text=${getMessageText(selectedDay || 0)}`}
                        onClick={(e) => handleSocialClick('instagram', e)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center justify-center w-full px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-purple-500 via-pink-500 to-orange-500 rounded-xl hover:opacity-90 transition-opacity"
                      >
                        <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M12 0C5.373 0 0 4.974 0 11.111c0 3.498 1.744 6.614 4.469 8.654V24l4.088-2.242c1.092.301 2.246.464 3.443.464 6.627 0 12-4.975 12-11.111S18.627 0 12 0zm1.191 14.963l-3.055-3.26-5.963 3.26L10.732 8l3.131 3.259L19.752 8l-6.561 6.963z"/>
                        </svg>
                        Agendar por Instagram
                      </a>
                    )}
                    
                    {hasSocialMedia('facebook') && (
                      <a
                        href={`https://m.me/${getSocialMediaId('facebook')}?ref=tatu_booking&text=${getMessageText(selectedDay || 0)}`}
                        onClick={(e) => handleSocialClick('facebook', e)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center justify-center w-full px-4 py-2.5 text-sm font-medium text-white bg-[#0084ff] rounded-xl hover:opacity-90 transition-opacity"
                      >
                        <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M12 0C5.373 0 0 4.974 0 11.111c0 3.498 1.744 6.614 4.469 8.654V24l4.088-2.242c1.092.301 2.246.464 3.443.464 6.627 0 12-4.975 12-11.111S18.627 0 12 0zm1.191 14.963l-3.055-3.26-5.963 3.26L10.732 8l3.131 3.259L19.752 8l-6.561 6.963z"/>
                        </svg>
                        Agendar por Messenger
                      </a>
                    )}
                    
                    {hasSocialMedia('tiktok') && (
                      <a
                        href={`https://www.tiktok.com/@${getSocialMediaId('tiktok')}`}
                        onClick={(e) => handleSocialClick('tiktok', e)}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center justify-center w-full px-4 py-2.5 text-sm font-medium text-white bg-[#010101] rounded-xl hover:opacity-90 transition-opacity"
                      >
                        <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z"/>
                        </svg>
                        Ver perfil en TikTok
                      </a>
                    )}
                  </div>
                </div>
              ) : getDayStatus(selectedDay || 0) === 'booked' ? (
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-red-100 mb-4">
                    <div className="w-6 h-6 text-red-600">×</div>
                  </div>
                  <p className="text-lg font-medium text-red-600 mb-2">Día Ocupado</p>
                  <p className="text-sm text-gray-600">
                    Este día ya está ocupado con otras sesiones.
                  </p>
                </div>
              ) : (
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-4">
                    <div className="w-6 h-6 text-gray-600">×</div>
                  </div>
                  <p className="text-lg font-medium text-gray-600 mb-2">Día No Laborable</p>
                  <p className="text-sm text-gray-600">
                    Este día no está disponible para agendar citas.
                  </p>
                </div>
              )}
            </div>

            <div className="mt-6">
              <button
                onClick={() => setIsBookingModalOpen(false)}
                className="w-full px-4 py-2.5 text-sm font-medium text-gray-700 bg-gray-100 rounded-xl hover:bg-gray-200 transition-colors"
              >
                Cerrar
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal para Copiar Mensaje */}
      {isMessageModalOpen && (
        <div className="absolute inset-0 bg-black/50 backdrop-blur-sm z-[60] flex items-center justify-center">
          <div className="bg-white p-6 rounded-2xl shadow-xl max-w-[320px] w-full mx-4">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">
                Copiar mensaje
              </h2>
              <button 
                onClick={() => setIsMessageModalOpen(false)}
                className="text-gray-400 hover:text-gray-500 transition-colors"
              >
                <XMarkIcon className="w-5 h-5" />
              </button>
            </div>

            <div className="py-4">
              <p className="text-sm text-gray-600 mb-3">
                Copia este mensaje para enviarlo por {selectedPlatform === 'instagram' ? 'Instagram' : selectedPlatform === 'facebook' ? 'Messenger' : 'TikTok'}:
              </p>
              
              <div className="relative">
                <textarea
                  className="w-full p-3 border border-gray-300 rounded-lg text-sm text-gray-700 min-h-[100px] focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                  value={getPlainMessageText(selectedDay || 0)}
                  readOnly
                />
                
                <button
                  onClick={handleCopyMessage}
                  className="absolute top-2 right-2 bg-gray-100 text-gray-600 p-1.5 rounded-md hover:bg-gray-200 transition-colors"
                  title="Copiar al portapapeles"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                    <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                  </svg>
                </button>
              </div>
              
              {/* Mensaje de confirmación de copia */}
              {isCopied && (
                <div className="mt-2 text-sm text-green-600 flex items-center justify-center">
                  <svg className="w-4 h-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  ¡Mensaje copiado al portapapeles!
                </div>
              )}
            </div>

            <div className="flex justify-between mt-4 space-x-3">
              <button
                onClick={() => setIsMessageModalOpen(false)}
                className="flex-1 px-4 py-2.5 text-sm font-medium text-gray-700 bg-gray-100 rounded-xl hover:bg-gray-200 transition-colors"
              >
                Cancelar
              </button>
              
              <a
                href={
                  selectedPlatform === 'instagram' 
                    ? `https://www.instagram.com/${getSocialMediaId('instagram')}`
                    : selectedPlatform === 'facebook' 
                      ? `https://m.me/${getSocialMediaId('facebook')}`
                      : `https://www.tiktok.com/@${getSocialMediaId('tiktok')}`
                }
                target="_blank"
                rel="noopener noreferrer"
                className="flex-1 px-4 py-3 text-sm font-medium text-white bg-blue-600 rounded-xl hover:bg-blue-700 transition-colors text-center flex items-center justify-center shadow-md border border-blue-500"
                onClick={() => setIsMessageModalOpen(false)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                Ir a plataforma
              </a>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}