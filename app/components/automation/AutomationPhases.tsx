'use client'

import { useState, Fragment } from 'react'
import { useAutomation } from './AutomationContext'
import { 
  ClockIcon, 
  PencilSquareIcon, 
  ChatBubbleLeftRightIcon, 
  MegaphoneIcon,
  ChatBubbleLeftEllipsisIcon,
  ExclamationCircleIcon,
  PlayIcon,
  XMarkIcon
} from '@heroicons/react/24/outline'
import { EmailIcon, InstagramIcon, FacebookIcon } from '../icons'
import AutomationPopup from './AutomationPopup'
import DeleteConfirmationDialog from './DeleteConfirmationDialog'
import type { Channel, PhaseId, TimingConfig, Automation } from './AutomationContext'
import { PlusIcon, TrashIcon } from '@heroicons/react/24/outline'
import { Dialog, Transition } from '@headlessui/react'
import { collection, getDocs } from 'firebase/firestore'
import { db } from '@/lib/firebase'

interface Template {
  name: string
  description: string
  timing: TimingConfig
  messageTemplate: string
}

interface Phase {
  id: PhaseId
  name: string
  description: string
  icon: any
  iconColor: string
  bgColor: string
  templates: Template[]
}

const phases: Phase[] = [
  {
    id: 'pre',
    name: 'Pre-Tatuaje',
    description: 'Automatizaciones para la preparación y recordatorios antes del tatuaje',
    icon: ClockIcon,
    iconColor: 'text-blue-500 dark:text-blue-400',
    bgColor: 'bg-blue-50 dark:bg-blue-900/20',
    templates: [
      {
        name: 'Recordatorio de Cita',
        description: 'Envía un recordatorio antes de la cita',
        timing: { type: 'before', value: 24, unit: 'hours' },
        messageTemplate: 'Hola {{client_name}}, te recuerdo tu cita para mañana a las {{appointment_time}}. Por favor confirma tu asistencia.',
      },
      {
        name: 'Preparación para la Sesión',
        description: 'Instrucciones de preparación para el día del tatuaje',
        timing: { type: 'before', value: 48, unit: 'hours' },
        messageTemplate: 'Hola {{client_name}}, para tu sesión de tatuaje del {{appointment_date}} a las {{appointment_time}} recuerda: desayunar bien, usar ropa cómoda y no tomar alcohol el día anterior.',
      },
      {
        name: 'Confirmación de Cita',
        description: 'Solicita confirmación de asistencia a la cita',
        timing: { type: 'before', value: 72, unit: 'hours' },
        messageTemplate: 'Hola {{client_name}}, tu cita está programada para el {{appointment_date}} a las {{appointment_time}}. Por favor, confirma si podrás asistir respondiendo a este mensaje.',
      },
      {
        name: 'Recordatorio de Documentos',
        description: 'Recuerda a los clientes traer identificación y documentos necesarios',
        timing: { type: 'before', value: 36, unit: 'hours' },
        messageTemplate: 'Hola {{client_name}}, recuerda traer tu identificación oficial para tu cita del {{appointment_date}}. Si eres menor de edad, también necesitarás el permiso firmado por tus padres o tutores.',
      },
      {
        name: 'Recordatorio de Pago',
        description: 'Información sobre métodos de pago aceptados',
        timing: { type: 'before', value: 48, unit: 'hours' },
        messageTemplate: 'Hola {{client_name}}, te recuerdo que para tu cita del {{appointment_date}} aceptamos efectivo, transferencias y tarjetas de crédito/débito. El depósito inicial no es reembolsable.',
      },
      {
        name: 'Instrucciones de Ubicación',
        description: 'Envía la dirección y consejos para llegar al estudio',
        timing: { type: 'before', value: 24, unit: 'hours' },
        messageTemplate: 'Hola {{client_name}}, para tu cita de mañana, te comparto la ubicación exacta del estudio: [DIRECCIÓN]. Hay estacionamiento disponible en la calle y estamos a 5 minutos caminando desde la estación de metro más cercana.',
      },
      {
        name: 'Recomendaciones Previas',
        description: 'Consejos para preparar la piel antes del tatuaje',
        timing: { type: 'before', value: 96, unit: 'hours' },
        messageTemplate: 'Hola {{client_name}}, para obtener mejores resultados en tu tatuaje, te recomiendo: hidratar bien la piel los días previos, evitar la exposición solar, no consumir alcohol 24h antes y evitar medicamentos anticoagulantes (consulta con tu médico).',
      }
    ]
  },
  {
    id: 'post',
    name: 'Post-Tatuaje',
    description: 'Automatizaciones para el cuidado y seguimiento después del tatuaje',
    icon: ChatBubbleLeftRightIcon,
    iconColor: 'text-green-500 dark:text-green-400',
    bgColor: 'bg-green-50 dark:bg-green-900/20',
    templates: [
      {
        name: 'Instrucciones de Cuidado',
        description: 'Envía instrucciones detalladas de cuidado',
        timing: { type: 'after', value: 2, unit: 'hours' },
        messageTemplate: 'Hola {{client_name}}, aquí están las instrucciones para cuidar tu nuevo tatuaje: 1. Mantén la zona limpia 2. No expongas al sol 3. Aplica la crema recomendada',
      },
      {
        name: 'Seguimiento',
        description: 'Mensaje de seguimiento para ver cómo va la cicatrización',
        timing: { type: 'after', value: 7, unit: 'days' },
        messageTemplate: 'Hola {{client_name}}, ¿cómo va la cicatrización de tu tatuaje? Si tienes alguna duda o inquietud, no dudes en contactarme.',
      },
      {
        name: 'Recordatorio de Hidratación',
        description: 'Recordatorio para aplicar crema hidratante',
        timing: { type: 'after', value: 1, unit: 'days' },
        messageTemplate: 'Hola {{client_name}}, recuerda mantener tu tatuaje hidratado aplicando una capa fina de la crema recomendada 2-3 veces al día. No uses productos con alcohol o fragancias.',
      },
      {
        name: 'Alerta de Exposición Solar',
        description: 'Recordatorio sobre evitar exposición al sol',
        timing: { type: 'after', value: 3, unit: 'days' },
        messageTemplate: 'Hola {{client_name}}, recuerda que es crucial evitar la exposición solar directa en tu tatuaje durante al menos 4 semanas. Si debes salir, cúbrelo con ropa o usa protector solar de alto espectro una vez cicatrizado.',
      },
      {
        name: 'Seguimiento a Largo Plazo',
        description: 'Verificación del estado del tatuaje después de un mes',
        timing: { type: 'after', value: 30, unit: 'days' },
        messageTemplate: 'Hola {{client_name}}, ¡ya ha pasado un mes desde tu tatuaje! ¿Cómo se ve? Me encantaría que me envíes una foto para ver cómo ha quedado. Recuerda seguir usando protector solar para mantener los colores vibrantes.',
      },
      {
        name: 'Recomendaciones para Baño',
        description: 'Instrucciones sobre cómo bañarse con un tatuaje nuevo',
        timing: { type: 'after', value: 1, unit: 'days' },
        messageTemplate: 'Hola {{client_name}}, para tus próximos baños: evita sumergir el tatuaje, usa agua tibia (no caliente), no frotes directamente la zona, usa jabón neutro y sécalo con toques suaves (sin frotar).',
      },
      {
        name: 'Alerta de Signos de Infección',
        description: 'Información sobre posibles signos de infección',
        timing: { type: 'after', value: 4, unit: 'days' },
        messageTemplate: 'Hola {{client_name}}, tu tatuaje debería estar sanando bien. Si notas enrojecimiento excesivo, hinchazón, calor, pus o fiebre, podría ser signo de infección. Contáctame inmediatamente o consulta a un médico si presentas estos síntomas.',
      }
    ]
  },
  {
    id: 'announcements',
    name: 'Anuncios',
    description: 'Mensajes programados para fechas específicas enviados a todos tus clientes',
    icon: MegaphoneIcon,
    iconColor: 'text-amber-500 dark:text-amber-400',
    bgColor: 'bg-amber-50 dark:bg-amber-900/20',
    templates: [
      {
        name: 'Promoción Especial',
        description: 'Anuncia una promoción o descuento por tiempo limitado',
        timing: { type: 'specific', value: 0, unit: 'days', specificDate: '', specificTime: '' },
        messageTemplate: 'Hola {{client_name}}, te anunciamos nuestra promoción especial: 15% de descuento en todos los diseños durante esta semana. ¡Agenda tu cita ahora!',
      },
      {
        name: 'Evento del Estudio',
        description: 'Invita a tus clientes a un evento especial',
        timing: { type: 'specific', value: 0, unit: 'days', specificDate: '', specificTime: '' },
        messageTemplate: 'Hola {{client_name}}, te invitamos a nuestro evento especial el día [FECHA] a las [HORA]. Tendremos artistas invitados, bebidas y la oportunidad de ganar un tatuaje gratis. ¡Te esperamos!',
      },
      {
        name: 'Nuevos Diseños Disponibles',
        description: 'Anuncia nuevos diseños o estilos disponibles',
        timing: { type: 'specific', value: 0, unit: 'days', specificDate: '', specificTime: '' },
        messageTemplate: 'Hola {{client_name}}, acabamos de actualizar nuestro catálogo con nuevos diseños de [ESTILO]. Puedes verlos en nuestra página web o pasar por el estudio. ¡Agenda tu cita para lucir uno de estos increibles diseños!',
      },
      {
        name: 'Artista Invitado',
        description: 'Anuncia la visita de un artista invitado al estudio',
        timing: { type: 'specific', value: 0, unit: 'days', specificDate: '', specificTime: '' },
        messageTemplate: 'Hola {{client_name}}, nos complace anunciar que el reconocido artista [NOMBRE] estará como invitado en nuestro estudio del [FECHA_INICIO] al [FECHA_FIN]. Sus espacios son limitados, ¡reserva tu cita ahora!',
      },
      {
        name: 'Aniversario del Estudio',
        description: 'Celebra el aniversario del estudio con tus clientes',
        timing: { type: 'specific', value: 0, unit: 'days', specificDate: '', specificTime: '' },
        messageTemplate: 'Hola {{client_name}}, estamos celebrando el [NÚMERO] aniversario de nuestro estudio y queremos compartirlo contigo. Tendremos descuentos especiales, regalos y sorpresas durante toda la semana del [FECHA_INICIO] al [FECHA_FIN]. ¡Gracias por ser parte de nuestra historia!',
      },
      {
        name: 'Cambio de Horario o Ubicación',
        description: 'Informa sobre cambios en el horario o ubicación del estudio',
        timing: { type: 'specific', value: 0, unit: 'days', specificDate: '', specificTime: '' },
        messageTemplate: 'Hola {{client_name}}, queremos informarte que a partir del [FECHA], nuestro estudio [CAMBIO_HORARIO/UBICACIÓN]. Todos los turnos ya agendados se mantienen según lo acordado. Si tienes alguna duda, no dudes en contactarnos.',
      },
      {
        name: 'Felicitaciones Festivas',
        description: 'Envía felicitaciones en fechas festivas importantes',
        timing: { type: 'specific', value: 0, unit: 'days', specificDate: '', specificTime: '' },
        messageTemplate: 'Hola {{client_name}}, desde [NOMBRE_ESTUDIO] queremos desearte felices fiestas y un excelente [AÑO_NUEVO/NAVIDAD/ETC]. Como agradecimiento por tu confianza, durante el mes de [MES] tendremos un descuento especial del [PORCENTAJE]% en tu próximo tatuaje.',
      }
    ]
  },
  {
    id: 'custom',
    name: 'Personalizado',
    description: 'Automatizaciones personalizadas basadas en el historial del cliente',
    icon: PencilSquareIcon,
    iconColor: 'text-purple-500 dark:text-purple-400',
    bgColor: 'bg-purple-50 dark:bg-purple-900/20',
    templates: [
      {
        name: 'Cliente Recurrente',
        description: 'Ofrece un descuento a clientes que han tenido múltiples sesiones',
        timing: { type: 'after', value: 30, unit: 'days' },
        messageTemplate: 'Hola {{client_name}}, como cliente recurrente queremos ofrecerte un 10% de descuento en tu próxima sesión. ¡Gracias por tu preferencia!',
      },
      {
        name: 'Recordatorio de Continuación',
        description: 'Recordatorio para continuar un proyecto de varias sesiones',
        timing: { type: 'after', value: 14, unit: 'days' },
        messageTemplate: 'Hola {{client_name}}, ¿te gustaría agendar tu próxima sesión para continuar con tu proyecto? Estoy disponible para seguir trabajando en tu diseño.',
      },
      {
        name: 'Aniversario de Tatuaje',
        description: 'Felicita al cliente por el aniversario de su tatuaje',
        timing: { type: 'after', value: 365, unit: 'days' },
        messageTemplate: 'Hola {{client_name}}, ¡hoy se cumple un año de tu tatuaje! Espero que lo sigas disfrutando tanto como el primer día. Si necesitas un retoque o tienes en mente un nuevo proyecto, estoy a tus órdenes.',
      },
      {
        name: 'Reactivación de Cliente',
        description: 'Mensaje para clientes que no han regresado en mucho tiempo',
        timing: { type: 'after', value: 180, unit: 'days' },
        messageTemplate: 'Hola {{client_name}}, ha pasado tiempo desde tu última visita. Me gustaría mostrarte mis nuevos trabajos y ofrecerte un descuento especial en tu próximo tatuaje. ¿Te gustaría agendar una consulta?',
      },
      {
        name: 'Cumpleaños del Cliente',
        description: 'Felicitación de cumpleaños con oferta especial',
        timing: { type: 'specific', value: 0, unit: 'days', specificDate: '', specificTime: '' },
        messageTemplate: 'Hola {{client_name}}, ¡feliz cumpleaños! Como regalo especial, te ofrecemos un 20% de descuento en cualquier tatuaje durante todo el mes de tu cumpleaños. ¡Esperamos verte pronto!',
      },
      {
        name: 'Solicitud de Reseña',
        description: 'Pide al cliente que deje una reseña de su experiencia',
        timing: { type: 'after', value: 21, unit: 'days' },
        messageTemplate: 'Hola {{client_name}}, espero que estés disfrutando de tu tatuaje. Tu opinión es muy importante para nosotros. ¿Podrías dedicar un minuto a dejar una reseña en Google o Instagram? Aquí está el enlace: [LINK]. ¡Gracias!',
      },
      {
        name: 'Referidos',
        description: 'Incentiva a los clientes a referir amigos',
        timing: { type: 'after', value: 45, unit: 'days' },
        messageTemplate: 'Hola {{client_name}}, si conoces a alguien interesado en hacerse un tatuaje, recórdale que tenemos un programa de referidos. Por cada amigo que traigas y se tatúe, ambos recibirán un 15% de descuento en su próximo trabajo.',
      }
    ]
  }
]

const channelIcons = {
  email: EmailIcon,
  instagram: InstagramIcon,
  facebook: FacebookIcon,
  sms: ChatBubbleLeftEllipsisIcon
}

export default function AutomationPhases() {
  const { automations, toggleAutomation, deleteAutomation, testAutomation } = useAutomation()
  const [selectedPhase, setSelectedPhase] = useState<PhaseId>('pre')
  const [isPopupOpen, setIsPopupOpen] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<(Template & { phaseId: PhaseId }) | undefined>()
  const [automationToEdit, setAutomationToEdit] = useState<Automation | undefined>()
  const [automationToDelete, setAutomationToDelete] = useState<string | undefined>()
  const [creatingAutomation, setCreatingAutomation] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [automationToTest, setAutomationToTest] = useState<Automation | null>(null)
  const [testClients, setTestClients] = useState<{ id: string, name: string }[]>([])
  const [loadingClients, setLoadingClients] = useState(false)

  const currentPhase = phases.find(p => p.id === selectedPhase)
  const Icon = currentPhase?.icon

  const getChannelIcon = (channel: keyof typeof channelIcons) => {
    const Icon = channelIcons[channel]
    return <Icon className="h-5 w-5" />
  }

  const formatTiming = (timing: TimingConfig) => {
    const { type, value, unit } = timing
    
    // Si es una fecha específica
    if (type === 'specific' && timing.specificDate) {
      const date = new Date(timing.specificDate);
      const formattedDate = date.toLocaleDateString('es-ES', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
      const formattedTime = date.toLocaleTimeString('es-ES', {
        hour: '2-digit',
        minute: '2-digit'
      });
      return `${formattedDate} a las ${formattedTime}`;
    }
    
    const unitMap = {
      minutes: 'minutos',
      hours: 'horas',
      days: 'días',
      weeks: 'semanas',
      months: 'meses'
    }
    
    const typeMap = {
      before: 'antes',
      after: 'después',
      during: 'durante',
      specific: 'específicamente'
    }

    return `${value} ${unitMap[unit]} ${typeMap[type]}`
  }

  const handleDeleteAutomation = async (id: string) => {
    try {
      await deleteAutomation(id)
      setAutomationToDelete(undefined)
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message)
      } else {
        setError('Error al eliminar la automatización')
      }
      // El error se mostrará pero mantenemos el diálogo abierto
      setTimeout(() => setError(null), 5000) // Limpiar el error después de 5 segundos
    }
  }

  const handleToggleAutomation = async (id: string) => {
    try {
      await toggleAutomation(id)
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message)
      } else {
        setError('Error al activar/desactivar la automatización')
      }
      setTimeout(() => setError(null), 5000)
    }
  }

  const handleTestAutomation = async (automation: Automation) => {
    try {
      setAutomationToTest(automation)
      setLoadingClients(true)
      
      // Obtener lista de clientes con citas
      const appointmentsRef = collection(db, 'appointments')
      const appointmentsSnap = await getDocs(appointmentsRef)
      const clients = appointmentsSnap.docs.map(doc => {
        const data = doc.data()
        return {
          id: data.clientId,
          name: data.clientName || 'Cliente sin nombre'
        }
      })
      
      // Eliminar duplicados por id
      const uniqueClients = Array.from(new Map(clients.map(item => [item.id, item])).values())
      setTestClients(uniqueClients)
    } catch (err) {
      console.error('Error loading clients:', err)
      setError('Error al cargar la lista de clientes')
      setAutomationToTest(null)
    } finally {
      setLoadingClients(false)
    }
  }

  const handleConfirmTest = async (clientId: string) => {
    if (!automationToTest) return
    
    try {
      const result = await testAutomation(automationToTest, { clientId })
      if (result.success) {
        // Mensaje enviado exitosamente
      } else {
        setError(result.message || 'Error al enviar el mensaje de prueba')
      }
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message)
      }
    } finally {
      setAutomationToTest(null) // Cerrar diálogo
    }
  }

  return (
    <div className="bg-white dark:bg-gray-800 shadow">
      {error && (
        <div className="p-4 bg-red-50 dark:bg-red-900/20 border-l-4 border-red-500">
          <div className="flex">
            <div className="flex-shrink-0">
              <ExclamationCircleIcon className="h-5 w-5 text-red-400" aria-hidden="true" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700 dark:text-red-200">
                {error}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <div className="px-4 sm:px-6 py-4 sm:py-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-3 sm:space-x-5">
              <div className={`
                flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-2xl
                ${currentPhase?.bgColor}
                transition-colors duration-200
              `}>
                {Icon && <Icon 
                  className={`h-5 w-5 sm:h-6 sm:w-6 ${currentPhase?.iconColor}`}
                  aria-hidden="true"
                />}
              </div>
              <div>
                <h1 className="text-xl sm:text-2xl font-semibold text-gray-900 dark:text-white">
                  {currentPhase?.name}
                </h1>
                <p className="mt-0.5 sm:mt-1 text-sm text-gray-500 dark:text-gray-400">
                  {currentPhase?.description}
                </p>
              </div>
            </div>
            <button
              onClick={() => setIsPopupOpen(true)}
              className="inline-flex items-center justify-center w-full sm:w-auto px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-black to-gray-800 dark:from-white dark:to-gray-200 dark:text-black rounded-full shadow-sm hover:from-gray-800 hover:to-gray-700 dark:hover:from-gray-200 dark:hover:to-gray-300 focus:outline-none focus:ring-2 focus:ring-black dark:focus:ring-white focus:ring-offset-2 transition-all duration-200"
            >
              <PlusIcon className="h-5 w-5 mr-1.5" />
              Nueva Automatización
            </button>
          </div>
        </div>

        {/* Tabs de Fases con scroll lateral */}
        <div className="relative">
          <div className="px-4 sm:px-6 overflow-x-auto scrollbar-hide">
            <nav className="flex space-x-6 sm:space-x-8 min-w-max pb-4" aria-label="Tabs">
              {phases.map((phase) => {
                const Icon = phase.icon
                return (
                  <button
                    key={phase.id}
                    onClick={() => setSelectedPhase(phase.id)}
                    className={`
                      group relative pb-2 px-1
                      text-sm font-medium focus:outline-none whitespace-nowrap
                      ${selectedPhase === phase.id
                        ? 'border-b-2 border-black dark:border-white text-black dark:text-white'
                        : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 border-b-2 border-transparent'
                      }
                    `}
                  >
                    <div className={`
                      mx-auto mb-2 flex h-8 w-8 items-center justify-center rounded-xl
                      ${selectedPhase === phase.id ? phase.bgColor : 'bg-gray-100 dark:bg-gray-800'}
                      transition-all duration-200 group-hover:scale-105
                    `}>
                      <Icon 
                        className={`h-5 w-5 ${selectedPhase === phase.id ? phase.iconColor : 'text-gray-400 dark:text-gray-500'}`}
                        aria-hidden="true"
                      />
                    </div>
                    <span>{phase.name}</span>
                  </button>
                )
              })}
            </nav>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4 sm:p-6">
        <div className="space-y-4 sm:space-y-6">
          {/* Automatizaciones Activas */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Automatizaciones Activas
            </h3>
            
            {automations.filter(a => a.phaseId === selectedPhase).length === 0 ? (
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-8 text-center">
                <div className="mx-auto h-12 w-12 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center mb-4">
                  <svg className="h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                  </svg>
                </div>
                <h3 className="text-base font-medium text-gray-900 dark:text-white">No hay automatizaciones activas</h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Crea una nueva automatización o usa una de las plantillas recomendadas.
                </p>
                <div className="mt-6">
                  <button
                    onClick={() => setIsPopupOpen(true)}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black dark:bg-white dark:text-black hover:bg-gray-800 dark:hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:focus:ring-white transition-all duration-200"
                  >
                    <PlusIcon className="h-5 w-5 mr-2" />
                    Crear Automatización
                  </button>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {automations
                  .filter(a => a.phaseId === selectedPhase)
                  .map(automation => (
                    <div
                      key={automation.id}
                      className="group relative flex flex-col rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden"
                      onClick={() => setAutomationToEdit(automation)}
                    >
                      {/* Barra de estado superior */}
                      <div className={`h-1.5 w-full ${automation.isActive ? 'bg-green-500' : 'bg-gray-300 dark:bg-gray-600'}`}></div>
                      
                      <div className="p-5">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <h4 className="text-base font-medium text-gray-900 dark:text-white">
                              {automation.name}
                            </h4>
                          </div>
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleTestAutomation(automation)
                              }}
                              className="p-1.5 rounded-full text-gray-400 hover:text-green-500 hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors duration-200"
                              title="Probar automatización"
                            >
                              <PlayIcon className="h-4 w-4" />
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                handleToggleAutomation(automation.id)
                              }}
                              className={`
                                relative inline-flex h-5 w-9 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent
                                transition-colors duration-200 ease-in-out focus:outline-none
                                ${automation.isActive ? 'bg-green-500' : 'bg-gray-200 dark:bg-gray-700'}
                              `}
                              title={automation.isActive ? 'Desactivar' : 'Activar'}
                            >
                              <span className="sr-only">
                                {automation.isActive ? 'Desactivar' : 'Activar'} automatización
                              </span>
                              <span
                                className={`
                                  pointer-events-none relative inline-block h-4 w-4 transform rounded-full bg-white dark:bg-gray-900
                                  shadow ring-0 transition duration-200 ease-in-out
                                  ${automation.isActive ? 'translate-x-4' : 'translate-x-0'}
                                `}
                              />
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation()
                                setAutomationToDelete(automation.id)
                              }}
                              className="p-1.5 rounded-full text-gray-400 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-200"
                              title="Eliminar automatización"
                            >
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                        
                        <div className="space-y-2 mt-2">
                          {automation.description && (
                            <p className="text-sm text-gray-600 dark:text-gray-300">
                              {automation.description}
                            </p>
                          )}
                          
                          <div className="flex flex-wrap gap-2 mt-3">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                              <ClockIcon className="h-3 w-3 mr-1" />
                              {formatTiming(automation.timing)}
                            </span>
                            
                            {selectedPhase === 'custom' && automation.customConditions && automation.customConditions.length > 0 && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-200">
                                <svg className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
                                </svg>
                                {automation.customConditions.length} {automation.customConditions.length === 1 ? 'condición' : 'condiciones'}
                              </span>
                            )}
                          </div>
                          
                          <div className="mt-4 pt-3 border-t border-gray-100 dark:border-gray-700">
                            <p className="text-xs text-gray-500 dark:text-gray-400 line-clamp-2">
                              <span className="font-medium">Mensaje:</span> {automation.messageTemplate}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </div>

          {/* Automatizaciones Recomendadas */}
          <div className="mt-6 sm:mt-8">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 space-y-2 sm:space-y-0">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Automatizaciones Recomendadas
              </h3>
              <button
                onClick={() => setIsPopupOpen(true)}
                className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-white bg-black dark:bg-white dark:text-black rounded-full shadow-sm hover:bg-gray-800 dark:hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black dark:focus:ring-white transition-all duration-200"
              >
                <PlusIcon className="h-4 w-4 mr-1.5" />
                Crear Personalizada
              </button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 sm:gap-10">
              {currentPhase?.templates.map((template, idx) => (
                <div
                  key={idx}
                  className="relative rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 p-6 transition-all duration-200 hover:shadow-md hover:border-black dark:hover:border-white cursor-pointer overflow-hidden"
                  onClick={async () => {
                    if (!creatingAutomation) {
                      setCreatingAutomation(true)
                      try {
                        setSelectedTemplate({
                          ...template,
                          phaseId: currentPhase.id
                        })
                        setIsPopupOpen(true)
                      } catch (error) {
                        console.error('Error:', error)
                      } finally {
                        setCreatingAutomation(false)
                      }
                    }
                  }}
                >
                  <div className="absolute top-0 right-0 mt-2 mr-2">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200">
                      <svg className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                      Plantilla
                    </span>
                  </div>
                  
                  <h4 className="text-base font-medium text-gray-900 dark:text-white mb-2 pr-20">
                    {template.name}
                  </h4>
                  
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                    {template.description}
                  </p>
                  
                  <div className="flex flex-wrap gap-2 mt-3">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                      <ClockIcon className="h-3 w-3 mr-1" />
                      {formatTiming(template.timing)}
                    </span>
                  </div>
                  
                  <div className="mt-4 pt-3 border-t border-gray-100 dark:border-gray-700">
                    <p className="text-xs text-gray-500 dark:text-gray-400 line-clamp-2">
                      <span className="font-medium">Mensaje:</span> {template.messageTemplate}
                    </p>
                  </div>
                  
                  <div className="absolute bottom-0 right-0 mb-2 mr-2">
                    <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                      <PlusIcon className="h-3 w-3 mr-1" />
                      Click para crear
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Diálogo de selección de cliente para prueba */}
      <Transition appear show={automationToTest !== null} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={() => setAutomationToTest(null)}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 z-10 overflow-y-auto">
            <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                enterTo="opacity-100 translate-y-0 sm:scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              >
                <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg">
                  <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex items-center justify-between">
                      <Dialog.Title as="h3" className="text-xl font-semibold leading-6 text-gray-900 dark:text-gray-100">
                        Seleccionar Cliente para Prueba
                      </Dialog.Title>
                      <button
                        type="button"
                        className="text-gray-400 hover:text-gray-500"
                        onClick={() => setAutomationToTest(null)}
                      >
                        <XMarkIcon className="h-6 w-6" />
                      </button>
                    </div>
                  </div>

                  <div className="px-6 py-4">
                    {loadingClients ? (
                      <div className="text-center py-4">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black dark:border-white mx-auto"></div>
                        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">Cargando clientes...</p>
                      </div>
                    ) : testClients.length === 0 ? (
                      <div className="text-center py-4">
                        <p className="text-sm text-gray-500 dark:text-gray-400">No hay clientes con citas disponibles</p>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {testClients.map(client => (
                          <button
                            key={client.id}
                            onClick={() => handleConfirmTest(client.id)}
                            className="w-full text-left px-4 py-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200"
                          >
                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{client.name}</p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">ID: {client.id}</p>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      {/* Popup de Nueva Automatización */}
      <AutomationPopup
        isOpen={isPopupOpen}
        onClose={() => {
          setIsPopupOpen(false)
          setSelectedTemplate(undefined)
        }}
        template={selectedTemplate}
      />

      {/* Popup de Edición */}
      <AutomationPopup
        isOpen={!!automationToEdit}
        onClose={() => setAutomationToEdit(undefined)}
        automation={automationToEdit}
        phaseId={selectedPhase}
      />

      {automationToDelete && (
        <DeleteConfirmationDialog
          isOpen={!!automationToDelete}
          onClose={() => setAutomationToDelete(undefined)}
          onConfirm={() => handleDeleteAutomation(automationToDelete)}
          title="Eliminar Automatización"
          message="¿Estás seguro de que quieres eliminar esta automatización? Esta acción no se puede deshacer."
        />
      )}
    </div>
  )
} 