'use client'

import { Fragment, useState, useEffect, useRef, useMemo } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { XMarkIcon, InformationCircleIcon } from '@heroicons/react/24/outline'
import { useAutomation } from './AutomationContext'
import { Switch } from '@headlessui/react'
import type { Automation, PhaseId, TriggerEvent, TimingConfig, Template } from './AutomationContext'
import { collection, getDocs } from 'firebase/firestore'
import { db } from '@/lib/firebase'

interface Props {
  isOpen: boolean
  onClose: () => void
  phaseId?: PhaseId
  automation?: Automation
  template?: {
    name: string
    phaseId: PhaseId
    messageTemplate: string
    timing: TimingConfig
  }
}

interface AutomationFormData {
  name: string
  description: string
  phaseId: PhaseId
  timing: TimingConfig
  messageTemplate: string
  isActive: boolean
  channel: string
  variables: string[]
  trigger: TriggerEvent
  customConditions?: Array<{
    type: string
    operator: string
    value: number
  }>
}

const defaultTiming: TimingConfig = {
  type: 'before',
  value: 24,
  unit: 'hours',
  specificDate: '',
  specificTime: ''
}

const AVAILABLE_VARIABLES = [
  { key: 'client_name', description: 'Nombre del cliente' },
  { key: 'appointment_date', description: 'Fecha de la cita (ej: lunes, 13 de febrero)' },
  { key: 'appointment_time', description: 'Hora de la cita (ej: 13:05)' },
  { key: 'service', description: 'Servicio o tipo de tatuaje' },
  { key: 'artist', description: 'Nombre del artista' },
  { key: 'studio', description: 'Nombre del estudio' },
  { key: 'address', description: 'Dirección del estudio' },
  { key: 'price', description: 'Precio del servicio' }
]

const PHASES = [
  {
    id: 'pre' as PhaseId,
    label: 'Pre-Tatuaje',
    icon: (
      <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>
    )
  },
  {
    id: 'post' as PhaseId,
    label: 'Post-Tatuaje',
    icon: (
      <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    )
  },
  {
    id: 'announcements' as PhaseId,
    label: 'Anuncios',
    icon: (
      <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
      </svg>
    )
  },
  {
    id: 'custom' as PhaseId,
    label: 'Personalizado',
    icon: (
      <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
      </svg>
    )
  }
]

const TIMING_TYPES = [
  { value: 'before', label: 'Antes' },
  { value: 'after', label: 'Después' },
  { value: 'specific', label: 'Específico' }
]

const TIME_UNITS = [
  { value: 'minutes', label: 'Minutos' },
  { value: 'hours', label: 'Horas' },
  { value: 'days', label: 'Días' },
  { value: 'weeks', label: 'Semanas' },
  { value: 'months', label: 'Meses' }
]

const CUSTOM_CONDITION_TYPES = [
  { value: 'pending_sessions', label: 'Sesiones pendientes', description: 'Número de sesiones que quedan por completar para el cliente en todos sus diseños multi-sesión' },
  { value: 'total_spent', label: 'Gasto total', description: 'Cantidad total que el cliente ha gastado en el estudio' },
  { value: 'appointment_count', label: 'Cantidad de citas', description: 'Número total de citas que el cliente ha tenido en el estudio' }
]

const CUSTOM_CONDITION_OPERATORS = [
  { value: '>', label: 'Mayor que' },
  { value: '<', label: 'Menor que' },
  { value: '=', label: 'Igual a' },
  { value: '>=', label: 'Mayor o igual a' },
  { value: '<=', label: 'Menor o igual a' }
]

// Función para extraer variables del template de mensaje
const extractVariables = (template: string): string[] => {
  const regex = /{{(.*?)}}/g
  const matches = template.match(regex) || []
  return matches.map(match => match.replace(/{{|}}/g, '').trim())
}

export default function AutomationPopup({ isOpen, onClose, phaseId, automation, template }: Props) {
  const { createAutomation, updateAutomation, testAutomation } = useAutomation()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isTesting, setIsTesting] = useState(false)
  const [testResult, setTestResult] = useState<any>(null)
  const [showTestDialog, setShowTestDialog] = useState(false)
  const [testClients, setTestClients] = useState<Array<{ id: string; name: string; photoURL?: string }>>([])
  const [loadingClients, setLoadingClients] = useState(false)
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})
  const [error, setError] = useState<string | null>(null)

  const initialFormData = useMemo(() => ({
    name: automation?.name || template?.name || '',
    description: automation?.description || '',
    phaseId: phaseId || template?.phaseId || 'pre',
    messageTemplate: automation?.messageTemplate || template?.messageTemplate || '',
    timing: automation?.timing || template?.timing || defaultTiming,
    isActive: automation?.isActive ?? true,
    channel: automation?.channel || 'whatsapp',
    variables: automation?.variables || [],
    trigger: automation?.trigger || 'appointment_reminder',
    customConditions: automation?.customConditions || []
  }), [automation, template, phaseId])

  const [formData, setFormData] = useState(initialFormData)

  useEffect(() => {
    setFormData(initialFormData)
  }, [initialFormData])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validación
    const errors: Record<string, string> = {}

    if (!formData.name) {
      errors.name = 'El nombre es obligatorio'
    }

    if (!formData.messageTemplate) {
      errors.messageTemplate = 'El mensaje es obligatorio'
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors)
      return
    }

    setIsSubmitting(true)

    try {
      // Crear una copia del formData para manipular
      const automationData = {
        ...formData,
        variables: extractVariables(formData.messageTemplate)
      }

      // Si no es fase custom, eliminar customConditions del objeto en lugar de establecerlo como undefined
      if (formData.phaseId !== 'custom') {
        delete automationData.customConditions;
      }

      if (automation?.id) {
        await updateAutomation(automation.id, automationData)
      } else {
        await createAutomation(automationData)
      }

      onClose()
    } catch (error) {
      console.error('Error al guardar la automatización:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const [showVariables, setShowVariables] = useState(false)
  const [showPhases, setShowPhases] = useState(false)
  const phaseRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (phaseRef.current && !phaseRef.current.contains(event.target as Node)) {
        setShowPhases(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const insertVariable = (variable: string) => {
    const textarea = document.getElementById('messageTemplate') as HTMLTextAreaElement
    if (textarea) {
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const text = formData.messageTemplate
      const newText = text.substring(0, start) + `{{${variable}}}` + text.substring(end)
      setFormData(prev => ({ ...prev, messageTemplate: newText }))
      // Restaurar el foco y la posición del cursor
      textarea.focus()
      const newCursorPos = start + variable.length + 4
      textarea.setSelectionRange(newCursorPos, newCursorPos)
    }
  }

  const handleTestAutomation = async () => {
    setIsTesting(true)
    try {
      const result = await testAutomation({
        automation: {
          ...formData,
          variables: extractVariables(formData.messageTemplate)
        }
      })

      // Mostrar el resultado en una alerta
      alert(result.message)
      setTestResult(result)
    } catch (error) {
      console.error('Error al probar la automatización:', error)
      alert('Error al probar la automatización')
    } finally {
      setIsTesting(false)
    }
  }

  const CustomConditionField = () => {
    const addCondition = () => {
      const newCondition: CustomCondition = {
        type: 'pending_sessions',
        operator: '>',
        value: 0
      }
      setFormData(prev => ({
        ...prev,
        customConditions: [
          ...(prev.customConditions || []),
          newCondition
        ]
      }))
    }

    const removeCondition = (index: number) => {
      setFormData(prev => ({
        ...prev,
        customConditions: prev.customConditions?.filter((_, i) => i !== index) || []
      }))
    }

    const updateCondition = (index: number, field: string, value: any) => {
      setFormData(prev => {
        const newConditions = [...(prev.customConditions || [])]
        newConditions[index] = {
          ...newConditions[index],
          [field]: value
        }
        return {
          ...prev,
          customConditions: newConditions
        }
      })
    }

    return (
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div>
            <h3 className="text-lg font-medium">Condiciones personalizadas</h3>
            <p className="text-sm text-gray-500 mt-1">
              Configura condiciones específicas para activar esta automatización.
              <span className="block mt-1 text-indigo-600">
                Nuevo: Ahora puedes usar la condición "Sesiones pendientes" para automatizar mensajes basados en el progreso de diseños.
              </span>
            </p>
          </div>
          <button
            type="button"
            onClick={addCondition}
            className="inline-flex items-center justify-center px-4 py-2.5 sm:py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 self-start sm:self-center transition-colors duration-200"
          >
            <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Añadir condición
          </button>
        </div>

        {formData.customConditions && formData.customConditions.length > 0 ? (
          <div className="space-y-4">
            {formData.customConditions.map((condition, index) => (
              <div key={index} className="flex flex-col p-4 border rounded-md bg-gray-50 dark:bg-gray-700/30 dark:border-gray-600">
                {/* Encabezado de la condición con número y botón de eliminar */}
                <div className="flex justify-between items-center mb-3">
                  <div className="flex items-center">
                    <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-300 text-sm font-medium mr-2">
                      {index + 1}
                    </span>
                    <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Condición {index + 1}</h4>
                  </div>
                  <button
                    type="button"
                    onClick={() => removeCondition(index)}
                    className="inline-flex items-center justify-center p-2 border border-gray-200 dark:border-gray-600 rounded-full shadow-sm text-red-600 dark:text-red-400 bg-white dark:bg-gray-700 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-700 dark:hover:text-red-300 hover:border-red-200 dark:hover:border-red-800/50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-200"
                    aria-label="Eliminar condición"
                  >
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                {/* Contenido de la condición en formato responsivo */}
                <div className="grid grid-cols-1 sm:grid-cols-12 gap-3">
                  {/* Tipo de condición - ocupa más espacio */}
                  <div className="sm:col-span-6">
                    <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
                      Tipo
                    </label>
                    <select
                      value={condition.type}
                      onChange={(e) => updateCondition(index, 'type', e.target.value)}
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                    >
                      {CUSTOM_CONDITION_TYPES.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>

                  {/* Operador - ocupa menos espacio */}
                  <div className="sm:col-span-3">
                    <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
                      Operador
                    </label>
                    <select
                      value={condition.operator}
                      onChange={(e) => updateCondition(index, 'operator', e.target.value)}
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                    >
                      {CUSTOM_CONDITION_OPERATORS.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>

                  {/* Valor - ocupa menos espacio */}
                  <div className="sm:col-span-3">
                    <label className="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">
                      Valor
                    </label>
                    <input
                      type="number"
                      value={condition.value}
                      onChange={(e) => updateCondition(index, 'value', parseInt(e.target.value) || 0)}
                      className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                      min="0"
                    />
                  </div>
                </div>

                {/* Descripción de la condición - siempre visible */}
                <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/10 rounded-md text-xs text-blue-700 dark:text-blue-300 border border-blue-100 dark:border-blue-800/30">
                  <div className="flex items-start">
                    <InformationCircleIcon className="h-4 w-4 text-blue-500 dark:text-blue-400 mt-0.5 mr-2 flex-shrink-0" />
                    <p>{CUSTOM_CONDITION_TYPES.find(t => t.value === condition.type)?.description || 'Descripción no disponible'}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-6 px-4 border border-dashed border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-800/30">
            <svg className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              No hay condiciones personalizadas. Haz clic en "Añadir condición" para crear una.
            </p>
          </div>
        )}
      </div>
    )
  }

  return (
    <>
      <Transition.Root show={isOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={onClose}>
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />

          <div className="fixed inset-0 z-10 overflow-y-auto">
            <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                enterTo="opacity-100 translate-y-0 sm:scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              >
                <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl">
                  <form onSubmit={handleSubmit}>
                    <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                      <div className="flex items-center justify-between">
                        <Dialog.Title as="h3" className="text-xl font-semibold leading-6 text-gray-900 dark:text-gray-100">
                          {automation ? 'Editar' : 'Crear'} Automatización
                        </Dialog.Title>
                        <button
                          type="button"
                          className="text-gray-400 hover:text-gray-500"
                          onClick={onClose}
                        >
                          <XMarkIcon className="h-6 w-6" />
                        </button>
                      </div>
                    </div>

                    <div className="px-6 py-4">
                      {formErrors.submit && (
                        <div className="mb-4 p-4 bg-red-50 dark:bg-red-900/20 border-l-4 border-red-500 text-red-700 dark:text-red-200">
                          {formErrors.submit}
                        </div>
                      )}

                      <div className="space-y-4">
                        {/* Nombre */}
                        <div>
                          <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Nombre
                          </label>
                          <input
                            type="text"
                            id="name"
                            value={formData.name}
                            onChange={e => setFormData(prev => ({ ...prev, name: e.target.value }))}
                            className={`
                              mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring-black dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm
                              ${formErrors.name ? 'border-red-500' : ''}
                            `}
                          />
                          {formErrors.name && (
                            <p className="mt-1 text-sm text-red-500">{formErrors.name}</p>
                          )}
                        </div>

                        {/* Fase */}
                        <div className="relative" ref={phaseRef}>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                            Fase
                          </label>
                          <div className="relative">
                            <button
                              type="button"
                              onClick={() => setShowPhases(!showPhases)}
                              className="relative w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg pl-3 pr-10 py-2.5 text-left cursor-default focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                            >
                              <div className="flex items-center">
                                {PHASES.find(p => p.id === formData.phaseId)?.icon}
                                <span className="ml-2 block truncate">
                                  {PHASES.find(p => p.id === formData.phaseId)?.label}
                                </span>
                              </div>
                              <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                                <svg className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M10 3a1 1 0 01.707.293l3 3a1 1 0 01-1.414 1.414L10 5.414 7.707 7.707a1 1 0 01-1.414-1.414l3-3A1 1 0 0110 3zm-3.707 9.293a1 1 0 011.414 0L10 14.586l2.293-2.293a1 1 0 011.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                                </svg>
                              </span>
                            </button>

                            {showPhases && (
                              <div className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-700 shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">
                                {PHASES.map((phase) => (
                                  <div
                                    key={phase.id}
                                    className={`
                                      cursor-pointer select-none relative py-2.5 pl-3 pr-9 flex items-center transition-colors duration-150
                                      ${formData.phaseId === phase.id
                                        ? 'bg-gray-100 dark:bg-gray-600/50 text-gray-900 dark:text-gray-100'
                                        : 'text-gray-900 dark:text-gray-100 hover:bg-gray-50 dark:hover:bg-gray-600/30'
                                      }
                                    `}
                                    onClick={() => {
                                      // Si se está seleccionando una fase diferente a 'announcements' y el tipo es 'specific',
                                      // cambiamos el tipo de timing a 'before' para evitar inconsistencias
                                      if (phase.id !== 'announcements' && formData.timing.type === 'specific') {
                                        setFormData(prev => ({
                                          ...prev,
                                          phaseId: phase.id,
                                          timing: {
                                            ...prev.timing,
                                            type: 'before',
                                            value: 24,
                                            unit: 'hours'
                                          }
                                        }))
                                      } else {
                                        setFormData(prev => ({ ...prev, phaseId: phase.id }))
                                      }
                                      setShowPhases(false)
                                    }}
                                  >
                                    <span className={`
                                      ${formData.phaseId === phase.id
                                        ? 'text-gray-900 dark:text-gray-100'
                                        : 'text-gray-600 dark:text-gray-300'
                                      }
                                    `}>
                                      {phase.icon}
                                    </span>
                                    <span className={`
                                      ml-2 block truncate
                                      ${formData.phaseId === phase.id
                                        ? 'font-medium text-gray-900 dark:text-gray-100'
                                        : 'font-normal text-gray-600 dark:text-gray-300'
                                      }
                                    `}>
                                      {phase.label}
                                    </span>
                                    {formData.phaseId === phase.id && (
                                      <span className="absolute inset-y-0 right-0 flex items-center pr-4 text-gray-600 dark:text-gray-300">
                                        <svg className="h-5 w-5" viewBox="0 0 24 24" fill="currentColor">
                                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                        </svg>
                                      </span>
                                    )}
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Estado */}
                        <style jsx>{`
                          @keyframes pulse {
                            0% {
                              box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);
                            }
                            70% {
                              box-shadow: 0 0 0 6px rgba(34, 197, 94, 0);
                            }
                            100% {
                              box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
                            }
                          }
                          .pulse-animation {
                            animation: pulse 2s infinite;
                          }
                        `}</style>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5">
                            Estado
                          </label>
                          <button
                            type="button"
                            onClick={() => setFormData(prev => ({ ...prev, isActive: !prev.isActive }))}
                            className={`
                              relative w-full flex items-center justify-between px-4 py-3 rounded-xl border transition-all duration-200
                              ${formData.isActive
                                ? 'bg-green-50 border-green-100 dark:bg-green-900/10 dark:border-green-800/50'
                                : 'bg-gray-50 border-gray-200 dark:bg-gray-800 dark:border-gray-700'
                              }
                            `}
                          >
                            <div className="flex items-center space-x-3">
                              <div className="flex flex-col">
                                <span className={`
                                  text-sm font-medium transition-colors duration-200
                                  ${formData.isActive
                                    ? 'text-green-700 dark:text-green-300'
                                    : 'text-gray-600 dark:text-gray-400'
                                  }
                                `}>
                                  {formData.isActive ? 'Activa' : 'Inactiva'}
                                </span>
                              </div>
                              <div className={`
                                w-12 h-7 flex items-center rounded-full p-1 transition-colors duration-200
                                ${formData.isActive
                                  ? 'bg-green-500 dark:bg-green-400 pulse-animation'
                                  : 'bg-gray-300 dark:bg-gray-600'
                                }
                              `}>
                                <div className={`
                                  bg-white w-5 h-5 rounded-full shadow-md transform transition-transform duration-200 ease-in-out
                                  ${formData.isActive ? 'translate-x-5' : 'translate-x-0'}
                                `} />
                              </div>
                            </div>
                            {formData.isActive && (
                              <svg className="w-5 h-5 animate-pulse text-green-500 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                              </svg>
                            )}
                          </button>
                        </div>

                        {/* Descripción */}
                        <div>
                          <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Descripción
                          </label>
                          <textarea
                            id="description"
                            value={formData.description}
                            onChange={e => setFormData(prev => ({ ...prev, description: e.target.value }))}
                            rows={2}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring-black dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm"
                            placeholder="Breve descripción del propósito de esta automatización"
                          />
                        </div>

                        {/* Timing Configuration */}
                        <div className="space-y-4">
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            ¿Cuándo se enviará?
                          </label>

                          {/* Timing Type Selection */}
                          <div className="grid grid-cols-4 gap-2">
                            {TIMING_TYPES.map((type) => (
                              <button
                                key={type.value}
                                type="button"
                                onClick={() => {
                                  // Si se selecciona el tipo 'specific', asignar automáticamente a la fase 'announcements'
                                  if (type.value === 'specific') {
                                    setFormData(prev => ({
                                      ...prev,
                                      phaseId: 'announcements', // Asignar automáticamente a la fase Anuncios
                                      timing: {
                                        ...prev.timing,
                                        type: 'specific',
                                        value: 0
                                      }
                                    }))
                                  } else {
                                    setFormData(prev => ({
                                      ...prev,
                                      timing: {
                                        ...prev.timing,
                                        type: type.value as TimingConfig['type'],
                                        // Asegurarse de que value sea un número válido cuando se cambia el tipo
                                        value: type.value === 'during' ? 0 : (prev.timing.value || 24)
                                      }
                                    }))
                                  }
                                }}
                                className={`
                                  px-3 py-2 text-sm font-medium rounded-md
                                  ${formData.timing.type === type.value
                                    ? 'bg-gray-900 text-white dark:bg-gray-100 dark:text-gray-900'
                                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700'
                                  }
                                `}
                              >
                                {type.label}
                              </button>
                            ))}
                          </div>
                          
                          {/* Advertencia para tipo específico */}
                          {formData.timing.type === 'specific' && (
                            <div className="mt-3 p-4 rounded-lg bg-amber-50 border border-amber-200 dark:bg-amber-900/20 dark:border-amber-800/50">
                              <div className="flex">
                                <div className="flex-shrink-0">
                                  <svg className="h-5 w-5 text-amber-500 dark:text-amber-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                                  </svg>
                                </div>
                                <div className="ml-3">
                                  <h3 className="text-sm font-medium text-amber-800 dark:text-amber-300">Atención</h3>
                                  <div className="mt-1 text-sm text-amber-700 dark:text-amber-200">
                                    <p>Al seleccionar una fecha y hora específica, este mensaje se enviará a <strong>todos tus clientes</strong> simultáneamente en el momento indicado. Ideal para anuncios o promociones generales.</p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Timing Value Configuration */}
                          {formData.timing.type === 'specific' ? (
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                  Fecha
                                </label>
                                <input
                                  type="date"
                                  value={formData.timing.specificDate || ''}
                                  onChange={(e) => setFormData(prev => ({
                                    ...prev,
                                    timing: {
                                      ...prev.timing,
                                      specificDate: e.target.value
                                    }
                                  }))}
                                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring-black dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm"
                                  required={formData.timing.type === 'specific'}
                                />
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                  Hora
                                </label>
                                <div className="relative">
                                  <input
                                    type="time"
                                    value={formData.timing.specificTime || ''}
                                    onChange={(e) => setFormData(prev => ({
                                      ...prev,
                                      timing: {
                                        ...prev.timing,
                                        specificTime: e.target.value
                                      }
                                    }))}
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring-black dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm"
                                    required={formData.timing.type === 'specific'}
                                  />
                                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <svg className="h-5 w-5 text-gray-400 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                                    </svg>
                                  </div>
                                </div>
                              </div>
                            </div>
                          ) : formData.timing.type !== 'during' && (
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                  Valor
                                </label>
                            <input
                              type="number"
                                  min="1"
                              value={formData.timing.value}
                              onChange={(e) => setFormData(prev => ({
                                ...prev,
                                    timing: {
                                      ...prev.timing,
                                      value: parseInt(e.target.value)
                                    }
                                  }))}
                                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring-black dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm"
                                  required
                                />
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                  Unidad
                                </label>
                            <select
                              value={formData.timing.unit}
                                  onChange={(e) => setFormData(prev => ({
                                ...prev,
                                    timing: {
                                      ...prev.timing,
                                      unit: e.target.value as TimingConfig['unit']
                                    }
                                  }))}
                                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring-black dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm"
                                  required
                                >
                                  {TIME_UNITS.map((unit) => (
                                    <option key={unit.value} value={unit.value}>
                                      {unit.label}
                                    </option>
                                  ))}
                            </select>
                          </div>
                            </div>
                          )}
                        </div>

                        {/* Plantilla de Mensaje */}
                        <div>
                          <div className="flex items-center justify-between mb-1.5">
                            <label htmlFor="messageTemplate" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                              Mensaje
                            </label>
                            <button
                              type="button"
                              onClick={() => setShowVariables(!showVariables)}
                              className="inline-flex items-center text-sm text-primary-600 hover:text-primary-500 dark:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 px-2 py-1 rounded-md transition-colors duration-200"
                            >
                              <InformationCircleIcon className="h-5 w-5 mr-1.5" />
                              Variables Disponibles
                            </button>
                          </div>

                          {showVariables && (
                            <div className="mb-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                              <div className="flex flex-wrap gap-2">
                                {AVAILABLE_VARIABLES.map(variable => (
                                  <button
                                    key={variable.key}
                                    type="button"
                                    onClick={() => insertVariable(variable.key)}
                                    className="inline-flex items-center px-2.5 py-1.5 rounded-md text-sm bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-500 transition-colors duration-200"
                                    title={variable.description}
                                  >
                                    {variable.key}
                                  </button>
                                ))}
                              </div>
                            </div>
                          )}

                          <textarea
                            id="messageTemplate"
                            value={formData.messageTemplate}
                            onChange={e => setFormData(prev => ({ ...prev, messageTemplate: e.target.value }))}
                            rows={5}
                            className={`
                              block w-full rounded-md border-gray-300 shadow-sm focus:border-black focus:ring-black dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm
                              ${formErrors.messageTemplate ? 'border-red-500' : ''}
                            `}
                            placeholder="Escribe tu mensaje aquí. Usa las variables disponibles para personalizar el contenido."
                            required
                          />
                          {formErrors.messageTemplate && (
                            <p className="mt-1 text-sm text-red-500">{formErrors.messageTemplate}</p>
                          )}
                        </div>

                        {/* Condiciones Personalizadas */}
                        {formData.phaseId === 'custom' && <CustomConditionField />}
                      </div>
                    </div>

                    <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-between">
                      <div>
                        {automation && (
                          <button
                            type="button"
                            onClick={handleTestAutomation}
                            disabled={isTesting}
                            className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-green-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                          >
                            {isTesting ? (
                              <>
                                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Probando...
                              </>
                            ) : (
                              <>
                                <svg className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-.417A1 1 0 010 12a1 1 0 00-1.329.094L8 12.586l7.293-7.293a1 1 0 011.414 0z" />
                                </svg>
                                Probar Automatización
                              </>
                            )}
                          </button>
                        )}
                      </div>
                      <div className="flex space-x-3">
                        <button
                          type="button"
                          className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-black dark:focus:ring-white focus:ring-offset-2"
                          onClick={onClose}
                        >
                          Cancelar
                        </button>
                        <button
                          type="submit"
                          disabled={isSubmitting}
                          className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-black dark:bg-white dark:text-black hover:bg-gray-800 dark:hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-black dark:focus:ring-white focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {isSubmitting ? (
                            <>
                              <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                              </svg>
                              Guardando...
                            </>
                          ) : (
                            'Guardar'
                          )}
                        </button>
                      </div>
                    </div>
                  </form>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition.Root>

      {/* Diálogo de selección de cliente para prueba */}
      <Transition appear show={showTestDialog} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={() => setShowTestDialog(false)}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 z-10 overflow-y-auto">
            <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                enterTo="opacity-100 translate-y-0 sm:scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              >
                <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg">
                  <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex items-center justify-between">
                      <Dialog.Title as="h3" className="text-xl font-semibold leading-6 text-gray-900 dark:text-gray-100">
                        Seleccionar Cliente para Prueba
                      </Dialog.Title>
                      <button
                        type="button"
                        className="text-gray-400 hover:text-gray-500"
                        onClick={() => setShowTestDialog(false)}
                      >
                        <XMarkIcon className="h-6 w-6" />
                      </button>
                    </div>
                  </div>

                  <div className="px-6 py-4">
                    {loadingClients ? (
                      <div className="text-center py-4">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black dark:border-white mx-auto"></div>
                        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">Cargando clientes...</p>
                      </div>
                    ) : testClients.length === 0 ? (
                      <div className="text-center py-4">
                        <p className="text-sm text-gray-500 dark:text-gray-400">No hay clientes con citas disponibles</p>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {testClients.map(client => (
                          <button
                            key={client.id}
                            onClick={() => handleConfirmTest(client.id)}
                            className="w-full text-left px-4 py-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 flex items-center space-x-3"
                          >
                            {client.photoURL ? (
                              <img src={client.photoURL} alt={client.name} className="h-10 w-10 rounded-full object-cover" />
                            ) : (
                              <div className="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center">
                                <span className="text-lg font-medium text-gray-600 dark:text-gray-300">
                                  {client.name?.charAt(0).toUpperCase()}
                                </span>
                              </div>
                            )}
                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{client.name}</p>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </>
  )
}
