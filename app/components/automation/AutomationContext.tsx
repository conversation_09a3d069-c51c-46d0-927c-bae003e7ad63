'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'
import { collection, query, where, onSnapshot, addDoc, updateDoc, deleteDoc, doc, serverTimestamp, getDoc, getDocs, Timestamp } from 'firebase/firestore'
import { ref, get, set, push, update, onValue, query as rtdbQuery, orderByChild, equalTo } from 'firebase/database'
import { FirebaseError } from 'firebase/app'
import { database as rtdb } from '@/lib/firebase/config'
import { db } from '@/lib/firebase/config'
import { useAuth } from '@/app/contexts/AuthContext'
import { addDays, addHours, addMinutes, addMonths, addWeeks, isBefore, isAfter, isSameMinute, format } from 'date-fns'
import { formatInTimeZone } from 'date-fns-tz'
import { es } from 'date-fns/locale'
import { automationService } from '@/lib/services/AutomationService'
import { useRouter } from 'next/navigation'
import { useToast } from '@/app/hooks/useToast'

export type PhaseId = 'pre' | 'post' | 'custom' | 'announcements'
export type TriggerEvent = 'appointment_reminder' | 'care_instructions' | 'feedback_request' | 'promotion'
export type Channel = 'whatsapp' | 'email' | 'sms' | 'instagram' | 'facebook'

export type CustomConditionType = 'pending_sessions' | 'total_spent' | 'appointment_count'
export type CustomConditionOperator = '>' | '<' | '=' | '>=' | '<='

export interface CustomCondition {
  type: CustomConditionType
  operator: CustomConditionOperator
  value: number
}

export interface TestAutomationOptions {
  clientId?: string
  appointmentId?: string
}

export interface TimingConfig {
  type: 'before' | 'after' | 'during' | 'specific'
  value: number
  unit: 'minutes' | 'hours' | 'days' | 'weeks' | 'months'
  specificDate?: string  // Para el tipo 'specific'
  specificTime?: string  // Para el tipo 'specific'
}

export interface Automation {
  id: string
  userId: string
  name: string
  description: string
  phaseId: PhaseId
  trigger: TriggerEvent
  timing: TimingConfig
  messageTemplate: string
  isActive: boolean
  variables: string[]
  createdAt: Date
  updatedAt: Date
  channel: Channel
  lastProcessed?: number
  customConditions?: CustomCondition[]
}

interface AutomationContextType {
  automations: Automation[]
  loading: boolean
  error: string | null
  createAutomation: (data: Omit<Automation, 'id' | 'userId' | 'createdAt' | 'updatedAt'>) => Promise<void>
  updateAutomation: (id: string, data: Partial<Automation>) => Promise<void>
  deleteAutomation: (id: string) => Promise<void>
  toggleAutomation: (id: string) => Promise<void>
  testAutomation: (automation: Automation, options?: TestAutomationOptions) => Promise<{ success: boolean, results: any[], message: string }>
  evaluateCustomConditions: (clientId: string, conditions: CustomCondition[]) => Promise<boolean>
}

const AutomationContext = createContext<AutomationContextType | undefined>(undefined)

export function useAutomation() {
  const context = useContext(AutomationContext)
  if (!context) {
    throw new Error('useAutomation must be used within an AutomationProvider')
  }
  return context
}

export function AutomationProvider({ children }: { children: React.ReactNode }) {
  const [automations, setAutomations] = useState<Automation[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { user } = useAuth()

  useEffect(() => {
    if (!user?.uid) {
      setAutomations([])
      setLoading(false)
      return
    }

    const automationsRef = ref(rtdb, `automations/${user.uid}`)
    
    const unsubscribe = onValue(automationsRef, (snapshot) => {
      try {
      const automationsData: Automation[] = []
      snapshot.forEach((childSnapshot) => {
        const data = childSnapshot.val()
        automationsData.push({
          id: childSnapshot.key!,
          ...data,
          createdAt: new Date(data.createdAt),
            updatedAt: new Date(data.updatedAt),
            lastProcessed: data.lastProcessed ? data.lastProcessed : undefined
          })
        })
      setAutomations(automationsData)
        setError(null)
      } catch (error) {
        console.error('Error loading automations:', error)
        setError(error instanceof Error ? error.message : 'Error desconocido')
      } finally {
      setLoading(false)
      }
    }, (error) => {
      console.error('Error in RTDB subscription:', error)
      setError(error instanceof Error ? error.message : 'Error desconocido')
      setLoading(false)
    })

    return () => unsubscribe()
  }, [user])

  const createAutomation = async (data: Omit<Automation, 'id' | 'userId' | 'createdAt' | 'updatedAt'>) => {
    if (!user) throw new Error('No user authenticated')

    try {
      // Filtrar propiedades undefined que Firebase no acepta
      const filteredData = Object.entries(data).reduce((acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = value;
        }
        return acc;
      }, {} as Record<string, any>);
      
      const automationsRef = ref(rtdb, `automations/${user.uid}`)
      const newAutomationRef = push(automationsRef)
      
      const now = new Date().toISOString()
      await set(newAutomationRef, {
        ...filteredData,
        userId: user.uid,
        createdAt: now,
        updatedAt: now
      })
    } catch (error) {
      console.error('Error creating automation:', error)
      throw error
    }
  }

  const updateAutomation = async (id: string, data: Partial<Automation>) => {
    if (!user) throw new Error('No user authenticated')

    try {
      // Filtrar propiedades undefined que Firebase no acepta
      const filteredData = Object.entries(data).reduce((acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = value;
        }
        return acc;
      }, {} as Record<string, any>);
      
      const automationRef = ref(rtdb, `automations/${user.uid}/${id}`)
      await update(automationRef, {
        ...filteredData,
        updatedAt: new Date().toISOString()
      })
    } catch (error) {
      console.error('Error updating automation:', error)
      throw error
    }
  }

  const deleteAutomation = async (id: string) => {
    if (!user) throw new Error('No user authenticated')
    
    try {
      const automationRef = ref(rtdb, `automations/${user.uid}/${id}`)
      await set(automationRef, null)
    } catch (error) {
      console.error('Error deleting automation:', error)
      throw error
    }
  }

  const toggleAutomation = async (id: string) => {
    if (!user) throw new Error('No user authenticated')

    const automation = automations.find(a => a.id === id)
    if (!automation) throw new Error('Automation not found')

    try {
      const automationRef = ref(rtdb, `automations/${user.uid}/${id}`)
      await update(automationRef, {
        isActive: !automation.isActive,
        updatedAt: new Date().toISOString()
      })
    } catch (error) {
      console.error('Error toggling automation:', error)
      throw error
    }
  }

  interface Appointment {
    id: string
    clientId: string  // Este es el conversationId
    clientName: string
    date: string
    // ... otros campos
  }

  const testAutomation = async (automation: Automation, options?: TestAutomationOptions) => {
    if (!user) throw new Error('No user authenticated')
    
    try {
      // Verificar que hay un clientId para evaluar
      if (!options?.clientId) {
        throw new Error('Se requiere un ID de cliente para probar la automatización');
      }
      
      // Si es una automatización personalizada, evaluar condiciones
      if (automation.phaseId === 'custom' && automation.customConditions && automation.customConditions.length > 0) {
        const conditionsMet = await evaluateCustomConditions(options.clientId, automation.customConditions);
        
        if (!conditionsMet) {
        return {
          success: false,
            results: [], 
            message: 'El cliente no cumple con las condiciones personalizadas para esta automatización' 
          };
        }
      }
      
      // Aquí implementaríamos el envío del mensaje de prueba
      // Por ahora, simular éxito
      return { 
        success: true, 
        results: [],
        message: 'Automatización probada correctamente. El cliente cumple con todas las condiciones.' 
      };
    } catch (error) {
      console.error('Error testing automation:', error);
      let errorMessage = 'Error desconocido al probar la automatización';
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      return { 
        success: false, 
        results: [],
        message: errorMessage 
      };
    }
  }

  // Función para evaluar las condiciones personalizadas de un cliente
  const evaluateCustomConditions = async (clientId: string, conditions: CustomCondition[]): Promise<boolean> => {
    if (!conditions || conditions.length === 0) {
      return true; // Sin condiciones, pasa la validación
    }

    try {
      // Buscar appointments para este cliente
      const appointmentsRef = ref(rtdb, 'appointments');
      const appointmentsQuery = rtdbQuery(appointmentsRef, orderByChild('clientId'), equalTo(clientId));
      const snapshot = await get(appointmentsQuery);
      
      if (!snapshot.exists()) {
        return false; // Sin citas, no cumple ninguna condición
      }
      
      const appointments: any[] = [];
      snapshot.forEach((childSnapshot) => {
        appointments.push({
          id: childSnapshot.key,
          ...childSnapshot.val()
        });
      });
      
      // Calcular sesiones pendientes basado en diseños multi-sesión
      let pendingSessions = 0;
      
      // Agrupar citas por diseño (usando relatedAppointmentId)
      const designGroups = new Map();
      
      appointments.forEach(app => {
        const designId = app.relatedAppointmentId || app.id;
        if (!designGroups.has(designId)) {
          designGroups.set(designId, []);
        }
        designGroups.get(designId).push(app);
      });
      
      // Calcular sesiones pendientes para cada grupo de diseño
      designGroups.forEach(appsGroup => {
        // Encontrar la cita con la información más completa (probablemente la original)
        const mainAppointment = appsGroup.reduce((prev, current) => {
          // Preferimos la que tenga totalSessions definido
          if ((current.totalSessions || 0) > (prev.totalSessions || 0)) return current;
          // Si tienen el mismo totalSessions, preferimos la original (la que no tiene relatedAppointmentId)
          if (!current.relatedAppointmentId && prev.relatedAppointmentId) return current;
          return prev;
        }, appsGroup[0]);
        
        const totalSessions = mainAppointment.totalSessions || 1;
        const completedSessions = mainAppointment.completedSessions || appsGroup.length;
        
        // Solo contar si hay sesiones pendientes (totalSessions > completedSessions)
        if (totalSessions > completedSessions) {
          pendingSessions += (totalSessions - completedSessions);
        }
      });
      
      // Calcular gasto total
      const totalSpent = appointments.reduce((total, app) => {
        let appointmentTotal = 0;
        if (app.depositPaid && app.bookingPrice) {
          appointmentTotal += parseFloat(app.bookingPrice) || 0;
        }
        if (app.sessionPaid && app.sessionPrice) {
          appointmentTotal += parseFloat(app.sessionPrice) || 0;
        }
        return total + appointmentTotal;
      }, 0);
      
      // Número total de citas (sin importar el estado)
      const appointmentCount = appointments.length;
      
      console.log(`Cliente ${clientId} - Métricas: Sesiones pendientes=${pendingSessions}, Gasto total=${totalSpent}, Cantidad de citas=${appointmentCount}`);
      
      // Evaluar cada condición
      return conditions.every(condition => {
        const { type, operator, value } = condition;
        let metricValue = 0;
        
        switch(type) {
          case 'pending_sessions':
            metricValue = pendingSessions;
            break;
          case 'total_spent':
            metricValue = totalSpent;
            break;
          case 'appointment_count':
            metricValue = appointmentCount;
            break;
          default:
            return false;
        }
        
        console.log(`Evaluando condición: ${type} ${operator} ${value} (valor actual: ${metricValue})`);
        
        // Evaluar operador
        switch(operator) {
          case '>': return metricValue > value;
          case '<': return metricValue < value;
          case '=': return metricValue === value;
          case '>=': return metricValue >= value;
          case '<=': return metricValue <= value;
          default: return false;
        }
      });
    } catch (error) {
      console.error('Error evaluando condiciones personalizadas:', error);
      return false;
    }
  };

  return (
    <AutomationContext.Provider value={{
      automations,
      createAutomation,
      updateAutomation,
      deleteAutomation,
      toggleAutomation,
      testAutomation,
      loading,
      error,
      evaluateCustomConditions
    }}>
      {children}
    </AutomationContext.Provider>
  )
}
