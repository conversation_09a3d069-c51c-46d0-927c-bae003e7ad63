'use client';

import React from 'react';
import { playNotificationSound, notifyNewMessage, updatePageTitle, resetNotifications } from './services/messageNotifications';

export default function TestNotifications() {
  const handleTestSound = async () => {
    console.log('🔊 Probando sonido de notificación...');
    try {
      await playNotificationSound();
      console.log('✅ Sonido reproducido exitosamente');
    } catch (error) {
      console.error('❌ Error al reproducir sonido:', error);
    }
  };

  const handleTestNotification = async () => {
    console.log('🔔 Probando notificación completa...');
    try {
      await notifyNewMessage(1, false);
      console.log('✅ Notificación enviada exitosamente');
    } catch (error) {
      console.error('❌ Error al enviar notificación:', error);
    }
  };

  const handleTestTitle = () => {
    console.log('📝 Probando actualización de título...');
    updatePageTitle(5);
    console.log('✅ Título actualizado');
  };

  const handleResetTitle = () => {
    console.log('🔄 Reseteando título...');
    resetNotifications();
    console.log('✅ Título reseteado');
  };

  return (
    <div style={{ 
      position: 'fixed', 
      top: '20px', 
      right: '20px', 
      background: 'white', 
      padding: '20px', 
      border: '2px solid #ccc',
      borderRadius: '8px',
      zIndex: 9999,
      boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
    }}>
      <h3>🧪 Test de Notificaciones</h3>
      <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
        <button 
          onClick={handleTestSound}
          style={{ 
            padding: '8px 16px', 
            backgroundColor: '#007bff', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          🔊 Probar Sonido
        </button>
        
        <button 
          onClick={handleTestNotification}
          style={{ 
            padding: '8px 16px', 
            backgroundColor: '#28a745', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          🔔 Probar Notificación Completa
        </button>
        
        <button 
          onClick={handleTestTitle}
          style={{ 
            padding: '8px 16px', 
            backgroundColor: '#ffc107', 
            color: 'black', 
            border: 'none', 
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          📝 Probar Título (5 mensajes)
        </button>
        
        <button 
          onClick={handleResetTitle}
          style={{ 
            padding: '8px 16px', 
            backgroundColor: '#6c757d', 
            color: 'white', 
            border: 'none', 
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          🔄 Resetear Título
        </button>
      </div>
      
      <p style={{ fontSize: '12px', marginTop: '10px', color: '#666' }}>
        Abre la consola del navegador para ver los logs detallados.
      </p>
    </div>
  );
}
