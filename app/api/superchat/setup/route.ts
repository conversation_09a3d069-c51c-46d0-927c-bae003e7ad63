import { NextRequest, NextResponse } from 'next/server';
import { logger } from '@/lib/logger';
import { db } from '@/lib/firebase-admin';

// Función para configurar el webhook de Superchat
async function configureSuperchatWebhook(apiKey: string) {
  try {
    // Configurar el webhook
    const webhookResponse = await fetch('https://api.superchat.com/v1.0/webhooks', {
      method: 'POST',
      headers: {
        'X-API-KEY': apiKey,
        'accept': 'application/json',
        'content-type': 'application/json'
      },
      body: JSON.stringify({
        target_url: `${process.env.NEXT_PUBLIC_APP_URL || 'https://tatu.ink'}/api/webhooks/unipile`,
        // La ruta anterior era incorrecta: /api/webhook/superchat
        events: [
          {
            type: "message_inbound"
          }
        ]
      })
    });

    if (!webhookResponse.ok) {
      const errorText = await webhookResponse.text();
      logger.error(`Error al configurar webhook de Superchat: ${errorText}`);
      return { success: false, error: 'Error al configurar webhook', details: errorText };
    }

    const webhookData = await webhookResponse.json();
    return { success: true, webhook: webhookData };
  } catch (error) {
    logger.error('Error al configurar webhook de Superchat:', error);
    return { success: false, error: 'Error al configurar webhook' };
  }
}

// Función para obtener los canales de Superchat
async function getSuperchatChannels(apiKey: string) {
  try {
    const channelsResponse = await fetch('https://api.superchat.com/v1.0/channels?limit=50', {
      method: 'GET',
      headers: {
        'X-API-KEY': apiKey,
        'accept': 'application/json'
      }
    });

    if (!channelsResponse.ok) {
      const errorText = await channelsResponse.text();
      logger.error(`Error al obtener canales de Superchat: ${errorText}`);
      return { success: false, error: 'Error al obtener canales', details: errorText };
    }

    const channelsData = await channelsResponse.json();
    return { success: true, channels: channelsData };
  } catch (error) {
    logger.error('Error al obtener canales de Superchat:', error);
    return { success: false, error: 'Error al obtener canales' };
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId, apiKey } = await request.json();

    if (!userId || !apiKey) {
      return NextResponse.json(
        { success: false, error: 'UserId y apiKey son requeridos' },
        { status: 400 }
      );
    }

    // Configurar webhook
    const webhookResult = await configureSuperchatWebhook(apiKey);
    
    // Si hay error en la configuración del webhook, igual continuamos para obtener canales
    
    // Obtener canales
    const channelsResult = await getSuperchatChannels(apiKey);

    // Guardar información en Firestore
    if (channelsResult.success) {
      // Usar la referencia correcta con firebase-admin
      const userRef = db.collection('users').doc(userId);
      const superchatData: any = {
        superchatApiKey: apiKey,
        superchatConfigured: true,
        updatedAt: new Date()
      };

      // Si hay canales, guardarlos
      if (channelsResult.channels?.results) {
        // Convertir los resultados en un mapa para guardarlo más fácilmente
        const channels = channelsResult.channels.results.map((channel: any) => ({
          id: channel.id,
          type: channel.type,
          name: channel.name
        }));
        
        superchatData.superchatChannels = channels;
      }

      // Si se configuró el webhook correctamente, guardar su ID
      if (webhookResult.success && webhookResult.webhook) {
        superchatData.superchatWebhookId = webhookResult.webhook.id;
      }

      await userRef.update(superchatData);
    }

    return NextResponse.json({
      success: true,
      webhookResult,
      channelsResult
    });
    
  } catch (error) {
    logger.error('Error en la configuración de Superchat:', error);
    return NextResponse.json(
      { success: false, error: 'Error en la configuración de Superchat' },
      { status: 500 }
    );
  }
} 