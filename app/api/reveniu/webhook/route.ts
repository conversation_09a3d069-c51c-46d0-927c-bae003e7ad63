import { NextResponse } from 'next/server';
import { db } from '@/lib/firebase-admin';
import { reveniuConfig } from '@/lib/reveniu/config';

export async function POST(request: Request) {
  try {
    // Obtener el cuerpo del webhook
    const rawWebhookData = await request.json();
    console.log('📥 Webhook de Reveniu recibido:', JSON.stringify(rawWebhookData, null, 2));

    // Estructura de webhook anidada
    let webhookData = rawWebhookData;
    
    // Manejar formato anidado donde los datos vienen dentro de otro campo 'data'
    if (rawWebhookData.data && typeof rawWebhookData.data === 'object') {
      webhookData = rawWebhookData.data;
      console.log('🔍 Detectada estructura anidada, extrayendo datos internos');
    }

    // Verificar que tenemos un evento y datos
    if (!webhookData.event) {
      console.error('❌ Webhook inválido: no contiene campo event');
      return NextResponse.json({ error: 'Webhook inválido, falta el campo event' }, { status: 400 });
    }

    if (!webhookData.data) {
      console.error('❌ Webhook inválido: no contiene campo data');
      return NextResponse.json({ error: 'Webhook inválido, falta el campo data' }, { status: 400 });
    }

    const event = webhookData.event;
    const data = webhookData.data;

    console.log(`🔔 Evento recibido: ${event}`);
    console.log(`📋 Datos del evento:`, JSON.stringify(data, null, 2));

    // Verificar que tenemos un ID de suscripción
    if (!data.subscription_id) {
      console.error('❌ No se encontró subscription_id en los datos del webhook');
      return NextResponse.json({ error: 'No se encontró subscription_id en los datos' }, { status: 400 });
    }

    // Manejar diferentes tipos de eventos
    switch (event) {
      case 'subscription_renewal_cancelled':
        await handleSubscriptionCancelled(data);
        break;
      case 'subscription_deactivated':
        await handleSubscriptionDeactivated(data);
        break;
      default:
        console.log(`⚠️ Evento no manejado: ${event}`);
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('❌ Error procesando webhook:', error);
    return NextResponse.json({ error: error.message || 'Error interno del servidor' }, { status: 500 });
  }
}

/**
 * Maneja el evento de cancelación de suscripción
 * Similar a como lo hace PayPal, mantiene el acceso hasta el final del período pagado
 */
async function handleSubscriptionCancelled(data: any) {
  try {
    const subscriptionId = data.subscription_id;
    const externalId = data.subscription_external_id;
    const cancelledBy = data.cancelled_by;
    const cancelReason = data.cancel_reason;
    const feedback = data.feedback;

    console.log('🚫 Suscripción cancelada:', subscriptionId);
    console.log('🧾 Razón de cancelación:', cancelReason);
    console.log('💬 Feedback:', feedback);
    console.log('🔑 ID externo del usuario:', externalId);

    if (!subscriptionId) {
      console.error('❌ ID de suscripción no proporcionado en el webhook');
      return;
    }

    // Obtener los detalles de la suscripción para obtener next_due
    let subscriptionDetails;
    let finalAccessDate = null;
    let userId = externalId || null; // Usar el external_id si está disponible
    let userEmail = null;

    // Usar la API key directamente
    const apiKey = "Q3_irVOLj-h39JoviFydH6GLnZgEkuDVlF0y";
    const baseUrl = 'https://production.reveniu.com';

    try {
      // URL para obtener los detalles de la suscripción
      const detailsUrl = `${baseUrl}/api/v1/subscriptions/${subscriptionId}/`;
      
      const detailsResponse = await fetch(detailsUrl, {
        method: 'GET',
        headers: {
          'Reveniu-Secret-Key': apiKey,
          'Content-Type': 'application/json',
        }
      });
      
      if (detailsResponse.ok) {
        subscriptionDetails = await detailsResponse.json();
        console.log('📋 Detalles de suscripción:', JSON.stringify(subscriptionDetails, null, 2));
        
        // Obtener la fecha del próximo pago (next_due) como finalAccessDate
        if (subscriptionDetails.next_due) {
          finalAccessDate = new Date(subscriptionDetails.next_due);
          console.log('📅 Fecha final de acceso:', finalAccessDate);
        } else {
          // Si no hay next_due, calcular una fecha aproximada (30 días desde hoy)
          finalAccessDate = new Date();
          finalAccessDate.setDate(finalAccessDate.getDate() + 30);
          console.log('⚠️ Sin datos de próximo pago, usando fecha estimada:', finalAccessDate);
        }

        // Obtener el email del cliente
        if (subscriptionDetails.customer && subscriptionDetails.customer.email) {
          userEmail = subscriptionDetails.customer.email;
          console.log('✉️ Email del cliente:', userEmail);
        }
        
        // Si el external_id no estaba en el webhook pero está en los detalles, usarlo
        if (!userId && subscriptionDetails.external_id) {
          userId = subscriptionDetails.external_id;
          console.log('✅ Usuario encontrado desde external_id en detalles:', userId);
        }
      } else {
        console.error('❌ Error al obtener detalles de la suscripción:', await detailsResponse.text());
        // Calcular una fecha aproximada si no podemos obtener los detalles
        finalAccessDate = new Date();
        finalAccessDate.setDate(finalAccessDate.getDate() + 30);
      }
    } catch (error) {
      console.error('❌ Error al obtener detalles de suscripción:', error);
      // Establecer una fecha por defecto en caso de error
      finalAccessDate = new Date();
      finalAccessDate.setDate(finalAccessDate.getDate() + 30);
    }

    // Si ya tenemos el userId del external_id, no necesitamos buscar
    if (!userId) {
      console.log('⚠️ No se encontró external_id, buscando usuario por otros medios');
      
      // Buscar al usuario usando el ID de suscripción o el email
      let userDoc = null;

      // 1. Primero buscar por el mapeo de suscripción
      try {
        const subscriptionMapping = await db.collection('subscription_mappings')
          .where('providerSubscriptionId', '==', subscriptionId.toString())
          .where('provider', '==', 'reveniu')
          .limit(1)
          .get();

        if (!subscriptionMapping.empty) {
          const mappingDoc = subscriptionMapping.docs[0];
          userId = mappingDoc.data().userId;
          console.log('✅ Usuario encontrado a través del mapeo:', userId);
        }
      } catch (error) {
        console.error('❌ Error al buscar mapeo de suscripción:', error);
      }

      // 2. Si no se encontró por mapeo y tenemos el email, buscar por email
      if (!userId && userEmail) {
        try {
          const userQuery = await db.collection('users')
            .where('email', '==', userEmail)
            .limit(1)
            .get();

          if (!userQuery.empty) {
            userDoc = userQuery.docs[0];
            userId = userDoc.id;
            console.log('✅ Usuario encontrado por email:', userId);
          }
        } catch (error) {
          console.error('❌ Error al buscar usuario por email:', error);
        }
      }
    }

    if (!userId) {
      console.error('❌ No se pudo encontrar al usuario para la suscripción:', subscriptionId);
      return;
    }

    console.log('✅ Usuario identificado:', userId);

    // Si no tenemos el documento del usuario, obtenerlo ahora
    let userDoc = null;
    try {
      userDoc = await db.collection('users').doc(userId).get();
      if (!userDoc.exists) {
        console.error('❌ Documento de usuario no existe:', userId);
        return;
      }
      console.log('✅ Documento de usuario encontrado');
    } catch (error) {
      console.error('❌ Error al obtener documento de usuario:', error);
      return;
    }

    // Actualizar el usuario manteniendo el estado activo hasta finalAccessDate
    try {
      const userUpdateData: Record<string, any> = {
        subscriptionCancelled: true,
        cancelledAt: new Date(),
        cancelledReason: cancelReason || 'Usuario canceló la suscripción',
        cancelledFeedback: feedback || '',
        subscriptionEndDate: finalAccessDate,
        finalAccessDate: finalAccessDate,
        subscriptionAutoRenew: false,
        updatedAt: new Date()
      };

      await db.collection('users').doc(userId).update(userUpdateData);
      console.log('✅ Usuario actualizado: suscripción marcada como cancelada, acceso hasta:', finalAccessDate);
    } catch (error) {
      console.error('❌ Error al actualizar usuario:', error);
    }

    // Actualizar el documento de suscripción
    try {
      const subscriptionData: Record<string, any> = {
        cancelledAt: new Date(),
        cancelAtPeriodEnd: true,
        status: 'active', // Mantener como activa hasta el final del periodo
        actualStatus: 'cancelled', // Guardar el estado real para referencia
        updatedAt: new Date(),
        finalAccessDate: finalAccessDate
      };

      await db.collection('subscriptions').doc(userId).update(subscriptionData);
      console.log('✅ Documento de suscripción actualizado:', userId);
    } catch (error) {
      console.error('❌ Error al actualizar documento de suscripción:', error);
    }

    // Actualizar el mapeo si existe
    try {
      const mappingQuery = await db.collection('subscription_mappings')
        .where('providerSubscriptionId', '==', subscriptionId.toString())
        .where('provider', '==', 'reveniu')
        .limit(1)
        .get();

      if (!mappingQuery.empty) {
        const mappingDoc = mappingQuery.docs[0];
        const mappingData: Record<string, any> = {
          cancelledAt: new Date(),
          finalAccessDate: finalAccessDate,
          status: 'active',
          actualStatus: 'cancelled',
          updatedAt: new Date()
        };

        await mappingDoc.ref.update(mappingData);
        console.log('✅ Mapeo actualizado');
      } else {
        console.log('ℹ️ No se encontró mapeo existente para actualizar');
      }
    } catch (error) {
      console.error('❌ Error al actualizar mapeo:', error);
    }

    // Agregar entrada al historial
    try {
      const historyData: Record<string, any> = {
        userId,
        subscriptionId: subscriptionId.toString(),
        action: 'cancelled',
        timestamp: new Date(),
        finalAccessDate: finalAccessDate,
        provider: 'reveniu',
        cancelReason: cancelReason || '',
        feedback: feedback || '',
        cancelledBy: cancelledBy || 'user'
      };

      await db.collection('subscription_history').add(historyData);
      console.log('✅ Historial actualizado');
    } catch (error) {
      console.error('❌ Error al actualizar historial:', error);
    }

    // Programar una tarea para cambiar el estado a inactive después de finalAccessDate
    try {
      await db.collection('scheduled_tasks').add({
        type: 'subscription_expiration',
        userId: userId,
        subscriptionId: subscriptionId.toString(),
        scheduledFor: finalAccessDate,
        provider: 'reveniu',
        createdAt: new Date()
      });
      console.log('✅ Tarea programada para cambiar estado a inactive en:', finalAccessDate);
    } catch (error) {
      console.error('❌ Error al programar tarea:', error);
    }
    
    console.log('✅✅✅ Procesamiento del webhook de cancelación completado con éxito ✅✅✅');
  } catch (error) {
    console.error('❌ Error en handleSubscriptionCancelled:', error);
  }
}

/**
 * Maneja el evento de desactivación de suscripción (subscription_deactivated)
 * Registra el evento pero NO cambia el estado a "free" inmediatamente
 * Espera hasta que se pase la fecha de expiración para cambiar el estado
 */
async function handleSubscriptionDeactivated(data: any) {
  try {
    const subscriptionId = data.subscription_id;
    const externalId = data.subscription_external_id;

    console.log('🚫 Suscripción desactivada:', subscriptionId);
    console.log('🔑 ID externo del usuario:', externalId);

    if (!subscriptionId) {
      console.error('❌ ID de suscripción no proporcionado en el webhook');
      return;
    }

    // Obtener los detalles de la suscripción para obtener next_due
    let subscriptionDetails;
    let finalAccessDate = null;
    let userId = externalId || null; // Usar el external_id si está disponible
    let userEmail = null;

    // Usar la API key directamente
    const apiKey = "Q3_irVOLj-h39JoviFydH6GLnZgEkuDVlF0y";
    const baseUrl = 'https://production.reveniu.com';

    try {
      // URL para obtener los detalles de la suscripción
      const detailsUrl = `${baseUrl}/api/v1/subscriptions/${subscriptionId}/`;
      
      const detailsResponse = await fetch(detailsUrl, {
        method: 'GET',
        headers: {
          'Reveniu-Secret-Key': apiKey,
          'Content-Type': 'application/json',
        }
      });
      
      if (detailsResponse.ok) {
        subscriptionDetails = await detailsResponse.json();
        console.log('📋 Detalles de suscripción:', JSON.stringify(subscriptionDetails, null, 2));
        
        // Obtener la fecha del próximo pago (next_due) como finalAccessDate
        if (subscriptionDetails.next_due) {
          finalAccessDate = new Date(subscriptionDetails.next_due);
          console.log('📅 Fecha final de acceso:', finalAccessDate);
        } else {
          // Si no hay next_due, calcular una fecha aproximada (30 días desde hoy)
          finalAccessDate = new Date();
          finalAccessDate.setDate(finalAccessDate.getDate() + 30);
          console.log('⚠️ Sin datos de próximo pago, usando fecha estimada:', finalAccessDate);
        }

        // Obtener el email del cliente
        if (subscriptionDetails.customer && subscriptionDetails.customer.email) {
          userEmail = subscriptionDetails.customer.email;
          console.log('✉️ Email del cliente:', userEmail);
        }
        
        // Si el external_id no estaba en el webhook pero está en los detalles, usarlo
        if (!userId && subscriptionDetails.external_id) {
          userId = subscriptionDetails.external_id;
          console.log('✅ Usuario encontrado desde external_id en detalles:', userId);
        }
      } else {
        console.error('❌ Error al obtener detalles de la suscripción:', await detailsResponse.text());
        // Calcular una fecha aproximada si no podemos obtener los detalles
        finalAccessDate = new Date();
        finalAccessDate.setDate(finalAccessDate.getDate() + 30);
      }
    } catch (error) {
      console.error('❌ Error al obtener detalles de suscripción:', error);
      // Establecer una fecha por defecto en caso de error
      finalAccessDate = new Date();
      finalAccessDate.setDate(finalAccessDate.getDate() + 30);
    }

    // Si ya tenemos el userId del external_id, no necesitamos buscar
    if (!userId) {
      console.log('⚠️ No se encontró external_id, buscando usuario por otros medios');
      
      // Buscar al usuario usando el ID de suscripción o el email
      let userDoc = null;

      // 1. Primero buscar por el mapeo de suscripción
      try {
        const subscriptionMapping = await db.collection('subscription_mappings')
          .where('providerSubscriptionId', '==', subscriptionId.toString())
          .where('provider', '==', 'reveniu')
          .limit(1)
          .get();

        if (!subscriptionMapping.empty) {
          const mappingDoc = subscriptionMapping.docs[0];
          userId = mappingDoc.data().userId;
          console.log('✅ Usuario encontrado a través del mapeo:', userId);
        }
      } catch (error) {
        console.error('❌ Error al buscar mapeo de suscripción:', error);
      }

      // 2. Si no se encontró por mapeo y tenemos el email, buscar por email
      if (!userId && userEmail) {
        try {
          const userQuery = await db.collection('users')
            .where('email', '==', userEmail)
            .limit(1)
            .get();

          if (!userQuery.empty) {
            userDoc = userQuery.docs[0];
            userId = userDoc.id;
            console.log('✅ Usuario encontrado por email:', userId);
          }
        } catch (error) {
          console.error('❌ Error al buscar usuario por email:', error);
        }
      }
    }

    if (!userId) {
      console.error('❌ No se pudo encontrar al usuario para la suscripción:', subscriptionId);
      return;
    }

    console.log('✅ Usuario identificado:', userId);

    // Si no tenemos el documento del usuario, obtenerlo ahora
    let userDoc = null;
    try {
      userDoc = await db.collection('users').doc(userId).get();
      if (!userDoc.exists) {
        console.error('❌ Documento de usuario no existe:', userId);
        return;
      }
      console.log('✅ Documento de usuario encontrado');
    } catch (error) {
      console.error('❌ Error al obtener documento de usuario:', error);
      return;
    }

    // Actualizar el usuario registrando la desactivación pero SIN cambiar el estado a "free" inmediatamente
    try {
      const userUpdateData: Record<string, any> = {
        // Registramos que la suscripción fue desactivada pero mantenemos el estado actual
        subscriptionDeactivated: true,
        deactivatedAt: new Date(),
        subscriptionEndDate: finalAccessDate,
        finalAccessDate: finalAccessDate,
        updatedAt: new Date()
      };

      await db.collection('users').doc(userId).update(userUpdateData);
      console.log('✅ Usuario actualizado: suscripción marcada como desactivada, acceso hasta:', finalAccessDate);
    } catch (error) {
      console.error('❌ Error al actualizar usuario:', error);
    }

    // Actualizar el documento de suscripción
    try {
      const subscriptionData: Record<string, any> = {
        deactivatedAt: new Date(),
        status: 'active', // Mantener como activa hasta el final del periodo
        actualStatus: 'deactivated', // Guardar el estado real para referencia
        updatedAt: new Date(),
        finalAccessDate: finalAccessDate
      };

      await db.collection('subscriptions').doc(userId).update(subscriptionData);
      console.log('✅ Documento de suscripción actualizado:', userId);
    } catch (error) {
      console.error('❌ Error al actualizar documento de suscripción:', error);
    }

    // Actualizar el mapeo si existe
    try {
      const mappingQuery = await db.collection('subscription_mappings')
        .where('providerSubscriptionId', '==', subscriptionId.toString())
        .where('provider', '==', 'reveniu')
        .limit(1)
        .get();

      if (!mappingQuery.empty) {
        const mappingDoc = mappingQuery.docs[0];
        const mappingData: Record<string, any> = {
          deactivatedAt: new Date(),
          finalAccessDate: finalAccessDate,
          status: 'active',
          actualStatus: 'deactivated',
          updatedAt: new Date()
        };

        await mappingDoc.ref.update(mappingData);
        console.log('✅ Mapeo actualizado');
      } else {
        console.log('ℹ️ No se encontró mapeo existente para actualizar');
      }
    } catch (error) {
      console.error('❌ Error al actualizar mapeo:', error);
    }

    // Agregar entrada al historial
    try {
      const historyData: Record<string, any> = {
        userId,
        subscriptionId: subscriptionId.toString(),
        action: 'deactivated',
        timestamp: new Date(),
        finalAccessDate: finalAccessDate,
        provider: 'reveniu'
      };

      await db.collection('subscription_history').add(historyData);
      console.log('✅ Historial actualizado');
    } catch (error) {
      console.error('❌ Error al actualizar historial:', error);
    }

    // Programar una tarea para cambiar el estado a "free" después de finalAccessDate
    try {
      await db.collection('scheduled_tasks').add({
        type: 'subscription_to_free',
        userId: userId,
        subscriptionId: subscriptionId.toString(),
        scheduledFor: finalAccessDate,
        provider: 'reveniu',
        createdAt: new Date()
      });
      console.log('✅ Tarea programada para cambiar estado a "free" en:', finalAccessDate);
    } catch (error) {
      console.error('❌ Error al programar tarea:', error);
    }

    console.log('✅✅✅ Procesamiento del webhook de desactivación completado con éxito ✅✅✅');
  } catch (error) {
    console.error('❌ Error en handleSubscriptionDeactivated:', error);
  }
}

// Asegurarnos de que las solicitudes OPTIONS sean manejadas correctamente (para CORS)
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
} 