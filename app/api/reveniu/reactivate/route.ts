import { NextResponse } from 'next/server';
import { db } from '@/lib/firebase-admin';
import { Timestamp } from 'firebase-admin/firestore';

export async function POST(request: Request) {
  try {
    const { subscriptionId, userId } = await request.json();

    if (!subscriptionId) {
      return NextResponse.json(
        { error: 'ID de suscripción requerido' },
        { status: 400 }
      );
    }

    console.log(`Reactivando suscripción ${subscriptionId} para usuario ${userId}`);

    // Usar la API key directamente que sabemos que funciona
    const apiKey = "Q3_irVOLj-h39JoviFydH6GLnZgEkuDVlF0y";
    
    // Verificar si la suscripción está dentro o fuera de su fecha de pago
    let isWithinPaymentPeriod = false;
    let subscriptionEndDate = null;
    
    if (userId) {
      try {
        // Obtener datos del usuario para verificar la fecha de vencimiento
        const userDoc = await db.collection('users').doc(userId).get();
        if (userDoc.exists) {
          const userData = userDoc.data();
          if (userData) {
            subscriptionEndDate = userData.subscriptionEndDate;
            
            if (subscriptionEndDate) {
              // Convertir a Date si es un Timestamp
              const endDate = subscriptionEndDate instanceof Timestamp ? 
                subscriptionEndDate.toDate() : 
                (subscriptionEndDate instanceof Date ? 
                  subscriptionEndDate : 
                  new Date(subscriptionEndDate));
                  
              // Verificar si la fecha actual es anterior a la fecha de vencimiento
              isWithinPaymentPeriod = new Date() < endDate;
              console.log(`Fecha de vencimiento: ${endDate.toISOString()}, ¿Dentro del período de pago? ${isWithinPaymentPeriod}`);
            }
          }
        }
      } catch (error) {
        console.error('Error al obtener datos del usuario:', error);
      }
    }
    
    let url;
    let requestBody;
    let isExtendMethod = false;
    
    if (isWithinPaymentPeriod) {
      // Si está dentro del período de pago, usar la API de extend
      url = 'https://production.reveniu.com/api/v1/subscriptions/extend/';
      
      // Formatear la fecha de vencimiento como DD/MM/YYYY para fallback_due_date
      let fallbackDueDate = '';
      if (subscriptionEndDate) {
        const endDate = new Date(subscriptionEndDate);
        // Formato DD/MM/YYYY
        fallbackDueDate = `${String(endDate.getDate()).padStart(2, '0')}/${String(endDate.getMonth() + 1).padStart(2, '0')}/${endDate.getFullYear()}`;
        console.log(`Fecha de vencimiento formateada para fallback_due_date: ${fallbackDueDate}`);
      }
      
      // Definir el tipo del cuerpo de la solicitud para incluir fallback_due_date
      interface ExtendRequestBody {
        subs: number[];
        cycles: number;
        auto_renew: boolean;
        fallback_due_date?: string;
      }
      
      requestBody = {
        subs: [parseInt(subscriptionId)], // Convertir a número
        cycles: 1, // Corregido de 'cicles' a 'cycles'
        auto_renew: true
      } as ExtendRequestBody;
      
      // Agregar fallback_due_date solo si tenemos una fecha válida
      if (fallbackDueDate) {
        (requestBody as ExtendRequestBody).fallback_due_date = fallbackDueDate;
      }
      
      isExtendMethod = true;
      console.log('Usando API de extend para reactivar suscripción dentro del período de pago');
    } else {
      // Si está fuera del período de pago, usar la API de cambio de método de pago
      url = 'https://production.reveniu.com/api/v1/subscriptions/change_method/';
      requestBody = {
        subs: [subscriptionId]
      };
      console.log('Usando API de cambio de método de pago para suscripción fuera del período de pago');
    }
    
    console.log('URL de la API:', url);
    
    // Enviar la solicitud a la API correspondiente
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Reveniu-Secret-Key': apiKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    // Leer la respuesta como texto primero para poder diagnosticar
    const responseText = await response.text();
    console.log('Respuesta de Reveniu (texto):', responseText);

    let responseData;
    try {
      responseData = JSON.parse(responseText);
    } catch (error) {
      console.error('Error al parsear la respuesta JSON:', error);
      responseData = { error: 'No se pudo parsear la respuesta del servidor' };
    }

    if (!response.ok) {
      console.error('Error response from Reveniu:', responseData);

      // Si la suscripción no existe (404), limpiar el estado local
      if (response.status === 404) {
        console.log('🧹 Suscripción no encontrada en Reveniu, limpiando estado local...');

        if (userId) {
          try {
            const userRef = db.collection('users').doc(userId);
            await userRef.update({
              subscriptionStatus: 'inactive',
              subscriptionSuspended: false,
              subscriptionCancelled: false,
              subscriptionAutoRenew: false,
              suspendedAt: null,
              suspendedReason: null,
              finalAccessDate: null,
              subscriptionEndDate: null,
              paymentProvider: null,
              subscriptionId: null,
              reveniuSubscriptionId: null,
              updatedAt: new Date()
            });

            console.log('✅ Estado de usuario limpiado - suscripción marcada como inactiva');

            return NextResponse.json({
              success: true,
              message: 'La suscripción no existe en Reveniu. Se ha limpiado el estado local. Puedes crear una nueva suscripción.',
              cleaned: true
            });
          } catch (cleanupError) {
            console.error('❌ Error limpiando estado:', cleanupError);
          }
        }

        return NextResponse.json(
          {
            error: 'La suscripción no existe en Reveniu. Por favor, crea una nueva suscripción.',
            notFound: true
          },
          { status: 404 }
        );
      }

      return NextResponse.json(
        {
          error: responseData.detail || responseData.error || 'Error al reactivar la suscripción',
          responseText: responseText
        },
        { status: response.status }
      );
    }

    console.log('Reveniu reactivate response:', responseData);

    // Actualizar Firestore según el método utilizado
    if (userId) {
      try {
        const userRef = db.collection('users').doc(userId);
        
        if (isExtendMethod) {
          // Verificar si la extensión falló
          if (responseData.failed_changes && responseData.failed_changes.includes(parseInt(subscriptionId))) {
            console.error(`Error al extender la suscripción ${subscriptionId} con Reveniu. ID encontrado en failed_changes.`);
            // No actualizamos Firestore a 'active'
            // Solo registramos el intento fallido en el historial
            try {
              if (subscriptionId && typeof subscriptionId === 'string' && subscriptionId.trim() !== '') {
                const historyRef = db.collection('subscription_history').doc(`${userId}_${Date.now()}`);
                await historyRef.set({
                  userId,
                  subscriptionId,
                  action: 'subscription_extend_failed',
                  errorDetails: responseData,
                  timestamp: new Date(),
                  provider: 'reveniu'
                });
                console.log('Historial de intento fallido de extensión de suscripción registrado');
              }
            } catch (historyError) {
              console.error('Error registrando historial de intento fallido:', historyError);
            }
            return NextResponse.json(
              {
                success: false,
                error: 'Reveniu no pudo reactivar la suscripción. Por favor, intenta actualizar tu método de pago o contacta a soporte.',
                method: 'extend_failed',
                data: responseData
              },
              { status: 400 } // Bad Request, ya que la solicitud a Reveniu falló
            );
          }

          // Si la extensión fue exitosa (o no hay `failed_changes` o el ID no está ahí), actualizamos Firestore
          await userRef.update({
            subscriptionStatus: 'active',
            subscriptionSuspended: false,
            subscriptionAutoRenew: true,
            suspendedAt: null,
            suspendedReason: null,
            updatedAt: new Date(),
            reactivatedAt: new Date(),
            reactivationMethod: 'extend'
          });
          console.log(`Suscripción reactivada exitosamente para usuario ${userId} usando método extend`);
        } else {
          // Si usamos la API de cambio de método de pago, solo registramos la solicitud
          await userRef.update({
            paymentMethodUpdateRequested: true,
            paymentMethodUpdateRequestedAt: new Date(),
            updatedAt: new Date(),
            reactivationMethod: 'change_method'
          });
          console.log(`Solicitud de actualización de método de pago registrada para usuario ${userId}`);
        }

        // Registrar en el historial la acción realizada
        try {
          if (subscriptionId && typeof subscriptionId === 'string' && subscriptionId.trim() !== '') {
            const historyRef = db.collection('subscription_history').doc(`${userId}_${Date.now()}`);
            await historyRef.set({
              userId,
              subscriptionId,
              action: isExtendMethod ? 'subscription_extended' : 'payment_method_update_requested',
              timestamp: new Date(),
              provider: 'reveniu'
            });
            console.log(`Historial de ${isExtendMethod ? 'extensión de suscripción' : 'solicitud de actualización de método de pago'} registrado`);
          } else {
            console.error('ID de suscripción inválido para registrar en historial:', subscriptionId);
          }
        } catch (historyError) {
          console.error('Error registrando historial:', historyError);
        }
      } catch (error) {
        console.error('Error actualizando estado en Firestore:', error);
        // No interrumpimos el flujo si hay un error al actualizar Firestore
      }
    }

    // Devolver la respuesta de éxito según el método utilizado
    return NextResponse.json({
      success: true,
      method: isExtendMethod ? 'extend' : 'change_method',
      message: isExtendMethod 
        ? 'Suscripción reactivada exitosamente' 
        : 'Se ha enviado un correo al cliente con un enlace para actualizar su método de pago',
      data: responseData
    });
  } catch (error: any) {
    console.error('Error reactivando suscripción:', error);
    return NextResponse.json(
      { error: error.message || 'Error interno del servidor' },
      { status: 500 }
    );
  }
}