import { NextResponse } from 'next/server';
import { db } from '@/lib/firebase-admin';

export async function POST(request: Request) {
  try {
    const { subscriptionId, userId, reason } = await request.json();

    if (!subscriptionId) {
      return NextResponse.json(
        { error: 'ID de suscripción requerido' },
        { status: 400 }
      );
    }

    console.log(`🔄 Dando de baja la suscripción ${subscriptionId} para usuario ${userId}`);
    console.log(`📝 Razón: ${reason}`);

    // Usar la API key directamente que sabemos que funciona
    const apiKey = "Q3_irVOLj-h39JoviFydH6GLnZgEkuDVlF0y";
    // Forzar el uso de la URL de producción
    const baseUrl = 'https://production.reveniu.com';

    // URL para dar de baja la suscripción por completo
    const url = `${baseUrl}/api/v1/subscriptions/${subscriptionId}/disable/`;
    console.log('🌐 URL para dar de baja:', url);

    // Dar de baja la suscripción en Reveniu (desactivar por completo)
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Reveniu-Secret-Key': apiKey,
        'Content-Type': 'application/json',
      }
    });

    // Leer la respuesta como texto primero para poder diagnosticar
    const responseText = await response.text();
    console.log('📡 Respuesta de Reveniu (texto):', responseText);

    let responseData;
    try {
      responseData = JSON.parse(responseText);
    } catch (error) {
      console.error('❌ Error al parsear la respuesta JSON:', error);
      responseData = { error: 'No se pudo parsear la respuesta del servidor' };
    }

    if (!response.ok) {
      console.error('❌ Error response from Reveniu:', responseData);
      return NextResponse.json(
        {
          error: responseData.detail || responseData.error || 'Error al dar de baja la suscripción',
          responseText: responseText
        },
        { status: response.status }
      );
    }

    console.log('✅ Reveniu disable response:', responseData);

    // Obtener los detalles de la suscripción para conocer la fecha final de acceso
    let subscriptionDetails;
    let finalAccessDate = null;

    try {
      // URL para obtener los detalles de la suscripción
      const detailsUrl = `${baseUrl}/api/v1/subscriptions/${subscriptionId}/`;

      const detailsResponse = await fetch(detailsUrl, {
        method: 'GET',
        headers: {
          'Reveniu-Secret-Key': apiKey,
          'Content-Type': 'application/json',
        }
      });

      if (detailsResponse.ok) {
        subscriptionDetails = await detailsResponse.json();
        console.log('📊 Detalles de suscripción:', subscriptionDetails);

        // Obtener la fecha del próximo pago (next_due) como finalAccessDate
        if (subscriptionDetails.next_due) {
          finalAccessDate = new Date(subscriptionDetails.next_due);
          console.log('📅 Fecha final de acceso:', finalAccessDate);
        } else {
          // Si no hay next_due, calcular una fecha aproximada (30 días desde hoy)
          finalAccessDate = new Date();
          finalAccessDate.setDate(finalAccessDate.getDate() + 30);
          console.log('⚠️ Sin datos de próximo pago, usando fecha estimada:', finalAccessDate);
        }
      } else {
        console.error('❌ Error al obtener detalles de la suscripción:', await detailsResponse.text());
        // Calcular una fecha aproximada si no podemos obtener los detalles
        finalAccessDate = new Date();
        finalAccessDate.setDate(finalAccessDate.getDate() + 30);
      }
    } catch (error) {
      console.error('❌ Error al obtener detalles de suscripción:', error);
      // Establecer una fecha por defecto en caso de error
      finalAccessDate = new Date();
      finalAccessDate.setDate(finalAccessDate.getDate() + 30);
    }

    // Actualizar el estado de la suscripción en Firestore si se proporciona un userId
    if (userId) {
      try {
        const userRef = db.collection('users').doc(userId);

        // Obtener datos actuales del usuario para registrar información histórica
        const userDoc = await userRef.get();
        let userData: Record<string, any> = {};

        if (userDoc.exists) {
          userData = userDoc.data() || {};
        }

        await userRef.update({
          // Mantener el estado como activo pero marcar como dada de baja
          subscriptionStatus: 'active', // Mantener acceso hasta el final del período
          subscriptionSuspended: true, // Indicar que está dada de baja (usamos el mismo campo por compatibilidad)
          suspendedAt: new Date(),
          suspendedReason: reason || 'Baja solicitada por el usuario',
          subscriptionAutoRenew: false, // Desactivar renovación automática
          finalAccessDate: finalAccessDate,
          subscriptionEndDate: finalAccessDate,
          updatedAt: new Date()
        });

        console.log(`✅ Estado de suscripción actualizado para usuario ${userId}`);
        console.log('🔄 Acceso Pro mantenido hasta:', finalAccessDate);

        // También actualizamos el documento de suscripciones si existe
        try {
          const subscriptionRef = db.collection('subscriptions').doc(userId);
          const subscriptionDoc = await subscriptionRef.get();

          if (subscriptionDoc.exists) {
            await subscriptionRef.update({
              status: 'active', // Mantener como activa hasta el final del periodo
              suspended: true, // Indicar que está dada de baja (usamos el mismo campo por compatibilidad)
              suspendedAt: new Date(),
              suspendedReason: reason || 'Baja solicitada por el usuario',
              finalAccessDate: finalAccessDate,
              updatedAt: new Date()
            });

            console.log('✅ Documento de suscripción actualizado');
          }
        } catch (subError) {
          console.error('❌ Error actualizando documento de suscripción:', subError);
        }

        // Registrar en el historial
        try {
          const historyRef = db.collection('subscription_history').doc(`${userId}_${Date.now()}`);
          await historyRef.set({
            userId,
            subscriptionId,
            action: 'disabled', // Cambiamos la acción a 'disabled' para reflejar que se dio de baja
            reason: reason || 'Baja solicitada por el usuario',
            timestamp: new Date(),
            finalAccessDate: finalAccessDate,
            provider: 'reveniu'
          });
        } catch (histError) {
          console.error('❌ Error registrando historial:', histError);
        }

      } catch (error) {
        console.error('❌ Error actualizando estado en Firestore:', error);
        // No interrumpimos el flujo si hay un error al actualizar Firestore
      }
    }

    // Devolver la respuesta de éxito
    return NextResponse.json({
      success: true,
      message: 'Suscripción dada de baja correctamente pero manteniendo acceso hasta el final del período',
      data: responseData,
      finalAccessDate: finalAccessDate
    });
  } catch (error: any) {
    console.error('❌ Error dando de baja la suscripción:', error);
    return NextResponse.json(
      { error: error.message || 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
