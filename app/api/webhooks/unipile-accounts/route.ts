import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase-admin';
import admin from 'firebase-admin';

/**
 * Webhook específico para recibir notificaciones de estado de cuentas de Unipile
 * Este endpoint está diseñado para manejar específicamente mensajes de tipo CREDENTIALS
 * y reiniciar automáticamente las cuentas desconectadas
 */
export async function POST(req: NextRequest) {
  try {
    console.log('🔔 UNIPILE ACCOUNTS WEBHOOK RECIBIDO 🔔');
    console.log('====================================================');

    // Registrar los headers recibidos
    console.log('📋 HEADERS RECIBIDOS:');
    const headersObj: Record<string, string> = {};
    req.headers.forEach((value, key) => {
      headersObj[key] = value;
    });
    console.log(JSON.stringify(headersObj, null, 2));

    // Verificar que el cuerpo de la solicitud sea JSON
    const contentType = req.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      return NextResponse.json(
        { error: 'Content-Type debe ser application/json' },
        { status: 400 }
      );
    }

    // Obtener los datos del webhook
    const webhookData = await req.json();
    console.log('📦 PAYLOAD COMPLETO DEL WEBHOOK:');
    console.log(JSON.stringify(webhookData, null, 2));
    console.log('====================================================');

    // Verificar si es un webhook de AccountStatus
    if (webhookData.AccountStatus) {
      // Procesar notificación de estado de cuenta
      return await handleAccountStatus(webhookData.AccountStatus);
    } else {
      // Si no tiene el formato esperado, simplemente registrar y responder con éxito
      console.log('ℹ️ Webhook recibido con formato no esperado, ignorando');
      return NextResponse.json({
        success: true,
        message: 'Webhook recibido pero no procesado (formato no esperado)'
      });
    }
  } catch (error) {
    console.error('💥 ERROR PROCESANDO WEBHOOK DE UNIPILE ACCOUNTS:');
    console.error(error);
    console.log('====================================================');
    return NextResponse.json(
      {
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * Procesa una notificación de estado de cuenta de Unipile
 * @param accountStatus Datos del webhook con el estado de la cuenta
 */
async function handleAccountStatus(accountStatus: any) {
  try {
    console.log('🔄 PROCESANDO ESTADO DE CUENTA DE UNIPILE');

    // Extraer datos relevantes del webhook
    const { account_id, account_type, message } = accountStatus;

    if (!account_id) {
      console.error('❌ No se recibió un ID de cuenta válido');
      return NextResponse.json(
        { error: 'No se recibió un ID de cuenta válido' },
        { status: 400 }
      );
    }

    console.log(`📱 Cuenta: ${account_id} (${account_type || 'Tipo desconocido'})`);
    console.log(`📝 Mensaje: ${message}`);

    // Obtener la clave API de Unipile desde las variables de entorno
    const apiKey = process.env.UNIPILE_API_KEY || process.env.NEXT_PUBLIC_UNIPILE_API_KEY;
    const apiUrl = process.env.UNIPILE_API_URL || process.env.NEXT_PUBLIC_UNIPILE_API_URL || 'https://api10.unipile.com:14039';

    if (!apiKey) {
      console.error('❌ No se encontró la clave API de Unipile en las variables de entorno');
      return NextResponse.json(
        { error: 'Configuración de API incompleta' },
        { status: 500 }
      );
    }

    // Buscar a qué usuario pertenece esta cuenta
    const connectionsRef = db.collection('unipileConnections');
    const connectionsQuery = await connectionsRef
      .where('accountId', '==', account_id)
      .get();

    let userId: string | null = null;
    let connectionDocRef: any = null;

    if (!connectionsQuery.empty) {
      const connectionDoc = connectionsQuery.docs[0];
      const connectionData = connectionDoc.data();
      userId = connectionData.userId;
      connectionDocRef = connectionDoc.ref;
      console.log(`👤 Usuario asociado encontrado: ${userId}`);
    } else {
      console.log(`⚠️ No se encontró un usuario asociado a la cuenta ${account_id}`);
    }

    // Manejar diferentes tipos de mensajes
    if (message === 'CREDENTIALS') {
      console.log(`🔄 Problema de credenciales en cuenta ${account_id}`);

      if (userId && connectionDocRef) {
        try {
          // Marcar la cuenta como en error en Firestore
          await connectionDocRef.update({
            status: 'error',
            errorMessage: 'Problema con las credenciales de la cuenta, requiere reconexión',
            active: false, // Marcar como inactiva
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
          });

          // Registrar el evento en Firestore para historial
          await db.collection('unipileEvents').add({
            userId,
            accountId: account_id,
            platform: account_type?.toLowerCase() || 'unknown',
            event: 'account_credentials_error',
            message: 'Problema con las credenciales de la cuenta, requiere reconexión',
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
            rawData: accountStatus
          });

          // Crear notificación para el usuario
          await db.collection('notifications').add({
            userId,
            type: 'account_error',
            title: 'Problema con tu cuenta conectada',
            message: `Tu cuenta de ${account_type || 'mensajería'} necesita ser reconectada debido a un problema con las credenciales.`,
            read: false,
            actionUrl: '/dashboard/settings/connections',
            actionText: 'Reconectar cuenta',
            createdAt: admin.firestore.FieldValue.serverTimestamp()
          });

          console.log(`✅ Estado de error de credenciales registrado para la cuenta ${account_id} del usuario ${userId}`);
        } catch (updateError) {
          console.error('❌ Error al actualizar estado de error en Firestore:', updateError);
        }
      }

      return NextResponse.json({
        success: true,
        message: 'Estado de error de credenciales registrado correctamente'
      });
    } else if (message === 'ERROR') {
      console.log(`❌ Cuenta ${account_id} en estado de ERROR`);

      if (userId && connectionDocRef) {
        try {
          // Marcar la cuenta como en error en Firestore
          await connectionDocRef.update({
            status: 'error',
            errorMessage: 'Cuenta en estado de error, requiere reconexión',
            active: false, // Marcar como inactiva
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
          });

          // Registrar el evento en Firestore para historial
          await db.collection('unipileEvents').add({
            userId,
            accountId: account_id,
            platform: account_type?.toLowerCase() || 'unknown',
            event: 'account_error',
            message: 'Cuenta en estado de error, requiere reconexión',
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
            rawData: accountStatus
          });

          // Crear notificación para el usuario
          await db.collection('notifications').add({
            userId,
            type: 'account_error',
            title: 'Problema con tu cuenta conectada',
            message: `Tu cuenta de ${account_type || 'mensajería'} necesita ser reconectada debido a un error.`,
            read: false,
            actionUrl: '/dashboard/settings/connections',
            actionText: 'Reconectar cuenta',
            createdAt: admin.firestore.FieldValue.serverTimestamp()
          });

          console.log(`✅ Estado de error registrado para la cuenta ${account_id} del usuario ${userId}`);
        } catch (updateError) {
          console.error('❌ Error al actualizar estado de error en Firestore:', updateError);
        }
      }

      return NextResponse.json({
        success: true,
        message: 'Estado de error registrado correctamente'
      });
    } else if (message === 'RECONNECTED') {
      console.log(`✅ Cuenta ${account_id} reconectada exitosamente`);

      if (userId && connectionDocRef) {
        try {
          // Marcar la cuenta como reconectada y activa
          await connectionDocRef.update({
            status: 'connected',
            errorMessage: null, // Limpiar mensaje de error
            active: true, // Marcar como activa
            connected: true, // Asegurar que esté marcada como conectada
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
          });

          // Registrar el evento en Firestore para historial
          await db.collection('unipileEvents').add({
            userId,
            accountId: account_id,
            platform: account_type?.toLowerCase() || 'unknown',
            event: 'account_reconnected',
            message: 'Cuenta reconectada exitosamente',
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
            rawData: accountStatus
          });

          console.log(`✅ Estado de reconexión registrado para la cuenta ${account_id} del usuario ${userId}`);
        } catch (updateError) {
          console.error('❌ Error al actualizar estado de reconexión en Firestore:', updateError);
        }
      }

      return NextResponse.json({
        success: true,
        message: 'Reconexión registrada correctamente'
      });
    } else if (message === 'OK') {
      console.log(`✅ Cuenta ${account_id} conectada exitosamente (OK)`);

      if (userId && connectionDocRef) {
        try {
          // Para cuentas de Instagram, obtener el provider_messaging_id
          let updateData: any = {
            status: 'connected',
            errorMessage: null, // Limpiar mensaje de error
            active: true, // Marcar como activa
            connected: true, // Asegurar que esté marcada como conectada
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
          };
          
          // Si es una cuenta de Instagram, obtener el provider_messaging_id
          if (account_type?.toLowerCase() === 'instagram') {
            try {
              console.log(`🔍 Obteniendo provider_messaging_id para la cuenta de Instagram ${account_id}`);
              
              const userResponse = await fetch(
                `${apiUrl}/api/v1/users/me?account_id=${account_id}`,
                {
                  method: 'GET',
                  headers: {
                    'X-API-KEY': apiKey,
                    'accept': 'application/json'
                  }
                }
              );
              
              if (userResponse.ok) {
                const userData = await userResponse.json();
                console.log(`✅ Información del usuario obtenida:`, JSON.stringify(userData, null, 2));
                
                if (userData.provider_messaging_id) {
                  updateData.provider_messaging_id = userData.provider_messaging_id;
                  console.log(`✅ provider_messaging_id obtenido: ${userData.provider_messaging_id}`);
                } else {
                  console.warn(`⚠️ No se encontró provider_messaging_id en la respuesta de la API`);
                }
              } else {
                console.error(`❌ Error al obtener información del usuario: ${userResponse.statusText}`);
              }
            } catch (apiError) {
              console.error(`❌ Error al obtener provider_messaging_id:`, apiError);
            }
          }
          
          // Actualizar la cuenta con la información obtenida
          await connectionDocRef.update(updateData);

          // Registrar el evento en Firestore para historial
          await db.collection('unipileEvents').add({
            userId,
            accountId: account_id,
            platform: account_type?.toLowerCase() || 'unknown',
            event: 'account_connected',
            message: 'Cuenta conectada exitosamente',
            timestamp: admin.firestore.FieldValue.serverTimestamp(),
            rawData: accountStatus
          });

          console.log(`✅ Estado de conexión exitosa registrado para la cuenta ${account_id} del usuario ${userId}`);
        } catch (updateError) {
          console.error('❌ Error al actualizar estado de conexión en Firestore:', updateError);
        }
      }

      return NextResponse.json({
        success: true,
        message: 'Conexión exitosa registrada correctamente'
      });
    } else {
      // Si el mensaje no es ninguno de los esperados, simplemente registrar el evento
      console.log(`ℹ️ Mensaje de estado de cuenta recibido: ${message} (no requiere acción específica)`);

      if (userId) {
        // Registrar el evento en Firestore para historial
        await db.collection('unipileEvents').add({
          userId,
          accountId: account_id,
          platform: account_type?.toLowerCase() || 'unknown',
          event: 'account_status_update',
          message: `Estado de cuenta actualizado: ${message}`,
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
          rawData: accountStatus
        });
      }

      return NextResponse.json({
        success: true,
        message: 'Estado de cuenta registrado (no requiere acción)'
      });
    }
  } catch (error) {
    console.error('❌ ERROR PROCESANDO ESTADO DE CUENTA:');
    console.error(error);
    console.log('====================================================');
    return NextResponse.json(
      {
        error: 'Error procesando estado de cuenta',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

// Manejar OPTIONS para CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
}
