import { database as rtdb } from '@/lib/firebase-admin';
import { v2 as cloudinary } from 'cloudinary';
import axios from 'axios';

// Configurar Cloudinary
cloudinary.config({
  cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || 'tatu-ink',
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
  secure: true
});

/**
 * Sube una imagen a Cloudinary
 * @param imageUrl URL de la imagen a subir
 * @param publicId ID público para la imagen en Cloudinary
 * @returns URL de la imagen en Cloudinary o null si hay error
 */
async function uploadImageToCloudinary(imageUrl: string, publicId: string, debugId: string = 'unknown'): Promise<string | null> {
  try {
    console.log(`[${debugId}] 🖼️ Iniciando subida a Cloudinary para imagen: ${imageUrl}`);
    console.log(`[${debugId}] 🆔 Public ID para Cloudinary: ${publicId}`);
    
    // Verificar que tenemos las variables de entorno necesarias
    const cloudName = process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME;
    const apiKey = process.env.CLOUDINARY_API_KEY;
    const apiSecret = process.env.CLOUDINARY_API_SECRET;
    
    if (!cloudName || !apiKey || !apiSecret) {
      console.error(`[${debugId}] ❌ Faltan variables de entorno para Cloudinary`);
      console.error(`[${debugId}] Cloud name: ${cloudName ? '✅' : '❌'}, API Key: ${apiKey ? '✅' : '❌'}, API Secret: ${apiSecret ? '✅' : '❌'}`);
      return null;
    }
    
    // Configurar Cloudinary
    cloudinary.config({
      cloud_name: cloudName,
      api_key: apiKey,
      api_secret: apiSecret,
      secure: true
    });

    console.log(`[${debugId}] ⏳ Subiendo imagen a Cloudinary...`);
    
    // Subir la imagen a Cloudinary
    const result = await cloudinary.uploader.upload(imageUrl, {
      public_id: publicId,
      overwrite: true,
      resource_type: 'image',
      timeout: 15000 // 15 segundos de timeout
    });

    console.log(`[${debugId}] ✅ Imagen subida exitosamente a Cloudinary: ${result.secure_url}`);
    return result.secure_url;
  } catch (error) {
    console.error(`[${debugId}] ❌ Error al subir imagen a Cloudinary:`, error);
    return null;
  }
}

/**
 * Interfaz para la respuesta de la API de Unipile
 */
interface UnipileAttendeeResponse {
  picture_url?: string;
  name?: string;
  profile_url?: string;
  specifics?: {
    public_identifier?: string;
  };
}

/**
 * Obtiene y guarda la foto de perfil de Instagram para un usuario
 * @param username Nombre de usuario de Instagram
 * @param chatId ID del chat
 * @param groupName Nombre del grupo (opcional)
 * @param attendeeId ID del asistente en Unipile (opcional)
 * @returns URL de la imagen de perfil o null si no se encuentra
 */
export async function fetchAndSaveInstagramProfilePicture(
  username: string, 
  chatId: string, 
  groupName?: string,
  attendeeId?: string
): Promise<string | null> {
  const debugId = Date.now().toString().slice(-6);
  console.log(`🔍 Buscando perfil de Instagram: ${username}`);
  
  try {
    // Verificar si ya hay una foto de perfil reciente (menos de 24h)
    const chatRef = rtdb.ref(`chats/${chatId}`);
    try {
      const chatSnapshot = await chatRef.once('value');
      const chatData = chatSnapshot.val();
      
      if (chatData) {
        const lastUpdate = chatData.profilePictureUpdatedAt || chatData.profilePicUpdatedAt;
        
        if (lastUpdate) {
          const lastUpdateTime = new Date(lastUpdate).getTime();
          const currentTime = Date.now();
          const hoursSinceLastUpdate = (currentTime - lastUpdateTime) / (1000 * 60 * 60);
          
          // Si la imagen se actualizó hace menos de 24h, no volvemos a obtenerla
          if (hoursSinceLastUpdate < 24 && (chatData.profilePictureUrl || chatData.profilePicUrl)) {
            const existingUrl = chatData.profilePictureUrl || chatData.profilePicUrl;
            console.log(`📷 Usando foto de perfil existente (actualizada hace ${hoursSinceLastUpdate.toFixed(2)} horas)`);
            console.log(`📷 URL de la foto existente: ${existingUrl}`);
            console.log(`📷 Ubicación en Firebase: chats/${chatId}/profilePictureUrl (o profilePicUrl)`);
            console.log(`📷 Datos completos de la conversación:`, JSON.stringify(chatData, null, 2));
            return existingUrl;
          }
        }
      }
    } catch (error) {
      console.error(`[${debugId}] ❌ Error al obtener datos de la conversación:`, error);
    }
    
    // Registrar intento en la base de datos
    const debugRef = rtdb.ref(`debug/instagram-profile/${chatId}`);
    await debugRef.set({
      username,
      timestamp: Date.now(),
      debugId,
      // Solo guardar attendeeId si está definido para evitar errores de Firebase
      ...(attendeeId ? { attendeeId } : {})
    });
    
    // Método principal: Obtener la foto directamente de la API de Unipile usando el attendee_id
    if (attendeeId) {
      console.log(`[${debugId}] 🔍 Obteniendo foto de perfil directamente de Unipile con attendee_id: ${attendeeId}`);
      
      try {
        // Intentar obtener la API key de diferentes variables de entorno
        let apiKey = process.env.NEXT_PUBLIC_UNIPILE_API_KEY || process.env.UNIPILE_API_KEY;
        
        // Verificar si tenemos la API key
        if (!apiKey) {
          console.error(`[${debugId}] ❌ API Key de Unipile no configurada`);
          console.error(`[${debugId}] ❌ Variables de entorno disponibles:`, 
            Object.keys(process.env)
              .filter(key => key.includes('UNIPILE') || key.includes('API'))
              .join(', '));
          throw new Error('API Key de Unipile no encontrada en las variables de entorno');
        }
        
        console.log(`[${debugId}] ✅ API Key de Unipile encontrada: ${apiKey.substring(0, 5)}...`);
        
        // Verificar formato de la API key
        if (apiKey.length < 10) {
          console.warn(`[${debugId}] ⚠️ La API key parece demasiado corta: ${apiKey.length} caracteres`);
        }
        
        // Registrar intento con Unipile API
        const unipileLogRef = rtdb.ref(`debug/instagram-profile/${chatId}/unipile`);
        await unipileLogRef.set({
          attemptTimestamp: Date.now(),
          unipileDebugId: debugId,
          // Registrar el attendeeId solo si está definido
          ...(attendeeId ? { attendeeId } : {})
        });
        
        // Preparar la URL y los headers para la API de Unipile
        const unipileUrl = `https://api10.unipile.com:14039/api/v1/chat_attendees/${attendeeId}`;
        const headers = {
          'X-API-KEY': apiKey,
          'accept': 'application/json'
        };
        
        console.log(`[${debugId}] 🔍 Llamando a Unipile API: ${unipileUrl}`);
        console.log(`[${debugId}] 🔑 Headers: X-API-KEY: ${apiKey.substring(0, 5)}...`);
        
        // Llamar a la API de Unipile para obtener los detalles del attendee
        console.log(`[${debugId}] 📡 Iniciando solicitud a Unipile...`);
        const response = await axios.get(unipileUrl, {
          headers,
          timeout: 10000 // 10 segundos de timeout
        });
        
        console.log(`[${debugId}] ✅ Respuesta recibida de Unipile API con status: ${response.status}`);
        
        // Guardar la respuesta completa para depuración
        await unipileLogRef.update({
          responseStatus: response.status,
          responseHeaders: JSON.stringify(response.headers),
          responseTimestamp: Date.now()
        });
        
        // Verificar si tenemos datos en la respuesta
        if (!response.data) {
          console.error(`[${debugId}] ❌ No hay datos en la respuesta de Unipile`);
          await unipileLogRef.update({
            error: 'No data in response',
            success: false
          });
          return null;
        }
        
        console.log(`[${debugId}] 📝 Datos recibidos de Unipile:`, JSON.stringify(response.data, null, 2));
        
        const attendeeData = response.data as UnipileAttendeeResponse;
        await unipileLogRef.update({
          responseData: JSON.stringify(attendeeData, null, 2)
        });
        
        // Verificar si tenemos una URL de imagen
        if (attendeeData && attendeeData.picture_url) {
          console.log(`[${debugId}] ✅ Foto de perfil encontrada en Unipile para ${username}`);
          
          // Registrar éxito con Unipile
          await unipileLogRef.set({
            attemptTimestamp: Date.now(),
            unipileDebugId: debugId,
            success: true,
            profileUrl: attendeeData.picture_url
          });
          
          // Subir la imagen a Cloudinary
          const cloudinaryUrl = await uploadImageToCloudinary(attendeeData.picture_url, `instagram_${username}`, debugId);
          
          if (cloudinaryUrl) {
            // Actualizar la foto de perfil en la base de datos
            // Usamos profilePictureUrl en vez de profilePicUrl para alinearlo con lo que lee ConversationList.tsx
            console.log(`[${debugId}] 💾 Guardando URL de Cloudinary en Firebase:`);
            console.log(`[${debugId}] 💾 URL de Cloudinary: ${cloudinaryUrl}`);
            console.log(`[${debugId}] 💾 Ruta en Firebase: chats/${chatId}/profilePictureUrl`);
            
            await chatRef.update({
              // Guardar con ambos nombres para asegurar compatibilidad
              profilePictureUrl: cloudinaryUrl,
              profilePictureUpdatedAt: new Date().toISOString(),
              // Formato antiguo (el que parece estar funcionando actualmente)
              profilePicUrl: cloudinaryUrl,
              profilePicUpdatedAt: new Date().toISOString()
            });
            
            // Verificar que se guardó correctamente
            const verifySnapshot = await chatRef.once('value');
            const verifyData = verifySnapshot.val();
            console.log(`[${debugId}] ✅ Foto de perfil de ${username} actualizada con éxito desde Unipile`);
            console.log(`[${debugId}] ✅ Datos actualizados en Firebase:`, JSON.stringify({
              // Formato nuevo
              profilePictureUrl: verifyData.profilePictureUrl,
              profilePictureUpdatedAt: verifyData.profilePictureUpdatedAt,
              // Formato antiguo
              profilePicUrl: verifyData.profilePicUrl,
              profilePicUpdatedAt: verifyData.profilePicUpdatedAt
            }, null, 2));
            return cloudinaryUrl;
          }
        } else {
          console.log(`[${debugId}] ⚠️ No se encontró URL de imagen en la respuesta de Unipile`);
          
          // Registrar fallo con Unipile
          await unipileLogRef.set({
            attemptTimestamp: Date.now(),
            unipileDebugId: debugId,
            success: false,
            error: 'No picture_url found in response'
          });
        }
      } catch (error) {
        console.error(`[${debugId}] ❌ Error al obtener perfil con Unipile API:`, error);
        
        // Extraer más detalles del error
        let errorDetails = {};
        
        if (axios.isAxiosError(error)) {
          errorDetails = {
            isAxiosError: true,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            config: {
              url: error.config?.url,
              method: error.config?.method,
              timeout: error.config?.timeout,
              headers: error.config?.headers ? JSON.stringify(error.config.headers) : null
            }
          };
          console.error(`[${debugId}] ❌ Detalles del error de Axios:`, JSON.stringify(errorDetails, null, 2));
        }
        
        // Registrar error con Unipile
        const unipileLogRef = rtdb.ref(`debug/instagram-profile/${chatId}/unipile`);
        await unipileLogRef.set({
          attemptTimestamp: Date.now(),
          unipileDebugId: debugId,
          error: (error as Error).message,
          stack: (error as Error).stack,
          errorDetails: JSON.stringify(errorDetails),
          // Registrar el attendeeId solo si está definido
          ...(attendeeId ? { attendeeId } : {})
        });
      }
    } else {
      console.log(`[${debugId}] ⚠️ No se proporcionó attendee_id para obtener la foto de perfil directamente`);
    }
    
    // Si llegamos aquí, no pudimos obtener la foto de perfil
    console.log(`[${debugId}] ❌ No se pudo obtener la foto de perfil para ${username}`);
    return null;
  } catch (error) {
    console.error(`❌ Error general al obtener/guardar foto de perfil:`, error);
    return null;
  }
}
