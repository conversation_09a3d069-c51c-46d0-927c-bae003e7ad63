import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'TatuApp - Gestión de Estudio de Tatuajes',
  description: 'La plataforma más completa para gestionar tu estudio de tatuajes. Agenda citas, administra clientes, gestiona pagos y más.',
  keywords: 'tatuajes, estudio de tatuajes, gestión de tatuajes, agenda de tatuajes, tatuadores, tatu, tatulat',
  authors: [{ name: '<PERSON><PERSON><PERSON><PERSON>' }],
  creator: '<PERSON><PERSON><PERSON><PERSON>',
  publisher: 'TatuApp',
  openGraph: {
    title: 'TatuApp - Gestión de Estudio de Tatuajes',
    description: 'La plataforma más completa para gestionar tu estudio de tatuajes. Agenda citas, administra clientes, gestiona pagos y más.',
    url: 'https://tatu.ink',
    siteName: 'TatuApp',
    locale: 'es_ES',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'TatuApp - Gestión de Estudio de Tatuajes',
    description: 'La plataforma más completa para gestionar tu estudio de tatuajes. Agenda citas, administra clientes, gestiona pagos y más.',
  },
  alternates: {
    canonical: 'https://tatu.ink',
  },
  metadataBase: new URL('https://tatu.ink'),
}
