'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { useAuth } from './AuthContext';

interface ProfileContextType {
  profile: any;
  loading: boolean;
  error: string | null;
  refreshProfile: () => Promise<void>;
  updateProfile: (updatedProfile: any) => Promise<boolean>;
}

const ProfileContext = createContext<ProfileContextType | undefined>(undefined);

export function ProfileProvider({ children }: { children: React.ReactNode }) {
  const { user, authCheckComplete } = useAuth();
  const [profile, setProfile] = useState<any>({ profile: { contact: {} } });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [updating, setUpdating] = useState(false);

  const loadProfile = async (uid: string) => {
    try {
      setLoading(true);
      console.log('📂 ProfileContext - Loading profile for user:', uid);
      const userRef = doc(db, 'users', uid);
      const userDoc = await getDoc(userRef);
      
      if (userDoc.exists()) {
        setProfile({ id: userDoc.id, ...userDoc.data() });
        console.log('✅ ProfileContext - Profile loaded successfully');
      } else {
        setError('Perfil no encontrado');
        console.warn('⚠️ ProfileContext - Profile not found');
      }
    } catch (err) {
      console.error('❌ ProfileContext - Error loading profile:', err);
      setError('Error al cargar el perfil');
    } finally {
      setLoading(false);
    }
  };

  const refreshProfile = async () => {
    if (user?.uid) {
      await loadProfile(user.uid);
    }
  };

  useEffect(() => {
    const fetchProfile = async () => {
      if (user) {
        setLoading(true);
        try {
          // Corregido: leer de 'users' en lugar de 'profiles'
          const profileDoc = await getDoc(doc(db, "users", user.uid));
          if (profileDoc.exists()) {
            setProfile(profileDoc.data());
            console.log('✅ ProfileContext - Profile fetched successfully from users collection');
          } else {
            // Inicializar perfil vacío
            setProfile({ profile: { contact: {} } });
            console.log('⚠️ ProfileContext - No profile found in users collection, initialized empty profile');
          }
        } catch (error) {
          console.error("❌ Error fetching profile:", error);
        } finally {
          setLoading(false);
        }
      } else {
        setProfile({ profile: { contact: {} } });
        setLoading(false);
      }
    };

    fetchProfile();
  }, [user]);

  useEffect(() => {
    // Solo intentar cargar el perfil cuando sepamos con certeza si hay un usuario o no
    if (!authCheckComplete) {
      console.log('⏳ ProfileContext - Waiting for auth check to complete');
      return;
    }
    
    if (user?.uid) {
      console.log('🔍 ProfileContext - Auth check complete, user found, loading profile');
      loadProfile(user.uid);
    } else {
      console.log('ℹ️ ProfileContext - Auth check complete, no user found');
      setProfile(null);
    }
  }, [user?.uid, authCheckComplete]);

  // Renderizar un indicador de carga si la autenticación aún no está completa
  if (!authCheckComplete) {
    return null; // No renderizar nada hasta que sepamos si hay usuario o no
  }

  return (
    <ProfileContext.Provider
      value={{
        profile,
        loading,
        error,
        refreshProfile,
        updateProfile: async (updatedProfile: any) => {
          try {
            // Evitar que se actualice el estado de loading mientras se guarda el perfil
            // ya que esto puede causar problemas de renderizado
            setUpdating(true);
            
            if (!user?.uid) {
              throw new Error('Usuario no autenticado');
            }
            
            // Crear una copia segura del perfil para evitar mutaciones
            const safeProfile = JSON.parse(JSON.stringify(updatedProfile));
            
            // Asegurar que la estructura básica existe
            if (!safeProfile.profile) {
              safeProfile.profile = {};
            }
            if (!safeProfile.profile.contact) {
              safeProfile.profile.contact = {};
            }
            
            // Corregido: guardar en 'users' en lugar de 'profiles'
            const userRef = doc(db, 'users', user.uid);
            await setDoc(userRef, safeProfile, { merge: true });
            
            // Actualizar el estado local con la copia segura
            setProfile(safeProfile);
            console.log('✅ ProfileContext - Profile updated successfully (saved to users collection)');
            return true; // Indicar éxito
          } catch (err) {
            console.error('❌ ProfileContext - Error updating profile:', err);
            setError('Error al actualizar el perfil');
            throw err; // Re-lanzar error para manejo en componentes
          } finally {
            setUpdating(false);
          }
        }
      }}
    >
      {children}
    </ProfileContext.Provider>
  );
}

export function useProfile() {
  const context = useContext(ProfileContext);
  if (context === undefined) {
    throw new Error('useProfile must be used within a ProfileProvider');
  }
  return context;
}