import { database as rtdb } from '@/lib/firebase/config';
import { ref, update, get } from 'firebase/database';

/**
 * Sincroniza las imágenes de perfil dentro de RTDB
 * @param userId ID del usuario (tatuador)
 * @returns Promesa que se resuelve cuando se completa la sincronización
 */
export async function syncProfileImages(userId: string): Promise<void> {
  try {
    console.log('🔄 Iniciando sincronización de imágenes de perfil para usuario:', userId);
    
    // PASO 1: Buscar imágenes en la ruta 'chats' y copiarlas a 'conversations'
    try {
      console.log('🔍 Buscando imágenes en la ruta chats/...');
      const chatsRef = ref(rtdb, 'chats');
      const chatsSnapshot = await get(chatsRef);
      
      if (chatsSnapshot.exists()) {
        const chats = chatsSnapshot.val();
        console.log(`🔍 Se encontraron ${Object.keys(chats).length} chats`);
        
        // Revisar cada chat buscando imágenes de perfil
        let imageCounter = 0;
        for (const [chatId, chatData] of Object.entries(chats)) {
          const chat = chatData as any;
          
          // Si este chat tiene una imagen de perfil
          if (chat.profilePicUrl || chat.profilePictureUrl) {
            const imageUrl = chat.profilePicUrl || chat.profilePictureUrl;
            console.log(`✅ Imagen encontrada en chat ${chatId}: ${imageUrl}`);
            
            // Buscar la conversación correspondiente
            const conversationsRef = ref(rtdb, `conversations/${userId}`);
            const conversationsSnapshot = await get(conversationsRef);
            
            if (conversationsSnapshot.exists()) {
              const conversations = conversationsSnapshot.val();
              
              // Buscar la conversación que coincida con este chatId
              const matchingConvId = Object.keys(conversations).find(convId => 
                convId === chatId || 
                (conversations[convId].attendeeId && chat.attendeeId && 
                 conversations[convId].attendeeId === chat.attendeeId)
              );
              
              if (matchingConvId) {
                console.log(`✅ Encontrada conversación coincidente: ${matchingConvId}`);
                
                // Actualizar la conversación con la imagen
                const convRef = ref(rtdb, `conversations/${userId}/${matchingConvId}`);
                await update(convRef, {
                  profilePictureUrl: imageUrl,
                  profilePictureUpdatedAt: chat.profilePictureUpdatedAt || new Date().toISOString(),
                  profilePicUrl: imageUrl,
                  profilePicUpdatedAt: chat.profilePicUpdatedAt || new Date().toISOString(),
                  participantPicture: imageUrl
                });
                
                console.log(`✅ Imagen copiada de chats a conversations/${userId}/${matchingConvId}`);
                imageCounter++;
              }
            }
          }
        }
        
        console.log(`🔄 Proceso completado. Se copiaron ${imageCounter} imágenes de chats a conversations`);
      } else {
        console.log('ℹ️ No se encontraron chats en la base de datos');
      }
    } catch (chatError: any) {
      console.error('❌ Error al acceder a los chats:', chatError.message);
      // Continuamos con el resto del proceso
    }
    
    // PASO 2: Continuar con el proceso normal
    // Verificamos si podemos acceder a las conversaciones
    const conversationsRef = ref(rtdb, `conversations/${userId}`);
    let conversationsSnapshot;
    
    try {
      conversationsSnapshot = await get(conversationsRef);
      
      if (!conversationsSnapshot.exists()) {
        console.log('ℹ️ No se encontraron conversaciones para este usuario');
        return;
      }
    } catch (error: any) {
      console.error('❌ Error al acceder a las conversaciones:', error.message);
      return;
    }
    
    // Intentamos acceder a profileUpdates, pero manejamos el caso en que no tengamos permisos
    let profileUpdates: Record<string, any> = {};
    let hasProfileUpdates = false;
    
    try {
      // Obtener todas las actualizaciones de imágenes de perfil de RTDB
      const profileUpdatesRef = ref(rtdb, `profileUpdates/${userId}`);
      const profileUpdatesSnapshot = await get(profileUpdatesRef);
      
      if (profileUpdatesSnapshot.exists()) {
        profileUpdates = profileUpdatesSnapshot.val();
        hasProfileUpdates = true;
        console.log(`✅ Se encontraron ${Object.keys(profileUpdates).length} actualizaciones de imágenes de perfil`);
      } else {
        console.log('ℹ️ No se encontraron actualizaciones de imágenes de perfil');
      }
    } catch (error: any) {
      console.log('⚠️ No se pudo acceder a profileUpdates, posible error de permisos:', error.message);
      // Continuamos con la sincronización usando solo los datos disponibles en las conversaciones
    }
    
    // Si no tenemos actualizaciones de imágenes, no hay nada que sincronizar
    if (!hasProfileUpdates) {
      console.log('ℹ️ No hay actualizaciones de imágenes para sincronizar');
      return;
    }
    
    // Crear un mapa para almacenar la última URL de imagen para cada attendeeProviderId y attendeeId
    const attendeeProviderImageMap = new Map<string, string>();
    const attendeeImageMap = new Map<string, string>();
    
    // Procesar las actualizaciones y quedarse con la más reciente para cada attendeeProviderId o attendeeId
    Object.entries(profileUpdates).forEach(([key, value]) => {
      const updateData = value as any;
      const { attendeeId, attendeeProviderId, imageUrl } = updateData;
      
      // Priorizar attendeeProviderId si está disponible
      if (attendeeProviderId && imageUrl && !attendeeProviderImageMap.has(attendeeProviderId)) {
        attendeeProviderImageMap.set(attendeeProviderId, imageUrl);
      }
      
      // También guardar por attendeeId para compatibilidad con datos antiguos
      if (attendeeId && imageUrl && !attendeeImageMap.has(attendeeId)) {
        attendeeImageMap.set(attendeeId, imageUrl);
      }
    });
    
    console.log(`ℹ️ Se encontraron ${attendeeProviderImageMap.size} attendeeProviderIds y ${attendeeImageMap.size} attendeeIds únicos con imágenes`);
    
    // Obtener las conversaciones
    const conversations = conversationsSnapshot.val();
    const conversationIds = Object.keys(conversations).filter(id => 
      id !== 'unread' && id !== 'unreadCount' && typeof conversations[id] === 'object' && conversations[id] !== null
    );
    
    console.log(`ℹ️ Se encontraron ${conversationIds.length} conversaciones válidas`);
    
    // Verificar si alguna conversación ya tiene profilePicUrl directamente en ella
    // Esto es para manejar el caso donde la imagen se guarda directamente en la conversación
    for (const conversationId of conversationIds) {
      const conversation = conversations[conversationId];
      if (conversation.profilePicUrl && !conversation.profilePictureUrl) {
        console.log(`📷 Encontrada imagen directa en conversación ${conversationId}: ${conversation.profilePicUrl}`);
        // Añadir esta imagen al mapa para que se sincronice correctamente
        if (conversation.attendeeId) {
          attendeeImageMap.set(conversation.attendeeId, conversation.profilePicUrl);
        }
      }
    }
    
    // Actualizar las conversaciones que tengan un attendeeProviderId o attendeeId que coincida
    let updatedCount = 0;
    
    for (const conversationId of conversationIds) {
      const conversation = conversations[conversationId];
      const { attendeeId, participantId } = conversation;
      let imageUrl = null;
      
      // Primero intentamos buscar por participantId (que puede contener el attendeeProviderId)
      if (participantId) {
        // Buscar en todas las claves de attendeeProviderImageMap si alguna coincide parcialmente con participantId
        for (const [providerId, url] of attendeeProviderImageMap.entries()) {
          if (providerId && participantId.includes(providerId)) {
            imageUrl = url;
            console.log(`ℹ️ Coincidencia encontrada por providerId: ${providerId} en conversación ${conversationId}`);
            break;
          }
        }
      }
      
      // Si no encontramos por providerId, intentamos por attendeeId
      if (!imageUrl && attendeeId && attendeeImageMap.has(attendeeId)) {
        imageUrl = attendeeImageMap.get(attendeeId);
        console.log(`ℹ️ Coincidencia encontrada por attendeeId: ${attendeeId} en conversación ${conversationId}`);
      }
      
      // Si encontramos una imagen, actualizamos la conversación
      if (imageUrl) {
        try {
          const conversationRef = ref(rtdb, `conversations/${userId}/${conversationId}`);
          // Asegurarnos de que participant sea un objeto
          const participant = conversation.participant || {};
          
          await update(conversationRef, {
            // Formato nuevo
            profilePictureUrl: imageUrl,
            profilePictureUpdatedAt: new Date().toISOString(),
            // Formato antiguo (el que parece estar funcionando actualmente)
            profilePicUrl: imageUrl,
            profilePicUpdatedAt: new Date().toISOString(),
            // Campos adicionales para compatibilidad
            participantPicture: imageUrl,
            participant: {
              ...participant,
              profilePic: imageUrl
            }
          });
          console.log(`✅ Conversación ${conversationId} actualizada con imagen de perfil`);
          updatedCount++;
        } catch (error: any) {
          console.error(`❌ Error al actualizar la conversación ${conversationId}:`, error.message);
          // Continuamos con la siguiente conversación
        }
      }
    }
    
    console.log(`✅ Se actualizaron ${updatedCount} de ${conversationIds.length} conversaciones con imágenes de perfil`);
  } catch (error: any) {
    console.error('❌ Error al sincronizar imágenes de perfil:', error.message);
    // No relanzamos el error para evitar que la aplicación se detenga
  }
}

/**
 * Sincroniza las imágenes de perfil para una conversación específica
 * @param userId ID del usuario (tatuador)
 * @param conversationId ID de la conversación
 * @returns Promesa que se resuelve cuando se completa la sincronización
 */
export async function syncProfileImageForConversation(
  userId: string,
  conversationId: string
): Promise<void> {
  try {
    console.log(`🔄 Sincronizando imagen de perfil para conversación ${conversationId}`);
    
    // 1. Obtener la conversación de RTDB
    const conversationRef = ref(rtdb, `conversations/${userId}/${conversationId}`);
    let conversationSnapshot;
    
    try {
      conversationSnapshot = await get(conversationRef);
      
      if (!conversationSnapshot.exists()) {
        console.log('ℹ️ La conversación no existe');
        return;
      }
    } catch (error: any) {
      console.error('❌ Error al acceder a la conversación:', error.message);
      return;
    }
    
    const conversation = conversationSnapshot.val();
    const { attendeeId } = conversation;
    
    if (!attendeeId) {
      console.log('ℹ️ La conversación no tiene attendeeId');
      return;
    }
    
    // 2. Buscar la imagen de perfil en RTDB
    // Primero intentamos obtener el participantId (que puede contener el attendeeProviderId)
    const participantId = conversation.participantId;
    let imageUrl = null;
    
    try {
      // Si tenemos participantId, intentamos buscar por attendeeProviderId
      if (participantId) {
        // Obtener todas las actualizaciones de imágenes de perfil
        const profileUpdatesRef = ref(rtdb, `profileUpdates/${userId}`);
        const profileUpdatesSnapshot = await get(profileUpdatesRef);
        
        if (profileUpdatesSnapshot.exists()) {
          const profileUpdates = profileUpdatesSnapshot.val();
          
          // Buscar coincidencias por attendeeProviderId
          for (const [key, value] of Object.entries(profileUpdates)) {
            const updateData = value as any;
            const { attendeeProviderId, imageUrl: url } = updateData;
            
            if (attendeeProviderId && participantId.includes(attendeeProviderId)) {
              imageUrl = url;
              console.log(`ℹ️ Coincidencia encontrada por providerId: ${attendeeProviderId}`);
              break;
            }
          }
        }
      }
      
      // Si no encontramos por attendeeProviderId, intentamos por attendeeId
      if (!imageUrl) {
        // Buscar directamente por attendeeId en profileUpdates
        const profileUpdateRef = ref(rtdb, `profileUpdates/${userId}/${attendeeId}`);
        const profileUpdateSnapshot = await get(profileUpdateRef);
        
        if (profileUpdateSnapshot.exists()) {
          const updateData = profileUpdateSnapshot.val();
          imageUrl = updateData.imageUrl;
          console.log(`ℹ️ Coincidencia encontrada por attendeeId: ${attendeeId}`);
        } else {
          // Si no encontramos por attendeeId exacto, buscamos en todas las actualizaciones
          const profileUpdatesRef = ref(rtdb, `profileUpdates/${userId}`);
          const profileUpdatesSnapshot = await get(profileUpdatesRef);
          
          if (profileUpdatesSnapshot.exists()) {
            const profileUpdates = profileUpdatesSnapshot.val();
            
            // Buscar coincidencias por attendeeId
            for (const [key, value] of Object.entries(profileUpdates)) {
              const updateData = value as any;
              if (updateData.attendeeId === attendeeId && updateData.imageUrl) {
                imageUrl = updateData.imageUrl;
                console.log(`ℹ️ Coincidencia encontrada por attendeeId en recorrido: ${attendeeId}`);
                break;
              }
            }
          }
        }
      }
    } catch (error: any) {
      console.log('⚠️ Error al acceder a profileUpdates, posible error de permisos:', error.message);
      // Continuamos con la sincronización usando solo los datos disponibles en la conversación
    }
    
    // Si no encontramos una imagen, usamos la que ya tiene la conversación (si existe)
    if (!imageUrl) {
      imageUrl = conversation.profilePictureUrl || conversation.participantPicture || 
                (conversation.participant && conversation.participant.profilePic);
      
      if (!imageUrl) {
        console.log('ℹ️ No se encontraron actualizaciones de imágenes de perfil para este attendeeId');
        return;
      }
    }
    
    // 3. Actualizar la conversación con la URL de la imagen si la encontramos
    try {
      // Asegurarnos de que participant sea un objeto
      const participant = conversation.participant || {};
      
      await update(conversationRef, {
        profilePictureUrl: imageUrl,
        participantPicture: imageUrl,
        participant: {
          ...participant,
          profilePic: imageUrl
        }
      });
      console.log(`✅ Conversación ${conversationId} actualizada con imagen de perfil`);
    } catch (error: any) {
      console.error(`❌ Error al actualizar la conversación ${conversationId}:`, error.message);
    }
  } catch (error: any) {
    console.error('❌ Error al sincronizar imagen de perfil para conversación:', error.message);
    // No relanzamos el error para evitar que la aplicación se detenga
  }
}
