import { createNotification, createNotificationInFirestore } from './notifications';
import { sendEmail } from './email';
import { NotificationType } from '../types/notifications';
import { db } from '@/lib/firebase/config';
import { doc, getDoc, collection, query, where, getDocs } from 'firebase/firestore';
import { ref, get, DataSnapshot } from 'firebase/database';
import { database } from '@/lib/firebase/config';
import admin from 'firebase-admin';
import { db as adminDb } from '@/lib/firebase-admin';

export interface NotificationData {
  clientName?: string;
  tattooSize?: string;
  tattooDesign?: string;
  tattooPlacement?: string;
  tattooStyle?: string;
  additionalNotes?: string;
  conversationId: string;
  clientId?: string;
  pageId?: string;
  [key: string]: any;
}

// Interface para actualizar nombre de contacto
export interface UpdateContactNameData {
  contactId: string;
  conversationId: string;
  newName: string;
}

// Enum para las herramientas disponibles para la IA
export enum AI_TOOLS {
  NOTIFY_POTENTIAL_CLIENT = "notify_potential_client",
  UPDATE_CONTACT_NAME = "update_contact_name"
}

/**
 * Notifica al tatuador sobre un cliente potencial identificado por la IA
 */
export async function notifyPotentialClient(data: NotificationData): Promise<{ success: boolean; message: string }> {
  try {
    console.log('📣 Iniciando notificación directa al tatuador:', JSON.stringify(data, null, 2));
    
    // Primero intentar buscar el usuario por channelId si hay un pageId
    let success = false;
    if (data.pageId) {
      console.log('Intentando buscar usuario por pageId/channelId:', data.pageId);
      success = await buscarUsuarioPorChannelId(data.pageId, data);
    }
    
    // Si no se encontró por channelId o no hay pageId, usar método alternativo
    if (!success) {
      console.log('No se encontró usuario por pageId o no hay pageId. Usando método alternativo con conversationId');
      success = await buscarUsuarioPorConversationId(data);
    }
    
    // Retornar resultado
    if (success) {
      console.log('Notificación enviada correctamente');
      return { 
        success: true, 
        message: 'Notification sent successfully' 
      };
    } else {
      console.error('No se pudo enviar la notificación al tatuador');
      return { 
        success: false, 
        message: 'Could not send notification to tattoo artist' 
      };
    }
  } catch (error) {
    console.error('Error in notifyPotentialClient:', error);
    return { 
      success: false, 
      message: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

// Actualiza el nombre de un contacto que era 'Unknown'
export async function updateContactName(data: UpdateContactNameData): Promise<{ success: boolean; message: string }> {
  try {
    console.log('🔄 Actualizando nombre del contacto directamente:', data);
    
    // Implementamos directamente la lógica para actualizar el nombre
    if (!data.contactId || !data.conversationId || !data.newName) {
      throw new Error('Faltan datos necesarios para actualizar el nombre del contacto');
    }
    
    // Actualizar nombre en Sinch
    try {
      // Buscar los datos del usuario/tatuador primero
      const conversationsRef = admin.firestore().collection('conversations');
      const conversationQuery = await conversationsRef
        .where('externalId', '==', data.conversationId)
        .limit(1)
        .get();
      
      if (conversationQuery.empty) {
        throw new Error('No se encontró la conversación en Firestore');
      }
      
      const conversationDoc = conversationQuery.docs[0];
      const conversationData = conversationDoc.data();
      const userId = conversationData.userId;
      
      if (!userId) {
        throw new Error('No se pudo determinar el userId para la conversación');
      }
      
      // Obtener credenciales de Sinch del usuario
      const userDoc = await admin.firestore().collection('users').doc(userId).get();
      if (!userDoc.exists) {
        throw new Error('No se encontró el usuario en Firestore');
      }
      
      const userData = userDoc.data() || {};
      const credentials = {
        sinchProjectId: userData.sinchProjectId || '',
        sinchClientId: userData.sinchClientId || '',
        sinchClientSecret: userData.sinchClientSecret || '',
        sinchAppId: userData.sinchAppId || conversationData?.appId || ''
      };
      
      // Obtener información del canal
      const channelData = await admin.firestore().collection('contacts')
        .where('externalId', '==', data.contactId)
        .limit(1)
        .get();
      
      if (channelData.empty) {
        throw new Error('No se encontró información del contacto en Firestore');
      }
      
      const contactDoc = channelData.docs[0];
      const contactData = contactDoc.data();
      const channel = contactData.channel || 'INSTAGRAM';
      const identity = contactData.identity || '';
      
      // Actualizar nombre en Sinch
      // Esta parte generalmente requiere una llamada a la API de Sinch
      // Para propósitos de este código, asumimos que ya está implementado en otro lugar
      // y solo registramos que se intentó actualizar
      console.log('Actualizando nombre en Sinch:', {
        contactId: data.contactId,
        newName: data.newName,
        credentials,
        channel,
        identity
      });
      
      // Actualizar en nuestra base de datos
      await admin.firestore().collection('contacts').doc(contactDoc.id).update({
        displayName: data.newName
      });
      
      // También actualizar en RTDB si existe
      const rtdb = admin.database();
      const rtdbPath = `contacts/${userId}/${data.contactId}`;
      await rtdb.ref(rtdbPath).update({
        displayName: data.newName
      });
      
      console.log('✅ Nombre actualizado correctamente en bases de datos');
      
      return { 
        success: true, 
        message: `Nombre actualizado correctamente a: ${data.newName}` 
      };
    } catch (updateError) {
      console.error('❌ Error actualizando nombre:', updateError);
      throw updateError;
    }
  } catch (error) {
    console.error('❌ Error en updateContactName:', error);
    return { 
      success: false, 
      message: error instanceof Error ? error.message : 'Error desconocido' 
    };
  }
}

// Función para buscar usuario por channelId
async function buscarUsuarioPorChannelId(channelId: string | null | undefined, data: NotificationData): Promise<boolean> {
  try {
    // Validar que el channelId no sea null o undefined antes de proceder
    if (!channelId) {
      console.error('channelId es null o undefined');
      return false;
    }
    
    console.log('Buscando usuario por channelId:', channelId);
    
    // Buscar en la colección 'users' los usuarios que tienen este channelId
    const usersRef = collection(db, 'users');
    const userDocs = await getDocs(usersRef);
    
    console.log(`Se encontraron ${userDocs.docs.length} usuarios para revisar`);
    
    // Variables para almacenar la información del tatuador
    let tattooArtistId = null;
    let tattooArtistEmail = null;
    let tattooArtistName = 'Tatuador';
    let userFound = false;
    
    for (const userDoc of userDocs.docs) {
      const userData = userDoc.data();
      
      // Verificar si el usuario tiene canales de Superchat
      if (userData.superchatChannels && Array.isArray(userData.superchatChannels)) {
        console.log(`Usuario ${userDoc.id} tiene ${userData.superchatChannels.length} canales de Superchat`);
        
        // Buscar si alguno de los canales coincide con el channelId
        const matchingChannel = userData.superchatChannels.find(
          (channel: any) => channel.id === channelId
        );
        
        if (matchingChannel) {
          // Encontramos el usuario dueño del canal
          tattooArtistId = userDoc.id;
          tattooArtistEmail = userData.email;
          tattooArtistName = userData.name || userData.firstName || userData.displayName || 'Tatuador';
          userFound = true;
          console.log(`Usuario encontrado para el canal ${channelId}: ${tattooArtistName} (${tattooArtistEmail})`);
          break;
        }
      }
    }
    
    // Si no encontramos un usuario específico para este canal, no usar respaldo automático
    if (!userFound) {
      console.log('No se encontró usuario específico para el canal. No se usará un usuario por defecto.');
      
      // Si tenemos un conversationId, intentar buscar el propietario por ese método
      if (data.conversationId) {
        console.log('Intentando buscar por conversationId como alternativa...');
        return await buscarUsuarioPorConversationId(data);
      }
      
      // Si no tenemos conversationId, no podemos continuar
      console.log('No se encontró usuario para el canal y no hay conversationId para buscar alternativas');
      return false;
    }
    
    // Si no se encontró un email para el tatuador, no continuamos
    if (!tattooArtistEmail) {
      console.error('No se encontró el email del tatuador, no se puede enviar notificación');
      return false;
    }

    // Verificamos que tengamos el ID del tatuador para guardar la notificación
    if (!tattooArtistId) {
      console.error('No se encontró el ID del tatuador, no se puede guardar notificación');
      return false;
    }

    // Obtener el nombre real del usuario si existe un clientId
    let clientDisplayName = data.clientName || 'Cliente';
    
    // Si tenemos un clientId, intentar obtener el nombre de usuario de la base de datos
    if (data.clientId) {
      try {
        console.log(`Buscando información del cliente con ID: ${data.clientId}`);
        const clientDoc = await admin.firestore().collection('users').doc(data.clientId).get();
        
        if (clientDoc.exists) {
          const clientData = clientDoc.data();
          // Usar el nombre de usuario si existe
          if (clientData?.displayName || clientData?.name || clientData?.username) {
            clientDisplayName = clientData.displayName || clientData.name || clientData.username;
            console.log(`Usando nombre de usuario real: ${clientDisplayName}`);
          }
        } else {
          console.log(`No se encontró información del cliente con ID: ${data.clientId}`);
        }
      } catch (clientError) {
        console.error('Error buscando información del cliente:', clientError);
      }
    }

    // Mensaje para la notificación
    const message = `${clientDisplayName} está interesado en un tatuaje de ${data.tattooDesign} en ${data.tattooPlacement}`;
    
    // Guardar una notificación en Firebase
    console.log(`Guardando notificación para el usuario ${tattooArtistId}`);
    
    // Verificar que tattooArtistId no sea null
    if (!tattooArtistId) {
      console.error('tattooArtistId es null o undefined, no se puede guardar notificación');
      return false;
    }
    
    try {
      await createNotification(
        tattooArtistId,
        NotificationType.AI_CLIENT_MATCH,
        '¡Nuevo cliente potencial!',
        message,
        data
      );
      console.log('Notificación guardada correctamente');
    } catch (notificationError) {
      console.error('Error al guardar notificación en RTDB, intentando Firestore:', notificationError);
      // Intentar con Firestore directamente
      try {
        await createNotificationInFirestore(
          tattooArtistId,
          NotificationType.AI_CLIENT_MATCH,
          '¡Nuevo cliente potencial!',
          message,
          data
        );
        console.log('Notificación guardada correctamente en Firestore');
      } catch (firestoreError) {
        console.error('Error al guardar notificación en Firestore:', firestoreError);
        // Continuar de todos modos para enviar el email
      }
    }

    // Enviar un email al tatuador con los detalles del cliente potencial
    const emailContent = createHtmlContent({
      title: '¡Nuevo cliente potencial detectado!',
      content: `
        <p>Hola ${tattooArtistName},</p>
        <p>La IA ha identificado un nuevo cliente potencial en tus conversaciones de Superchat.</p>
        <h3>Detalles del cliente:</h3>
        <ul>
          <li><strong>Nombre:</strong> ${clientDisplayName}</li>
          <li><strong>Diseño:</strong> ${data.tattooDesign}</li>
          <li><strong>Tamaño:</strong> ${data.tattooSize}</li>
          <li><strong>Ubicación:</strong> ${data.tattooPlacement}</li>
          <li><strong>Estilo:</strong> ${data.tattooStyle}</li>
          ${data.additionalNotes ? `<li><strong>Notas adicionales:</strong> ${data.additionalNotes}</li>` : ''}
        </ul>
        <p>Puedes revisar la conversación completa en la aplicación.</p>
      `,
      actionText: 'Ver conversación',
      actionUrl: `/app/messages?conversation=${data.conversationId}`
    });

    console.log(`Enviando correo a ${tattooArtistEmail}`);
    await sendEmail({
      to: tattooArtistEmail,
      subject: '¡Nuevo cliente potencial detectado!',
      html: emailContent
    });
    
    console.log(`Correo enviado a: ${tattooArtistEmail}`);

    return true;
  } catch (error) {
    console.error('Error buscando usuario por channelId:', error);
    return false;
  }
}

// Método de respaldo para buscar por conversationId directamente
async function buscarUsuarioPorConversationId(data: NotificationData): Promise<boolean> {
  try {
    console.log(`Buscando usuario para conversación: ${data.conversationId}`);
    
    // Variables para el resultado
    let tattooArtistId: string | null = null;
    let tattooArtistEmail: string | null = null;
    let tattooArtistName = 'Tatuador';
    
    // Primero intentar en Firestore con externalId
    const conversationsRef = admin.firestore().collection('conversations');
    
    // Intentar primero con externalId
    let conversationQuery = await conversationsRef
      .where('externalId', '==', data.conversationId)
      .limit(1)
      .get();
    
    // Si no hay resultados, intentar con channelConversationId
    if (conversationQuery.empty) {
      console.log('No se encontró la conversación con externalId, intentando con channelConversationId');
      conversationQuery = await conversationsRef
        .where('channelConversationId', '==', data.conversationId)
        .limit(1)
        .get();
    }
    
    if (!conversationQuery.empty) {
      const conversationDoc = conversationQuery.docs[0];
      const conversationData = conversationDoc.data();
      tattooArtistId = conversationData.userId;
      
      console.log(`Se encontró conversación en Firestore, pertenece al usuario: ${tattooArtistId}`);
      
      // Obtener información del usuario/tatuador
      if (tattooArtistId) {
        const userDoc = await admin.firestore().collection('users').doc(tattooArtistId).get();
        
        if (userDoc.exists) {
          const userData = userDoc.data();
          tattooArtistEmail = userData?.email || null;
          tattooArtistName = userData?.displayName || userData?.name || 'Tatuador';
          
          console.log(`Información del tatuador obtenida: ${tattooArtistName} / ${tattooArtistEmail}`);
        } else {
          console.log(`No se encontró información del usuario: ${tattooArtistId}`);
        }
      }
    } else {
      // Si no se encuentra en Firestore, intentar en Realtime Database
      console.log('No se encontró la conversación en Firestore, intentando en RTDB');
      
      try {
        // Buscar en todas las conversaciones de RTDB
        const rtdb = admin.database();
        const conversationsRef = rtdb.ref('conversations');
        const snapshot = await conversationsRef.get();
        
        if (snapshot.exists()) {
          let found = false;
          
          // Iterar por los usuarios
          snapshot.forEach((userSnapshot: admin.database.DataSnapshot) => {
            if (found) return; // Ya encontramos lo que buscábamos
            
            const userId = userSnapshot.key;
            
            // Iterar por las conversaciones del usuario
            userSnapshot.forEach((convSnapshot: admin.database.DataSnapshot) => {
              if (found) return; // Ya encontramos lo que buscábamos
              
              const convId = convSnapshot.key;
              const convData = convSnapshot.val();
              
              // Verificar si es la conversación que buscamos
              if (convId === data.conversationId || convData.id === data.conversationId) {
                tattooArtistId = userId;
                found = true;
                console.log(`Se encontró la conversación en RTDB, pertenece al usuario: ${tattooArtistId}`);
                return;
              }
            });
          });
          
          if (!found) {
            console.log('No se encontró la conversación en RTDB');
          }
        } else {
          console.log('No hay datos de conversaciones en RTDB');
        }
        
        // Si encontramos el ID del usuario, intentar obtener más información
        if (tattooArtistId) {
          const userDoc = await admin.firestore().collection('users').doc(tattooArtistId).get();
          
          if (userDoc.exists) {
            const userData = userDoc.data();
            tattooArtistEmail = userData?.email || null;
            tattooArtistName = userData?.displayName || userData?.name || 'Tatuador';
            
            console.log(`Información del tatuador obtenida: ${tattooArtistName} / ${tattooArtistEmail}`);
          } else {
            console.log(`No se encontró información del usuario: ${tattooArtistId}`);
          }
        }
      } catch (rtdbError) {
        console.error('Error buscando en RTDB:', rtdbError);
      }
    }
    
    // Si no encontramos un tatuador, no podemos continuar
    if (!tattooArtistId) {
      console.error('No se pudo determinar el tatuador para la conversación');
      return false;
    }
    
    // Obtener el nombre real del usuario si existe un clientId
    let clientDisplayName = data.clientName || 'Cliente';
    
    // Si tenemos un clientId, intentar obtener el nombre de usuario de la base de datos
    if (data.clientId) {
      try {
        console.log(`Buscando información del cliente con ID: ${data.clientId}`);
        const clientDoc = await admin.firestore().collection('users').doc(data.clientId).get();
        
        if (clientDoc.exists) {
          const clientData = clientDoc.data();
          // Usar el nombre de usuario si existe
          if (clientData?.displayName || clientData?.name || clientData?.username) {
            clientDisplayName = clientData.displayName || clientData.name || clientData.username;
            console.log(`Usando nombre de usuario real: ${clientDisplayName}`);
          }
        } else {
          console.log(`No se encontró información del cliente con ID: ${data.clientId}`);
        }
      } catch (clientError) {
        console.error('Error buscando información del cliente:', clientError);
      }
    }
    
    // Crear el mensaje para la notificación
    const message = `${clientDisplayName} está interesado en un tatuaje de ${data.tattooDesign} en ${data.tattooPlacement}`;
    
    // Guardar una notificación en Firebase
    console.log(`Guardando notificación para el usuario ${tattooArtistId}`);
    
    // Verificar que tattooArtistId no sea null
    if (!tattooArtistId) {
      console.error('tattooArtistId es null o undefined, no se puede guardar notificación');
      return false;
    }
    
    try {
      await createNotification(
        tattooArtistId,
        NotificationType.AI_CLIENT_MATCH,
        '¡Nuevo cliente potencial!',
        message,
        data
      );
      console.log('Notificación guardada correctamente');
    } catch (notificationError) {
      console.error('Error al guardar notificación en RTDB, intentando Firestore:', notificationError);
      // Intentar con Firestore directamente
      try {
        await createNotificationInFirestore(
          tattooArtistId,
          NotificationType.AI_CLIENT_MATCH,
          '¡Nuevo cliente potencial!',
          message,
          data
        );
        console.log('Notificación guardada correctamente en Firestore');
      } catch (firestoreError) {
        console.error('Error al guardar notificación en Firestore:', firestoreError);
        // Continuar de todos modos para enviar el email
      }
    }

    // Enviar un email al tatuador con los detalles del cliente potencial
    const emailContent = createHtmlContent({
      title: '¡Nuevo cliente potencial detectado!',
      content: `
        <p>Hola ${tattooArtistName},</p>
        <p>La IA ha identificado un nuevo cliente potencial en tus conversaciones.</p>
        <h3>Detalles del cliente:</h3>
        <ul>
          <li><strong>Nombre:</strong> ${clientDisplayName}</li>
          <li><strong>Diseño:</strong> ${data.tattooDesign}</li>
          <li><strong>Tamaño:</strong> ${data.tattooSize}</li>
          <li><strong>Ubicación:</strong> ${data.tattooPlacement}</li>
          <li><strong>Estilo:</strong> ${data.tattooStyle}</li>
          ${data.additionalNotes ? `<li><strong>Notas adicionales:</strong> ${data.additionalNotes}</li>` : ''}
        </ul>
        <p>Puedes revisar la conversación completa en la aplicación.</p>
      `,
      actionText: 'Ver conversación',
      actionUrl: `/app/messages?conversation=${data.conversationId}`
    });

    // Verificar que tengamos un email válido
    if (!tattooArtistEmail) {
      console.error('No se tiene un email válido para enviar la notificación');
      return false;
    }
    
    console.log(`Enviando correo a ${tattooArtistEmail}`);
    await sendEmail({
      to: tattooArtistEmail, // Ya verificamos que no es null
      subject: '¡Nuevo cliente potencial detectado!',
      html: emailContent 
    });
    
    console.log(`Correo enviado a: ${tattooArtistEmail}`);

    return true;
  } catch (error) {
    console.error('Error usando método de respaldo:', error);
    return false;
  }
}

// Implementación temporal si el módulo no está disponible
function createHtmlContent(data: any): string {
  return `
    <h1>${data.title}</h1>
    ${data.content}
    <p><a href="${data.actionUrl}">${data.actionText}</a></p>
  `;
} 