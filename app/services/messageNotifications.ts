/**
 * Servicio para manejar notificaciones de mensajes
 * Incluye sonido y actualización del título de la pestaña
 */

let originalTitle = '';
let unreadCount = 0;
let audioContext: AudioContext | null = null;
let notificationSound: AudioBuffer | null = null;
let contextIsResumed = false;

/**
 * Inicializa el servicio de notificaciones de mensajes
 */
export const initMessageNotifications = () => {
  try {
    if (typeof window !== 'undefined') {
      originalTitle = document.title;
      
      // Crear un contexto de audio para reproducir sonidos
      if (!audioContext) {
        const AudioContextClass = window.AudioContext || ((window as any).webkitAudioContext);
        if (AudioContextClass) {
          audioContext = new AudioContextClass();
          
          // Verificar el estado inicial del contexto
          if (audioContext.state === 'suspended') {
            console.log('⚠️ AudioContext está en estado suspended, se necesita interacción del usuario');
            
            // Configurar listeners para eventos de usuario
            setupUserInteractionListeners();
          } else {
            contextIsResumed = true;
          }
        }
      }
      
      // Cargar el sonido de notificación
      loadNotificationSound();
      
      console.log('✅ Servicio de notificaciones de mensajes inicializado');
    }
  } catch (error) {
    console.error('❌ Error al inicializar notificaciones:', error);
  }
};

/**
 * Configurar event listeners para reanudar el contexto de audio
 */
const setupUserInteractionListeners = () => {
  if (typeof window === 'undefined') return;
  
  // Eventos de usuario que pueden reanudar el contexto de audio
  const userEvents = ['click', 'touchstart', 'keydown'];
  
  const resumeAudioContext = async () => {
    if (audioContext && audioContext.state === 'suspended') {
      try {
        await audioContext.resume();
        contextIsResumed = true;
        console.log('✅ AudioContext reanudado después de interacción del usuario');
        
        // Una vez que se ha reanudado, podemos eliminar los event listeners
        userEvents.forEach(event => {
          window.removeEventListener(event, resumeAudioContext);
        });
      } catch (error) {
        console.error('❌ Error al reanudar AudioContext:', error);
      }
    }
  };
  
  // Agregar listeners para cada evento de usuario
  userEvents.forEach(event => {
    window.addEventListener(event, resumeAudioContext, { once: false });
  });
};

/**
 * Carga el sonido de notificación
 */
const loadNotificationSound = async () => {
  try {
    if (!audioContext) return;
    
    // Cargar el archivo de sonido
    try {
      const response = await fetch('/sounds/system-notification-02-352442.mp3');
      const arrayBuffer = await response.arrayBuffer();
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
      notificationSound = audioBuffer;
      console.log('✅ Sonido de notificación cargado correctamente');
    } catch (error) {
      console.error('❌ Error al cargar el sonido de notificación:', error);
    }
  } catch (error) {
    console.error('Error cargando sonido de notificación:', error);
  }
};

/**
 * Reproduce el sonido de notificación
 */
export const playNotificationSound = async () => {
  try {
    if (!notificationSound) {
      console.log('⚠️ No se puede reproducir el sonido: notificationSound no está disponible');
      await loadNotificationSound(); // Intentar cargar el sonido si no está disponible
      if (!notificationSound) {
        console.error('❌ No se pudo cargar el sonido de notificación');
        return;
      }
    }
    
    if (!audioContext) {
      console.log('⚠️ No se puede reproducir el sonido: audioContext no está disponible');
      // Intentar crear un nuevo contexto de audio
      try {
        const AudioContextClass = window.AudioContext || ((window as any).webkitAudioContext);
        if (AudioContextClass) {
          audioContext = new AudioContextClass();
          console.log('✅ Nuevo AudioContext creado');
        } else {
          console.error('❌ AudioContext no es compatible con este navegador');
          return;
        }
      } catch (error) {
        console.error('❌ Error al crear AudioContext:', error);
        return;
      }
    }
    
    // Verificar si el contexto está suspendido y tratar de reanudarlo
    if (audioContext.state === 'suspended') {
      try {
        console.log('⚠️ AudioContext está suspendido, intentando reanudar...');
        await audioContext.resume();
        contextIsResumed = true;
        console.log('✅ AudioContext reanudado para reproducir la notificación');
      } catch (resumeError) {
        console.error('❌ Error al reanudar AudioContext:', resumeError);
        
        // Intento alternativo: reproducir un elemento de audio HTML
        try {
          console.log('⚠️ Intentando reproducir con elemento HTML Audio como alternativa...');
          const audio = new Audio('/sounds/system-notification-02-352442.mp3');
          audio.volume = 0.5;
          await audio.play();
          console.log('✅ Sonido reproducido con elemento HTML Audio');
          return;
        } catch (audioError) {
          console.error('❌ Error al reproducir con HTML Audio:', audioError);
          return;
        }
      }
    }
    
    // Si llegamos aquí, el contexto debe estar activo
    try {
      const source = audioContext.createBufferSource();
      source.buffer = notificationSound;
      source.connect(audioContext.destination);
      source.start(0);
      console.log('✅ Sonido de notificación reproducido con Web Audio API');
    } catch (playError) {
      console.error('❌ Error al reproducir con Web Audio API:', playError);
      
      // Intento alternativo si falla Web Audio API
      try {
        console.log('⚠️ Intentando reproducir con elemento HTML Audio como fallback...');
        const audio = new Audio('/sounds/system-notification-02-352442.mp3');
        audio.volume = 0.5;
        await audio.play();
        console.log('✅ Sonido reproducido con elemento HTML Audio (fallback)');
      } catch (audioError) {
        console.error('❌ Error al reproducir con HTML Audio (fallback):', audioError);
      }
    }
  } catch (error) {
    console.error('❌ Error general al reproducir sonido de notificación:', error);
  }
};

/**
 * Actualiza el título de la página con el contador de mensajes no leídos
 * @param count Número de mensajes no leídos
 */
export const updatePageTitle = (count: number) => {
  unreadCount = count;
  
  if (count > 0) {
    document.title = `(${count}) ${originalTitle}`;
  } else {
    document.title = originalTitle;
  }
};

/**
 * Notifica sobre un nuevo mensaje
 * Reproduce sonido y actualiza el título
 * @param count Número total de mensajes no leídos
 * @param increment Si es true, incrementa el contador en lugar de establecerlo
 */
export const notifyNewMessage = async (count?: number, increment: boolean = true) => {
  try {
    // Reproducir sonido (ahora es una promesa)
    await playNotificationSound();
    console.log('✅ Notificación de mensaje completada');
  } catch (error) {
    console.error('❌ Error en la notificación de sonido:', error);
  } finally {
    // Actualizar título (siempre hacemos esto incluso si el sonido falla)
    if (count !== undefined) {
      updatePageTitle(count);
    } else if (increment) {
      updatePageTitle(unreadCount + 1);
    }
  }
};

/**
 * Resetea las notificaciones
 * Restaura el título original
 */
export const resetNotifications = () => {
  unreadCount = 0;
  document.title = originalTitle;
};
