import { MetadataRoute } from 'next';
import { getFirestore } from 'firebase-admin/firestore';
import { getApp, initializeApp } from 'firebase-admin/app';
import { db as adminDb } from '@/lib/firebase/firebase-admin';

// Define tu URL base
const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://tatu.ink';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  // Rutas estáticas principales
  const staticRoutes = [
    '',
    '/politicas-de-privacidad',
    '/terminos-de-servicio',
    '/politica-de-reembolso',
    '/exito',
    '/fallido',
  ].map(route => ({
    url: `${baseUrl}${route}`,
    lastModified: new Date(),
    changeFrequency: 'monthly' as 'monthly',
    priority: route === '' ? 1 : 0.8,
  }));

  // Obtener perfiles de tatuadores desde Firestore
  let userProfiles: { username: string; updatedAt?: Date }[] = [];
  
  try {
    // Usar la instancia de Firestore ya inicializada
    const db = adminDb;
    
    const usersSnapshot = await db.collection('users')
      .where('role', '==', 'artist') // Asumiendo que los tatuadores tienen role='artist'
      .where('isActive', '==', true) // Solo perfiles activos
      .get();
    
    userProfiles = usersSnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        username: data.username || doc.id,
        updatedAt: data.updatedAt ? new Date(data.updatedAt.toDate()) : new Date(),
      };
    });
  } catch (error) {
    console.error('Error al obtener perfiles para el sitemap:', error);
    // Continuar con un array vacío si hay error
  }

  // Crear rutas dinámicas para perfiles de tatuadores
  const dynamicRoutes = userProfiles.map(profile => ({
    url: `${baseUrl}/${profile.username}`,
    lastModified: profile.updatedAt || new Date(),
    changeFrequency: 'weekly' as 'weekly',
    priority: 0.9,
  }));

  return [
    ...staticRoutes,
    ...dynamicRoutes,
  ];
}
