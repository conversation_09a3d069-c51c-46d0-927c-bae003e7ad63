import { database as rtdb } from '@/app/firebase/config'
import { ref, get, query as rtdbQuery, orderByChild, equalTo, push, update } from 'firebase/database'
import { addDays, addHours, addMinutes, addMonths, addWeeks, isBefore, isAfter } from 'date-fns'

interface Automation {
  id: string
  userId: string
  name: string
  phaseId: string
  trigger: string
  timing: {
    type: 'before' | 'after' | 'during' | 'specific'
    value: number
    unit: 'minutes' | 'hours' | 'days' | 'weeks' | 'months'
    specificDate?: string  // Formato YYYY-MM-DD
    specificTime?: string  // Formato HH:MM
  }
  messageTemplate: string
  isActive: boolean
  channel: string
  lastProcessed?: number
}

interface Appointment {
  id: string
  date: string
  clientId: string
  clientName: string
  artistId: string
  status: string
}

interface MetaConnection {
  pageId: string
  pageAccessToken: string
  userId: string
}

async function getActiveAutomations(): Promise<Automation[]> {
  const automationsRef = ref(rtdb, 'automations')
  const snapshot = await get(automationsRef)
  if (!snapshot.exists()) return []
  
  const automations: Automation[] = []
  snapshot.forEach((userSnapshot) => {
    userSnapshot.forEach((automationSnapshot) => {
      const automation = automationSnapshot.val()
      if (automation.isActive) {
        automations.push({
          id: automationSnapshot.key!,
          ...automation
        })
      }
    })
  })
  return automations
}

async function getRelevantAppointments(automation: Automation): Promise<Appointment[]> {
  // Manejar el caso especial para tipo 'specific'
  if (automation.timing.type === 'specific') {
    // Verificar si la fecha y hora actual coinciden con la fecha y hora específica
    const now = new Date();
    const today = now.toISOString().split('T')[0]; // YYYY-MM-DD
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    
    // Extraer la hora y minuto configurados
    const specificDate = automation.timing.specificDate || '';
    const specificTime = automation.timing.specificTime || '';
    const [specHour, specMinute] = specificTime.split(':').map(Number);
    
    // Verificar si coincide la fecha
    if (today === specificDate) {
      // Verificar si coincide la hora (con un margen de 5 minutos para el cron)
      const hourMatches = currentHour === specHour;
      const minuteMatches = Math.abs(currentMinute - specMinute) <= 5; // Margen de 5 minutos
      
      if (hourMatches && minuteMatches) {
        // Para tipo specific, obtenemos todas las citas recientes para tener clientes
        const appointmentsRef = ref(rtdb, `appointments/${automation.userId}`)
        const snapshot = await get(appointmentsRef)
        if (!snapshot.exists()) return []
        
        const clientMap = new Map(); // Para evitar duplicados
        
        snapshot.forEach((appointmentSnapshot) => {
          const appointment = appointmentSnapshot.val()
          // Solo incluir cada cliente una vez usando su ID como clave
          if (!clientMap.has(appointment.clientId)) {
            clientMap.set(appointment.clientId, {
              id: appointmentSnapshot.key!,
              ...appointment
            });
          }
        });
        
        return Array.from(clientMap.values());
      }
    }
    return []; // No es el momento de ejecutar esta automatización
  }
  
  // Código existente para los otros tipos
  const appointmentsRef = ref(rtdb, `appointments/${automation.userId}`)
  const snapshot = await get(appointmentsRef)
  if (!snapshot.exists()) return []
  
  const now = new Date()
  const appointments: Appointment[] = []
  
  snapshot.forEach((appointmentSnapshot) => {
    const appointment = appointmentSnapshot.val()
    const appointmentDate = new Date(appointment.date)
    let targetDate: Date

    // Calcular la fecha objetivo según el timing
    switch (automation.timing.unit) {
      case 'minutes':
        targetDate = addMinutes(appointmentDate, automation.timing.type === 'before' ? -automation.timing.value : automation.timing.value)
        break
      case 'hours':
        targetDate = addHours(appointmentDate, automation.timing.type === 'before' ? -automation.timing.value : automation.timing.value)
        break
      case 'days':
        targetDate = addDays(appointmentDate, automation.timing.type === 'before' ? -automation.timing.value : automation.timing.value)
        break
      case 'weeks':
        targetDate = addWeeks(appointmentDate, automation.timing.type === 'before' ? -automation.timing.value : automation.timing.value)
        break
      case 'months':
        targetDate = addMonths(appointmentDate, automation.timing.type === 'before' ? -automation.timing.value : automation.timing.value)
        break
      default:
        return
    }

    // Verificar si la cita es relevante
    let isRelevant = false
    switch (automation.timing.type) {
      case 'before':
        isRelevant = isAfter(now, targetDate) && isBefore(now, appointmentDate)
        break
      case 'after':
        isRelevant = isAfter(now, appointmentDate) && isBefore(now, targetDate)
        break
      case 'during':
        isRelevant = isBefore(now, addDays(appointmentDate, 1)) && isAfter(now, appointmentDate)
        break
    }

    if (isRelevant) {
      appointments.push({
        id: appointmentSnapshot.key!,
        ...appointment
      })
    }
  })

  return appointments
}

async function getMetaConnection(userId: string): Promise<MetaConnection | null> {
  const metaConnectionsRef = ref(rtdb, `metaConnections/${userId}`)
  const snapshot = await get(metaConnectionsRef)
  if (!snapshot.exists()) return null
  return snapshot.val() as MetaConnection
}

async function sendMessage(automation: Automation, appointment: Appointment, metaConnection: MetaConnection) {
  try {
    // Formatear el mensaje con los datos del cliente
    const message = automation.messageTemplate.replace(/{{([^}]+)}}/g, (match, variable) => {
      const mappings: { [key: string]: string } = {
        client_name: appointment.clientName || 'Cliente',
        appointment_time: new Date(appointment.date).toLocaleTimeString(),
        studio_name: 'Estudio', // TODO: Obtener del perfil del artista
        artist_name: 'Artista'  // TODO: Obtener del perfil del artista
      }
      return mappings[variable.trim()] || match
    })

    // Extraer el ID del recipiente del ID de la conversación
    const [pageId, recipientId] = appointment.clientId.split('_')
    
    if (!recipientId) {
      throw new Error('No se pudo extraer el ID del destinatario de la conversación')
    }

    // Enviar mensaje a través de la API
    const response = await fetch('/api/webhook/meta/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        recipientId,
        pageId: metaConnection.pageId,
        message,
        userId: automation.userId,
        attachments: [],
        tag: 'CONFIRMED_EVENT_UPDATE'
      })
    })

    if (!response.ok) {
      throw new Error('Error al enviar mensaje a Meta')
    }

    const responseData = await response.json()

    // Guardar el mensaje en RTDB
    const timestamp = Date.now()
    const messageRef = ref(rtdb, `messages/${appointment.clientId}`)
    await push(messageRef, {
      content: message,
      timestamp,
      sender: 'business',
      read: true,
      messageId: responseData.message_id,
      attachments: []
    })

    // Actualizar la conversación
    const conversationRef = ref(rtdb, `conversations/${automation.userId}/${appointment.clientId}`)
    await update(conversationRef, {
      lastMessage: {
        content: message,
        timestamp,
        sender: 'business'
      },
      lastMessageAt: timestamp,
      unread: false,
      unreadCount: 0
    })

    // Actualizar lastProcessed de la automatización
    const automationRef = ref(rtdb, `automations/${automation.userId}/${automation.id}`)
    await update(automationRef, {
      lastProcessed: timestamp
    })

    return true
  } catch (error) {
    console.error('Error sending message:', error)
    return false
  }
}

export async function processAutomations() {
  try {
    // 1. Obtener todas las automatizaciones activas
    const automations = await getActiveAutomations()

    for (const automation of automations) {
      // 2. Obtener la conexión de Meta para este artista
      const metaConnection = await getMetaConnection(automation.userId)
      if (!metaConnection) {
        console.log(`No Meta connection found for user ${automation.userId}`)
        continue
      }

      // 3. Obtener las citas relevantes para esta automatización
      const appointments = await getRelevantAppointments(automation)

      // 4. Enviar mensajes para cada cita relevante
      for (const appointment of appointments) {
        const success = await sendMessage(automation, appointment, metaConnection)
        if (success) {
          console.log(`Message sent successfully for appointment ${appointment.id}`)
        } else {
          console.error(`Failed to send message for appointment ${appointment.id}`)
        }
      }
    }
  } catch (error) {
    console.error('Error processing automations:', error)
    throw error
  }
}
