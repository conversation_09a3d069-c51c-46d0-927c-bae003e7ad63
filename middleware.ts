import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Rutas para acceso público y de aplicación
const PUBLIC_ROUTES = [
  '/',
  '/login',
  '/register',
  '/reset-password',
  '/forgot-password',
  '/payment',
  '/exito',
  '/fallido',
  '/terminos-de-servicio',
  '/politicas-de-privacidad',
  '/404',
  '/500'
];

const APP_ROUTES = [
  '/dashboard',
  '/clients',
  '/appointments',
  '/appointments/calendar',
  '/calendar',
  '/studio',
  '/finances',
  '/messages',
  '/settings',
  '/site',
  '/insights',
  '/files',
  '/onboarding',
  '/payment',
  '/exito',
  '/fallido',
  '/automations',
  '/analytics',
  '/links',
  '/customization',
  '/portfolio',
  '/notifications',
  '/reviews',
  '/profile'
];

const LANDING_ROUTES = [
  '/about',
  '/pricing',
  '/contact',
  '/terms',
  '/privacy',
  '/faq',
  '/features',
  '/blog',
  '/site-preview',
  '/terminos-de-servicio',
  '/politicas-de-privacidad',
  '/politica-de-reembolso'
];

// Rutas de API y recursos estáticos que siempre pasan sin verificación
const BYPASS_ROUTES = [
  '/api/',
  '/public/',
  '/images/',
  '/media/',
  '/assets/',
  '/_next/',
  '/favicon.ico',
  '/logo-white.png',
  '/sinch-logo.png',
  '/facebook.png',
  '/whatsapp.png',
  '/tattoo-pattern.png',
  '/ai-pattern.png',
  '/browser-view.png',
  '/mobile-view.png',
  '/fondo-tatu-hero.png',
  '/fondo-features.png',
  '/Hero.mp4',
  '/Hero.gif',
  '/public/Hero.mp4',
  '/public/Hero.gif',
  '/media/hero-video.mp4',
  '/1.png',
  '/2.png',
  '/public/1.png',
  '/public/2.png',
  '/media/testimonials/1.png',
  '/media/testimonials/2.png',
  '/media/trustCompanies/multicolorink.png',
  '/media/trustCompanies/Diseño_sin_título__40_-removebg-preview.png',
  '/media/multicolorink.png',
  // Permitir carga de scripts externos
  'https://forms.app/cdn/embed.js'
];

export function middleware(request: NextRequest) {
  try {
    const { pathname } = request.nextUrl;
    const host = request.headers.get('host') || '';

    console.log(`🔍 [Middleware] Request: ${host}${pathname}`);

    // Detectar si estamos en el subdominio app
    const isAppSubdomain = host.startsWith('app.') || host === 'app.localhost';

    // Extraer el dominio base (sin el subdominio app.)
    const baseDomain = host.replace('app.', '');

    // Permitir que las rutas de API y recursos estáticos pasen directamente
    if (BYPASS_ROUTES.some(route => pathname.startsWith(route) || request.url.includes(route)) || pathname.toLowerCase().endsWith('.gif')) {
      console.log(`✅ [Middleware] Ruta de API/assets/legal: ${pathname}, pasando directamente`);
      return NextResponse.next();
    }

    // Verificar si es una ruta de perfil de usuario
    const segments = pathname.split('/').filter(Boolean);
    const firstSegment = segments[0] || '';

    // Comprobar si el segmento es un nombre de archivo (contiene punto)
    const fileExtensions = ['.png', '.jpg', '.jpeg', '.svg', '.ico', '.mp4', '.webm', '.mov', '.gif'];
    const isLikelyFile = firstSegment.includes('.') ||
                         fileExtensions.some(ext => pathname.toLowerCase().endsWith(ext)) ||
                         pathname.includes('Hero') ||
                         pathname.includes('trustCompanies');

    const isUserProfileRoute =
      pathname !== '/' &&
      segments.length === 1 &&
      !pathname.startsWith('/_') &&
      !pathname.startsWith('/api') &&
      !APP_ROUTES.includes(pathname) &&
      !LANDING_ROUTES.includes(pathname) &&
      !PUBLIC_ROUTES.includes(pathname) &&
      !isLikelyFile;

    console.log(`🔍 [Middleware] Path: ${pathname}, firstSegment: ${firstSegment}, isUserProfile: ${isUserProfileRoute}`);

    // Procesar rutas de perfil de usuario - SIEMPRE EN DOMINIO PRINCIPAL SIN AUTENTICACIÓN
    if (isUserProfileRoute) {
      console.log('🔍 [Middleware] Detectada ruta de perfil de usuario - reescribiendo');
      const url = request.nextUrl.clone();
      url.pathname = '/[username]';
      url.searchParams.set('username', firstSegment);
      return NextResponse.rewrite(url);
    }

    // REGLA 1: Los perfiles de usuario deben estar en el dominio principal
    if (isAppSubdomain && isUserProfileRoute) {
      // Construir URL para el dominio principal
      const protocol = request.nextUrl.protocol;
      const url = new URL(pathname, `${protocol}//${baseDomain}`);
      console.log(`🔄 [Middleware] Redirigiendo app a principal: ${url.toString()}`);
      return NextResponse.redirect(url);
    }

    // REGLA 2: La landing page debe estar en el dominio principal
    if (isAppSubdomain && pathname === '/') {
      // Redirigir al dominio principal
      const protocol = request.nextUrl.protocol;
      const url = new URL('/', `${protocol}//${baseDomain}`);
      console.log(`🔄 [Middleware] Redirigiendo landing a principal: ${url.toString()}`);
      return NextResponse.redirect(url);
    }

    // REGLA 3: Rutas de aplicación deben estar en el subdominio app
    if (!isAppSubdomain && APP_ROUTES.includes(pathname)) {
      // Construir URL para el subdominio app
      const protocol = request.nextUrl.protocol;
      const appDomain = `app.${baseDomain}`;
      const url = new URL(pathname, `${protocol}//${appDomain}`);
      console.log(`🔄 [Middleware] Redirigiendo a app: ${url.toString()}`);
      return NextResponse.redirect(url);
    }

    // REGLA 4: Rutas de autenticación deben estar en el subdominio app
    if (!isAppSubdomain && (pathname === '/login' || pathname === '/register')) {
      // Construir URL para el subdominio app
      const protocol = request.nextUrl.protocol;
      const appDomain = `app.${baseDomain}`;
      const url = new URL(pathname, `${protocol}//${appDomain}`);
      console.log(`🔄 [Middleware] Redirigiendo autenticación a app: ${url.toString()}`);
      return NextResponse.redirect(url);
    }

    // App subdomain specific logic - SOLO PARA RUTAS DE APP, NO PARA PERFILES
    if (isAppSubdomain && pathname === '/') {
      const authCookie = request.cookies.get('auth');
      const redirectUrl = authCookie ? '/studio' : '/login';
      const url = new URL(redirectUrl, request.url);
      console.log(`🔄 [Middleware] Redirigiendo app root a: ${redirectUrl}`);
      return NextResponse.redirect(url);
    }

    console.log(`✅ [Middleware] Ruta normal: ${pathname}, continuando sin redirección`);
    return NextResponse.next();
  } catch (error) {
    console.error('❌ [Middleware] Error:', error);
    // En caso de error, permitir que la solicitud continúe
    return NextResponse.next();
  }
}

// Configurar qué rutas deben pasar por el middleware
export const config = {
  matcher: [
    // Match all paths except static files
    '/((?!_next/static|_next/image|favicon.ico|.*\\.png|.*\\.jpg|.*\\.jpeg|.*\\.svg|.*\\.ico|.*\\.mp4|.*\\.webm|.*\\.mov|.*\\.gif|public/.*|assets/.*|media/.*|media/trustCompanies/.*|Hero.*|hero.*|fondo.*|Hero\\.mp4|Hero\\.gif).*)',
  ],
};